<?php
  $isLocal = str_contains($_SERVER['HTTP_HOST'], 'localhost');

  if ($isLocal) {
    // Whitelisting localhost for CORS
    header("Access-Control-Allow-Origin: http://localhost:3000");
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
      http_response_code(200);
      exit();
    }
  }

include("gsdfw/main/main.php");