<?php
  /**
   * This file logs text to file
   *
   * @package    GSDframework
   * <AUTHOR>
   * @copyright  2006-2020
   * @link       https://www.gsd.nl
   */

  /**
   * @param $error_level
   * @param $error_message
   * @param $error_file
   * @param $error_line
   * @return void
   */
  function errorHandler($error_level, $error_message, $error_file, $error_line) {
    (new GsdExceptionHandler())->errorHandler($error_level, $error_message, $error_file, $error_line);
  }

  /**
   * Will be called when php script ends (normally or abonormally), and logs/sends error if detected.
   * @return void
   */
  function shutdownHandler() {
    (new GsdExceptionHandler())->shutdownHandler();
  }

  function logToFile($level, $msg, $identity = 'unknown', $showDay = true) {
    if ($identity == 'unknown' && isset($_SESSION['userId'])) $identity = $_SESSION['userId'];
    $filename = LOG_FOLDER . $level . ($showDay ? "_" . DateTimeHelper::strftime("%Y%m%d") : "") . ".log";

    $fp = @fopen($filename, 'a');
    if ($fp) {
      if (@flock($fp, LOCK_EX)) {
        @fseek($fp, 0, SEEK_END);
        fputs($fp, sprintf("%s\t%s\t%s", date("Y-m-d H:i:s"), $identity, $msg));
        fputs($fp, "\r\n");
      }
      fclose($fp);
    }
  }

  /**
   * Log query totals and page load times
   * @param int $logtimein
   */
  function logTotalQueryAndLoadTimes($logtimein = false) {
    if ($logtimein === false) {
      global $logtime;
      $logtimein = $logtime;
    }
    $logendtime = microtime(true);
    $duration = $logendtime - $logtimein;
    //niet loggen bij cron, deze is bedoeld voor zware klussen.
    if (empty($_SESSION["cron"]) && $duration > 1.2) {
      logToFile("slowpage_log", "LAADTIJDPAGINA: " . number_format($duration, 2) . " seconden, URL: " . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . ", IP: " . IpHelper::getIpAdress());
    }
    if (defined('LOG_QUERY') && LOG_QUERY == true) {
      logToFile("mysql_log", "TOTAAL QUERY TIJD: " . number_format(DBConn::$total_duration_queries / 1000, 2) . " seconden");
      logToFile("mysql_log", "LAADTIJDPAGINA: " . number_format($duration, 2) . " seconden");
    }
  }
