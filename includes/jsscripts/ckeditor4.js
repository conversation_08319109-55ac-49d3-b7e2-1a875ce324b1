/**
 * Default configuration for CKeditor 4
 * http://docs.ckeditor.com/#!/api/CKEDITOR.config
 * http://nightly.ckeditor.com/13-04-18-07-42/standard/samples/plugins/toolbar/toolbar.html
 */

//ckeditor init
function ckeditorInit() {

  ///gsdfw/includes/ckeditor4/contents.css: dit is de standaard content.css van de ckeditor. Deze heb ik  uitgezet.

  var stylesheets = [];
  // if(!Array.isArray(CKEDITOR.config['contentsCss']) && CKEDITOR.config['contentsCss']!="") {
  //   stylesheets.push(CKEDITOR.config['contentsCss']);
  // }
  // else if(Array.isArray(CKEDITOR.config['contentsCss'])) {
  //   stylesheets = CKEDITOR.config['contentsCss'];
  // }

  stylesheets.push('/gsdfw/includes/font-awesome/css/font-awesome.min.css');

  CKEDITOR.config['contentsCss'] = stylesheets;
  CKEDITOR.config['height'] = 400;

  CKEDITOR.config['toolbarGroups'] = [
    { name: 'document',	   groups: [ 'mode', 'document', 'doctools' ] },
    // On the basic preset, clipboard and undo is handled by keyboard.
    // Uncomment the following line to enable them on the toolbar as well.
    { name: 'clipboard',   groups: [ 'clipboard', 'undo' ] },
    { name: 'editing',     groups: [ 'find', 'selection', 'spellchecker' ] },
    //{ name: 'forms' },
    { name: 'basicstyles', groups: [ 'basicstyles', 'cleanup' ] },
    { name: 'paragraph',   groups: [ 'list', 'indent', 'blocks', 'align' ] },
    { name: 'links' },
    { name: 'insert' },
    { name: 'styles' },
    { name: 'colors' },
    { name: 'tools' },
    { name: 'others' },
    { name: 'about' }
  ];

  //open filebrowser in popup
  CKEDITOR.on('dialogDefinition', function (event)
  {
    var editor = event.editor;
    var dialogDefinition = event.data.definition;
    var dialogName = event.data.name;

    var cleanUpFuncRef = CKEDITOR.tools.addFunction(function ()
    {
      // Do the clean-up of filemanager here (called when an image was selected or cancel was clicked)
      $('#fm-iframe').remove();
      $("body").css("overflow-y", "scroll");
    });

    var tabCount = dialogDefinition.contents.length;
    for (var i = 0; i < tabCount; i++) {
      var dialogTab = dialogDefinition.contents[i];
      if (!(dialogTab && typeof dialogTab.get === 'function')) {
        continue;
      }

      var browseButton = dialogTab.get('browse');
      if (browseButton !== null) {
        //browseButton.hidden = false;
        browseButton.onClick = function (dialog, i) {
          editor._.filebrowserSe = this;
          var iframe = $("<iframe id='fm-iframe' class='fm-modal'/>").attr({
            src: CKEDITOR.config['filebrowserBrowseUrl']+ // Change it to wherever  Filemanager is stored.
              '?CKEditorFuncNum=' + CKEDITOR.instances[event.editor.name]._.filebrowserFn +
              '&CKEditorCleanUpFuncNum=' + cleanUpFuncRef +
              '&langCode=nl' +
              '&CKEditor=' + event.editor.name +
              '&filter='+dialogName
          });

          $("body").append(iframe);
          $("body").css("overflow-y", "hidden");  // Get rid of possible scrollbars in containing document
        }
      }
    }
  }); // dialogDefinition
}

// simpele toolbar
function ckeditorSimple() {
  CKEDITOR.config['toolbar'] = [
    ['Format', 'Bold', 'Italic','Underline','TextColor', '-', 'NumberedList', 'BulletedList','-','Cut','Copy','Paste','PasteText','PasteFromWord','RemoveFormat', '-', 'Link', 'Unlink', '-', 'Table', '-', 'Source']
  ];
}

function ckeditorSimpleWithImage() {
  CKEDITOR.config['toolbar'] = [
    ['Format', 'Bold', 'Italic','Underline','TextColor', '-', 'NumberedList', 'BulletedList','-','Cut','Copy','Paste','PasteText','PasteFromWord','RemoveFormat', '-', 'Link', 'Unlink', 'Image','-', 'Table', '-', 'Source']
  ];
}

function ckeditorSimpleWithIframe() {
  CKEDITOR.config.extraPlugins = 'scayt';
  CKEDITOR.config.scayt_autoStartup = true;
  CKEDITOR.config.scayt_sLang = 'nl_NL';
  CKEDITOR.config['toolbar'] = [
    ['Format', 'Bold', 'Italic','Underline','TextColor', '-', 'NumberedList', 'BulletedList','-','Cut','Copy','Paste','PasteText','PasteFromWord','RemoveFormat', '-', 'Link', 'Unlink', '-', 'Table', '-', 'Source','Iframe']
  ];
}

// NL spellingscontrole
function ckeditorScaytNl() {
  CKEDITOR.config.extraPlugins = 'scayt';
  CKEDITOR.config.scayt_autoStartup = true;
  CKEDITOR.config.scayt_sLang = 'nl_NL';
}

// scripts toestaan in editor
function ckeditorAllowScripts() {
  CKEDITOR.config.allowedContent = true;
  //CKEDITOR.config.disallowedContent  = 'img{width,height}';
}