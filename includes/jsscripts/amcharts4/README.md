## amCharts 4

You are now in possession of amCharts 4 - the most advanced JavaScript dataviz
library ever.

For a short overview of features, visit [this page](https://www.amcharts.com/javascript-charts/).


### Important notice about support

amCharts support is guaranteed for commercial license holders and amPlus
subscribers. GitHub issues is not usually monitored by amCharts support
staff and may not be answered. If you do have a license/subscription, you 
may [contact us directly](https://www.amcharts.com/support/support-info/priority-support/)
for support. If you don't, here are
[a few options](https://www.amcharts.com/support/support-info/free-support/)
for you.


### Contents

|Directory|Description|
|---------|-----------|
|/|Root directory containing library files and related files|
|/deps/|3rd party libraries and other dependencies|
|/examples/|Collection of working examples|
|/lang/|Translation files / locales|
|/themes/|Theme files|


### Documentation

For extensive documentation, including getting started tutorials, as well
as class reference visit [V4 documentation website](https://www.amcharts.com/docs/v4).


### Other ways to get amCharts 4

* [NPM package](https://www.npmjs.com/package/@amcharts/amcharts4)
* [GitHub repository](https://github.com/amcharts/amcharts4)
* [ZIP download & CDN info](https://www.amcharts.com/download/)


### CDN

Copy of the contents of this package is available as a free CDN service,
accessible via URL `https://www.amcharts.com/lib/4/...`, e.g.:

```
<script src="https://www.amcharts.com/lib/4/core.js"></script>
<script src="https://www.amcharts.com/lib/4/charts.js"></script>
<script src="https://www.amcharts.com/lib/4/themes/animated.js"></script>
```


### Related packages

This package inlcudes `MapChart` (geographical maps) functionality. However,
it does not include geodata itself (map files) needed to instantiate the maps.

Those are available via separate package:

* [NPM package](https://www.npmjs.com/package/@amcharts/amcharts4-geodata)
* [GitHub repository (geodata)](https://github.com/amcharts/amcharts4-geodata)
* [ZIP download](https://www.amcharts.com/download/download-v4/)


### License

If you have a commercial amCharts 4 license, this software is covered by your
license, which supersedes any other license bundled with this package.

If you don't have a commercial license, the use of this software is covered by
a freeware license. Refer to included LICENSE file. The license is also
[available online](https://github.com/amcharts/amcharts4/blob/master/dist/script/LICENSE).


### Making translations

Please refer to [this tutorial](https://www.amcharts.com/docs/v4/tutorials/creating-translations/).


### Changelog

* [amCharts 4 Changelog](https://github.com/amcharts/amcharts4/blob/master/dist/script/CHANGELOG.md)
* [Documentation Changelog](https://www.amcharts.com/docs/v4/changelog/)


### Questions?

[Contact amCharts](mailto:<EMAIL>).


### Found a bug?

Open an [issue](https://github.com/amcharts/amcharts4/issues).