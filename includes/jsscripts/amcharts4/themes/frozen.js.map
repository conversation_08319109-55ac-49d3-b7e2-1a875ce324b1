{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/frozen.ts", "webpack:///./themes/frozen.js"], "names": ["frozen", "object", "Object", "Registry", "list", "Color", "minLightness", "maxLightness", "reuse", "window", "am4themes_frozen"], "mappings": ";;;;;;;;;;;;;;;;;;;kJA+BeA,EArBO,SAACC,GAElBC,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,cACxBA,EAAOG,MACNF,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,YAEPD,EAAOK,aAAe,GACtBL,EAAOM,aAAe,GACtBN,EAAOO,OAAQ,IC1BjBC,OAAAC,iBAA0BV", "file": "./themes/frozen.js", "sourcesContent": ["/**\r\n * Color set from Frozen movie borrowed from https://twitter.com/CINEMAPALETTES\r\n */\r\n\r\nimport { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { color } from \"../core/utils/Color\";\r\nimport { ColorSet } from \"../core/utils/ColorSet\";\r\nimport { BaseObject } from \"../core/Base\";\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\r\n\tif (is<ColorSet>(object, \"ColorSet\")) {\r\n\t\tobject.list = [\r\n\t\t\tcolor(\"#bec4f8\"),\r\n\t\t\tcolor(\"#a5abee\"),\r\n\t\t\tcolor(\"#6a6dde\"),\r\n\t\t\tcolor(\"#4d42cf\"),\r\n\t\t\tcolor(\"#713e8d\"),\r\n\t\t\tcolor(\"#a160a0\"),\r\n\t\t\tcolor(\"#eb6eb0\"),\r\n\t\t\tcolor(\"#f597bb\"),\r\n\t\t\tcolor(\"#fbb8c9\"),\r\n\t\t\tcolor(\"#f8d4d8\")\r\n\t\t];\r\n\t\tobject.minLightness = 0.2;\r\n\t\tobject.maxLightness = 0.7;\r\n\t\tobject.reuse = true;\r\n\t}\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/frozen.ts", "import m from \"../../es2015/themes/frozen\";\nwindow.am4themes_frozen = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/frozen.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}