{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/moonrisekingdom.ts", "webpack:///./themes/moonrisekingdom.js"], "names": ["moonrisekingdom", "object", "Object", "Registry", "list", "Color", "minLightness", "maxLightness", "reuse", "window", "am4themes_moonrisekingdom"], "mappings": ";;;;;;;;;;;;;;;;;;;kJA+BeA,EArBO,SAACC,GAElBC,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,cACxBA,EAAOG,MACNF,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,YAEPD,EAAOK,aAAe,GACtBL,EAAOM,aAAe,GACtBN,EAAOO,OAAQ,IC1BjBC,OAAAC,0BAAmCV", "file": "./themes/moonrisekingdom.js", "sourcesContent": ["/**\r\n * Color set from Moonrise kingdom movie borrowed from https://twitter.com/CINEMAPALETTES\r\n */\r\n\r\nimport { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { color } from \"../core/utils/Color\";\r\nimport { ColorSet } from \"../core/utils/ColorSet\";\r\nimport { BaseObject } from \"../core/Base\";\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\r\n\tif (is<ColorSet>(object, \"ColorSet\")) {\r\n\t\tobject.list = [\r\n\t\t\tcolor(\"#3a1302\"),\r\n\t\t\tcolor(\"#601205\"),\r\n\t\t\tcolor(\"#8a2b0d\"),\r\n\t\t\tcolor(\"#c75e24\"),\r\n\t\t\tcolor(\"#c79f59\"),\r\n\t\t\tcolor(\"#a4956a\"),\r\n\t\t\tcolor(\"#868569\"),\r\n\t\t\tcolor(\"#756f61\"),\r\n\t\t\tcolor(\"#586160\"),\r\n\t\t\tcolor(\"#617983\")\r\n\t\t];\r\n\t\tobject.minLightness = 0.2;\r\n\t\tobject.maxLightness = 0.7;\r\n\t\tobject.reuse = true;\r\n\t}\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/moonrisekingdom.ts", "import m from \"../../es2015/themes/moonrisekingdom\";\nwindow.am4themes_moonrisekingdom = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/moonrisekingdom.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}