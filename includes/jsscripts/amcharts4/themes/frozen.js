/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["8ce2"],{ik00:function(e,c,t){"use strict";Object.defineProperty(c,"__esModule",{value:!0});var b=t("aCit"),a=t("8ZqG"),i=function(e){Object(b.b)(e,"ColorSet")&&(e.list=[Object(a.c)("#bec4f8"),Object(a.c)("#a5abee"),Object(a.c)("#6a6dde"),Object(a.c)("#4d42cf"),Object(a.c)("#713e8d"),Object(a.c)("#a160a0"),Object(a.c)("#eb6eb0"),Object(a.c)("#f597bb"),Object(a.c)("#fbb8c9"),Object(a.c)("#f8d4d8")],e.minLightness=.2,e.maxLightness=.7,e.reuse=!0)};window.am4themes_frozen=i}},["ik00"]);
//# sourceMappingURL=frozen.js.map