{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/material.ts", "webpack:///./themes/material.js"], "names": ["material", "object", "Object", "Registry", "list", "Color", "minLightness", "maxLightness", "reuse", "background", "cornerRadiusTopLeft", "cornerRadiusTopRight", "cornerRadiusBottomLeft", "cornerRadiusBottomRight", "animationDuration", "window", "am4themes_material"], "mappings": ";;;;;;;;;;;;;;;;;;;kJAiDeA,EAzCO,SAACC,GAClBC,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,cACxBA,EAAOG,MACNF,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,YAEPD,EAAOK,aAAe,GACtBL,EAAOM,aAAe,GACtBN,EAAOO,OAAQ,GAGZN,OAAAC,EAAA,EAAAD,CAAiBD,EAAQ,kBAC5BA,EAAOQ,WAAWC,oBAAsB,GACxCT,EAAOQ,WAAWE,qBAAuB,GACzCV,EAAOQ,WAAWG,uBAAyB,GAC3CX,EAAOQ,WAAWI,wBAA0B,IAGzCX,OAAAC,EAAA,EAAAD,CAAYD,EAAQ,aACvBA,EAAOa,kBAAoB,MC3C7BC,OAAAC,mBAA4BhB", "file": "./themes/material.js", "sourcesContent": ["import { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { color } from \"../core/utils/Color\";\r\nimport { ColorSet } from \"../core/utils/ColorSet\";\r\nimport { BaseObject } from \"../core/Base\";\r\nimport { Tooltip } from \"../core/elements/Tooltip\";\r\nimport { ResizeButton } from \"../core/elements/ResizeButton\";\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\tif (is<ColorSet>(object, \"ColorSet\")) {\r\n\t\tobject.list = [\r\n\t\t\tcolor(\"#F44336\"),\r\n\t\t\tcolor(\"#E91E63\"),\r\n\t\t\tcolor(\"#9C27B0\"),\r\n\t\t\tcolor(\"#673AB7\"),\r\n\t\t\tcolor(\"#3F51B5\"),\r\n\t\t\tcolor(\"#2196F3\"),\r\n\t\t\tcolor(\"#03A9F4\"),\r\n\t\t\tcolor(\"#00BCD4\"),\r\n\t\t\tcolor(\"#009688\"),\r\n\t\t\tcolor(\"#4CAF50\"),\r\n\t\t\tcolor(\"#8BC34A\"),\r\n\t\t\tcolor(\"#CDDC39\"),\r\n\t\t\tcolor(\"#FFEB3B\"),\r\n\t\t\tcolor(\"#FFC107\"),\r\n\t\t\tcolor(\"#FF9800\"),\r\n\t\t\tcolor(\"#FF5722\"),\r\n\t\t\tcolor(\"#795548\"),\r\n\t\t\tcolor(\"#9E9E9E\"),\r\n\t\t\tcolor(\"#607D8B\")\r\n\t\t];\r\n\t\tobject.minLightness = 0.2;\r\n\t\tobject.maxLightness = 0.7;\r\n\t\tobject.reuse = true;\r\n\t}\r\n\r\n\tif (is<ResizeButton>(object, \"ResizeButton\")) {\r\n\t\tobject.background.cornerRadiusTopLeft = 20;\r\n\t\tobject.background.cornerRadiusTopRight = 20;\r\n\t\tobject.background.cornerRadiusBottomLeft = 20;\r\n\t\tobject.background.cornerRadiusBottomRight = 20;\r\n\t}\r\n\r\n\tif (is<Tooltip>(object, \"Tooltip\")) {\r\n\t\tobject.animationDuration = 800;\r\n\t}\r\n\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/material.ts", "import m from \"../../es2015/themes/material\";\nwindow.am4themes_material = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/material.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}