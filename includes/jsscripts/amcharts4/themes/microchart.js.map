{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/microchart.ts", "webpack:///./themes/microchart.js"], "names": ["microchart", "object", "Object", "Registry", "showSystemTooltip", "padding", "startGrip", "disabled", "endGrip", "cursorTooltipEnabled", "labels", "template", "ticks", "minHeight", "undefined", "min<PERSON><PERSON><PERSON>", "window", "am4themes_microchart"], "mappings": ";;;;;;;;;;;;;;;;;;;sIA0DeA,EA5CO,SAACC,GAElBC,OAAAC,EAAA,EAAAD,CAAWD,EAAQ,YACtBA,EAAOG,mBAAoB,GAGxBF,OAAAC,EAAA,EAAAD,CAAUD,EAAQ,UACrBA,EAAOI,QAAQ,EAAG,EAAG,EAAG,GAGrBH,OAAAC,EAAA,EAAAD,CAAcD,EAAQ,eACzBA,EAAOK,UAAUC,UAAW,EAC5BN,EAAOO,QAAQD,UAAW,IAGvBL,OAAAC,EAAA,EAAAD,CAAcD,EAAQ,cAAgBC,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,aAAeC,OAAAC,EAAA,EAAAD,CAASD,EAAQ,WAC9FA,EAAOM,UAAW,GAGfL,OAAAC,EAAA,EAAAD,CAASD,EAAQ,UACpBA,EAAOQ,sBAAuB,GAG3BP,OAAAC,EAAA,EAAAD,CAAkBD,EAAQ,mBAC7BA,EAAOS,OAAOC,SAASJ,UAAW,EAClCN,EAAOW,MAAMD,SAASJ,UAAW,GAG9BL,OAAAC,EAAA,EAAAD,CAAkBD,EAAQ,kBAC7BA,EAAOI,QAAQ,EAAG,EAAG,EAAG,GAGrBH,OAAAC,EAAA,EAAAD,CAAcD,EAAQ,eACtBA,EAAOY,YACTZ,EAAOY,eAAYC,GAEjBb,EAAOc,WACTd,EAAOc,cAAWD,KClDrBE,OAAAC,qBAA8BjB", "file": "./themes/microchart.js", "sourcesContent": ["import { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { BaseObject } from \"../core/Base\";\r\nimport { Sprite } from \"../core/Sprite\";\r\nimport { Container } from \"../core/Container\";\r\nimport { ZoomOutButton } from \"../core/elements/ZoomOutButton\";\r\nimport { AxisLine } from \"../charts/axes/AxisLine\";\r\nimport { Axis } from \"../charts/axes/Axis\";\r\nimport { Grid } from \"../charts/axes/Grid\";\r\nimport { Chart } from \"../charts/Chart\";\r\nimport { PercentSeries } from \"../charts/series/PercentSeries\";\r\nimport { AxisLabel } from \"../charts/axes/AxisLabel\";\r\nimport { Scrollbar } from \"../core/elements/Scrollbar\";\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\r\n\tif (is<Sprite>(object, \"Sprite\")) {\r\n\t\tobject.showSystemTooltip = false;\r\n\t}\r\n\r\n\tif (is<Chart>(object, \"Chart\")) {\r\n\t\tobject.padding(0, 0, 0, 0);\r\n\t}\r\n\r\n\tif (is<Scrollbar>(object, \"Scrollbar\")) {\r\n\t\tobject.startGrip.disabled = true;\r\n\t\tobject.endGrip.disabled = true;\r\n\t}\r\n\r\n\tif (is<AxisLabel>(object, \"AxisLabel\") || is<AxisLine>(object, \"AxisLine\") || is<Grid>(object, \"Grid\")) {\r\n\t\tobject.disabled = true;\r\n\t}\r\n\r\n\tif (is<Axis>(object, \"Axis\")) {\r\n\t\tobject.cursorTooltipEnabled = false;\r\n\t}\r\n\r\n\tif (is<PercentSeries>(object, \"PercentSeries\")) {\r\n\t\tobject.labels.template.disabled = true;\r\n\t\tobject.ticks.template.disabled = true;\r\n\t}\r\n\r\n\tif (is<ZoomOutButton>(object, \"ZoomOutButton\")) {\r\n\t\tobject.padding(1, 1, 1, 1);\r\n\t}\r\n\r\n\tif (is<Container>(object, \"Container\")) {\r\n\t\tif(object.minHeight) {\r\n\t\t\tobject.minHeight = undefined;\r\n\t\t}\r\n\t\tif(object.minWidth) {\r\n\t\t\tobject.minWidth = undefined;\r\n\t\t}\r\n\t}\r\n\r\n\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/microchart.ts", "import m from \"../../es2015/themes/microchart\";\nwindow.am4themes_microchart = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/microchart.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}