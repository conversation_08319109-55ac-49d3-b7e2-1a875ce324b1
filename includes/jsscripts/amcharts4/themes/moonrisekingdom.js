/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["364f"],{ORxu:function(e,c,t){"use strict";Object.defineProperty(c,"__esModule",{value:!0});var b=t("aCit"),n=t("8ZqG"),O=function(e){Object(b.b)(e,"ColorSet")&&(e.list=[Object(n.c)("#3a1302"),Object(n.c)("#601205"),Object(n.c)("#8a2b0d"),Object(n.c)("#c75e24"),Object(n.c)("#c79f59"),Object(n.c)("#a4956a"),Object(n.c)("#868569"),Object(n.c)("#756f61"),Object(n.c)("#586160"),Object(n.c)("#617983")],e.minLightness=.2,e.maxLightness=.7,e.reuse=!0)};window.am4themes_moonrisekingdom=O}},["ORxu"]);
//# sourceMappingURL=moonrisekingdom.js.map