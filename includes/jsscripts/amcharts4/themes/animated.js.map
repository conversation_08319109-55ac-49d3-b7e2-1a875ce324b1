{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/animated.ts", "webpack:///./themes/animated.js"], "names": ["animated", "object", "Object", "Registry", "transitionDuration", "rangeChangeDuration", "interpolationDuration", "sequencedInterpolation", "defaultState", "hiddenState", "animationDuration", "properties", "opacity", "showOnInit", "window", "am4themes_animated"], "mappings": ";;;;;;;;;;;;;;;;;;;sIAiGeA,EA5EO,SAACC,GAClBC,OAAAC,EAAA,EAAAD,CAA0BD,EAAQ,iBACrCA,EAAOG,mBAAqB,KAGzBF,OAAAC,EAAA,EAAAD,CAAcD,EAAQ,eACzBA,EAAOI,oBAAsB,IAC7BJ,EAAOK,sBAAwB,IAC/BL,EAAOM,wBAAyB,EAE5BL,OAAAC,EAAA,EAAAD,CAAkBD,EAAQ,mBAC7BA,EAAOM,wBAAyB,GAG7BL,OAAAC,EAAA,EAAAD,CAAiBD,EAAQ,kBAC5BA,EAAOM,wBAAyB,IAI9BL,OAAAC,EAAA,EAAAD,CAAUD,EAAQ,WACrBA,EAAOO,aAAaJ,mBAAqB,IACzCH,EAAOQ,YAAYL,mBAAqB,KAGrCF,OAAAC,EAAA,EAAAD,CAAYD,EAAQ,aACvBA,EAAOS,kBAAoB,IAC3BT,EAAOO,aAAaJ,mBAAqB,IACzCH,EAAOQ,YAAYL,mBAAqB,KAGrCF,OAAAC,EAAA,EAAAD,CAAcD,EAAQ,eACzBA,EAAOS,kBAAoB,KAGxBR,OAAAC,EAAA,EAAAD,CAAWD,EAAQ,YACtBA,EAAOO,aAAaJ,mBAAqB,IACzCH,EAAOQ,YAAYL,mBAAqB,IACxCH,EAAOQ,YAAYE,WAAWC,QAAU,EACxCX,EAAOY,YAAa,GAGjBX,OAAAC,EAAA,EAAAD,CAAcD,EAAQ,eACzBA,EAAOQ,YAAYE,WAAWC,QAAU,GAGrCV,OAAAC,EAAA,EAAAD,CAAkBD,EAAQ,mBAC7BA,EAAOQ,YAAYE,WAAWC,QAAU,GAGrCV,OAAAC,EAAA,EAAAD,CAAgBD,EAAQ,iBAC3BA,EAAOO,aAAaJ,mBAAqB,IACzCH,EAAOQ,YAAYL,mBAAqB,IACxCH,EAAOQ,YAAYE,WAAWC,QAAU,GAGrCV,OAAAC,EAAA,EAAAD,CAAUD,EAAQ,WACrBA,EAAOO,aAAaJ,mBAAqB,IACzCH,EAAOQ,YAAYL,mBAAqB,IACxCH,EAAOQ,YAAYE,WAAWC,QAAU,GAGrCV,OAAAC,EAAA,EAAAD,CAAcD,EAAQ,eACzBA,EAAOQ,YAAYL,mBAAqB,KAGrCF,OAAAC,EAAA,EAAAD,CAAWD,EAAQ,YACtBA,EAAOO,aAAaJ,mBAAqB,IACzCH,EAAOQ,YAAYL,mBAAqB,IACxCH,EAAOQ,YAAYE,WAAWC,QAAU,GAGrCV,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,cACxBA,EAAOQ,YAAYE,WAAWC,QAAU,IC5F1CE,OAAAC,mBAA4Bf", "file": "./themes/animated.js", "sourcesContent": ["import { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { SpriteState } from \"../core/SpriteState\";\r\nimport { Component } from \"../core/Component\";\r\nimport { BaseObject } from \"../core/Base\";\r\nimport { Scrollbar } from \"../core/elements/Scrollbar\";\r\nimport { Tooltip } from \"../core/elements/Tooltip\";\r\nimport { Series } from \"../charts/series/Series\";\r\nimport { PercentSeries } from \"../charts/series/PercentSeries\";\r\nimport { SankeyDiagram } from \"../charts/types/SankeyDiagram\";\r\nimport { FunnelSeries } from \"../charts/series/FunnelSeries\";\r\nimport { MapSeries } from \"../charts/map/MapSeries\";\r\nimport { FunnelSlice } from \"../charts/elements/FunnelSlice\";\r\nimport { Column } from \"../charts/elements/Column\";\r\nimport { Column3D } from \"../charts/elements/Column3D\";\r\nimport { Slice } from \"../core/elements/Slice\";\r\nimport { Preloader } from \"../core/elements/Preloader\";\r\nimport { Chart } from \"../charts/Chart\";\r\n\r\n\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\tif (is<SpriteState<any, any>>(object, \"SpriteState\")) {\r\n\t\tobject.transitionDuration = 400;\r\n\t}\r\n\r\n\tif (is<Component>(object, \"Component\")) {\r\n\t\tobject.rangeChangeDuration = 500;\r\n\t\tobject.interpolationDuration = 500;\r\n\t\tobject.sequencedInterpolation = false;\r\n\r\n\t\tif (is<SankeyDiagram>(object, \"SankeyDiagram\")) {\r\n\t\t\tobject.sequencedInterpolation = true;\r\n\t\t}\r\n\r\n\t\tif (is<FunnelSeries>(object, \"FunnelSeries\")) {\r\n\t\t\tobject.sequencedInterpolation = true;\r\n\t\t}\r\n\t}\r\n\r\n\tif (is<Chart>(object, \"Chart\")) {\r\n\t\tobject.defaultState.transitionDuration = 2000;\r\n\t\tobject.hiddenState.transitionDuration = 1000;\r\n\t}\r\n\r\n\tif (is<Tooltip>(object, \"Tooltip\")) {\r\n\t\tobject.animationDuration = 400;\r\n\t\tobject.defaultState.transitionDuration = 400;\r\n\t\tobject.hiddenState.transitionDuration = 400;\r\n\t}\r\n\r\n\tif (is<Scrollbar>(object, \"Scrollbar\")) {\r\n\t\tobject.animationDuration = 500;\r\n\t}\r\n\r\n\tif (is<Series>(object, \"Series\")) {\r\n\t\tobject.defaultState.transitionDuration = 1000;\r\n\t\tobject.hiddenState.transitionDuration = 700;\r\n\t\tobject.hiddenState.properties.opacity = 1;\r\n\t\tobject.showOnInit = true;\r\n\t}\r\n\r\n\tif (is<MapSeries>(object, \"MapSeries\")) {\r\n\t\tobject.hiddenState.properties.opacity = 0;\r\n\t}\r\n\r\n\tif (is<PercentSeries>(object, \"PercentSeries\")) {\r\n\t\tobject.hiddenState.properties.opacity = 0;\r\n\t}\r\n\r\n\tif (is<FunnelSlice>(object, \"FunnelSlice\")) {\r\n\t\tobject.defaultState.transitionDuration = 800;\r\n\t\tobject.hiddenState.transitionDuration = 1000;\r\n\t\tobject.hiddenState.properties.opacity = 1;\r\n\t}\r\n\r\n\tif (is<Slice>(object, \"Slice\")) {\r\n\t\tobject.defaultState.transitionDuration = 700;\r\n\t\tobject.hiddenState.transitionDuration = 1000;\r\n\t\tobject.hiddenState.properties.opacity = 1;\r\n\t}\r\n\r\n\tif (is<Preloader>(object, \"Preloader\")) {\r\n\t\tobject.hiddenState.transitionDuration = 2000;\r\n\t}\r\n\r\n\tif (is<Column>(object, \"Column\")) {\r\n\t\tobject.defaultState.transitionDuration = 700;\r\n\t\tobject.hiddenState.transitionDuration = 1000;\r\n\t\tobject.hiddenState.properties.opacity = 1;\r\n\t}\r\n\r\n\tif (is<Column3D>(object, \"Column3D\")) {\r\n\t\tobject.hiddenState.properties.opacity = 0;\r\n\t}\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/animated.ts", "import m from \"../../es2015/themes/animated\";\nwindow.am4themes_animated = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/animated.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}