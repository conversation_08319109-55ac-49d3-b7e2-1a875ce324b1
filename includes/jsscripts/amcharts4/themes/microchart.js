/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["30f0"],{uvMO:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var b=i("aCit"),a=function(e){Object(b.b)(e,"Sprite")&&(e.showSystemTooltip=!1),Object(b.b)(e,"Chart")&&e.padding(0,0,0,0),Object(b.b)(e,"Scrollbar")&&(e.startGrip.disabled=!0,e.endGrip.disabled=!0),(Object(b.b)(e,"AxisLabel")||Object(b.b)(e,"AxisLine")||Object(b.b)(e,"Grid"))&&(e.disabled=!0),Object(b.b)(e,"Axis")&&(e.cursorTooltipEnabled=!1),Object(b.b)(e,"PercentSeries")&&(e.labels.template.disabled=!0,e.ticks.template.disabled=!0),Object(b.b)(e,"ZoomOutButton")&&e.padding(1,1,1,1),Object(b.b)(e,"Container")&&(e.minHeight&&(e.minHeight=void 0),e.minWidth&&(e.minWidth=void 0))};window.am4themes_microchart=a}},["uvMO"]);
//# sourceMappingURL=microchart.js.map