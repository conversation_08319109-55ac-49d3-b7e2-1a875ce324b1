/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["8593"],{d66p:function(c,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var b=e("aCit"),o=e("8ZqG"),n=function(c){Object(b.b)(c,"ColorSet")&&(c.list=[Object(o.c)("#F44336"),Object(o.c)("#E91E63"),Object(o.c)("#9C27B0"),Object(o.c)("#673AB7"),Object(o.c)("#3F51B5"),Object(o.c)("#2196F3"),Object(o.c)("#03A9F4"),Object(o.c)("#00BCD4"),Object(o.c)("#009688"),Object(o.c)("#4CAF50"),Object(o.c)("#8BC34A"),Object(o.c)("#CDDC39"),Object(o.c)("#FFEB3B"),Object(o.c)("#FFC107"),Object(o.c)("#FF9800"),Object(o.c)("#FF5722"),Object(o.c)("#795548"),Object(o.c)("#9E9E9E"),Object(o.c)("#607D8B")],c.minLightness=.2,c.maxLightness=.7,c.reuse=!0),Object(b.b)(c,"ResizeButton")&&(c.background.cornerRadiusTopLeft=20,c.background.cornerRadiusTopRight=20,c.background.cornerRadiusBottomLeft=20,c.background.cornerRadiusBottomRight=20),Object(b.b)(c,"Tooltip")&&(c.animationDuration=800)};window.am4themes_material=n}},["d66p"]);
//# sourceMappingURL=material.js.map