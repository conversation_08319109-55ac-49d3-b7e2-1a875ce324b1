{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/kelly.ts", "webpack:///./themes/kelly.js"], "names": ["kelly", "object", "Object", "Registry", "list", "Color", "minLightness", "maxLightness", "reuse", "window", "am4themes_kelly"], "mappings": ";;;;;;;;;;;;;;;;;;;oJAgDeA,EAlCO,SAACC,GAElBC,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,cACxBA,EAAOG,MACNF,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,YAEPD,EAAOK,aAAe,GACtBL,EAAOM,aAAe,GACtBN,EAAOO,OAAQ,IC1CjBC,OAAAC,gBAAyBV", "file": "./themes/kelly.js", "sourcesContent": ["/**\r\n * <PERSON>'s colors is a set of 22 highly contrasting colors.\r\n *\r\n * More info:\r\n * {@link https://i.kinja-img.com/gawker-media/image/upload/1015680494325093012.JPG}\r\n * {@link https://eleanormaclure.files.wordpress.com/2011/03/colour-coding.pdf}\r\n */\r\n\r\nimport { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { color } from \"../core/utils/Color\";\r\nimport { ColorSet } from \"../core/utils/ColorSet\";\r\nimport { BaseObject } from \"../core/Base\";\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\r\n\tif (is<ColorSet>(object, \"ColorSet\")) {\r\n\t\tobject.list = [\r\n\t\t\tcolor(\"#F3C300\"),\r\n\t\t\tcolor(\"#875692\"),\r\n\t\t\tcolor(\"#F38400\"),\r\n\t\t\tcolor(\"#A1CAF1\"),\r\n\t\t\tcolor(\"#BE0032\"),\r\n\t\t\tcolor(\"#C2B280\"),\r\n\t\t\tcolor(\"#848482\"),\r\n\t\t\tcolor(\"#008856\"),\r\n\t\t\tcolor(\"#E68FAC\"),\r\n\t\t\tcolor(\"#0067A5\"),\r\n\t\t\tcolor(\"#F99379\"),\r\n\t\t\tcolor(\"#604E97\"),\r\n\t\t\tcolor(\"#F6A600\"),\r\n\t\t\tcolor(\"#B3446C\"),\r\n\t\t\tcolor(\"#DCD300\"),\r\n\t\t\tcolor(\"#882D17\"),\r\n\t\t\tcolor(\"#8DB600\"),\r\n\t\t\tcolor(\"#654522\"),\r\n\t\t\tcolor(\"#E25822\"),\r\n\t\t\tcolor(\"#2B3D26\"),\r\n\t\t\tcolor(\"#F2F3F4\"),\r\n\t\t\tcolor(\"#222222\")\r\n\t\t];\r\n\t\tobject.minLightness = 0.2;\r\n\t\tobject.maxLightness = 0.7;\r\n\t\tobject.reuse = true;\r\n\t}\r\n\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/kelly.ts", "import m from \"../../es2015/themes/kelly\";\nwindow.am4themes_kelly = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/kelly.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}