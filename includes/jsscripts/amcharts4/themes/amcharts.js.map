{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/amcharts.ts", "webpack:///./themes/amcharts.js"], "names": ["amcharts", "object", "Object", "Registry", "strokeOpacity", "list", "Color", "reuse", "stepOptions", "lightness", "hue", "passOptions", "window", "am4themes_amcharts"], "mappings": ";;;;;;;;;;;;;;;;;;;kJA+BeA,EAxBO,SAACC,GAClBC,OAAAC,EAAA,EAAAD,CAASD,EAAQ,UACpBA,EAAOG,cAAgB,KAGpBF,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,cACxBA,EAAOI,MACNH,OAAAI,EAAA,EAAAJ,CAAM,WACNA,OAAAI,EAAA,EAAAJ,CAAM,WACNA,OAAAI,EAAA,EAAAJ,CAAM,WACNA,OAAAI,EAAA,EAAAJ,CAAM,WACNA,OAAAI,EAAA,EAAAJ,CAAM,WACNA,OAAAI,EAAA,EAAAJ,CAAM,WACNA,OAAAI,EAAA,EAAAJ,CAAM,YAEPD,EAAOM,OAAQ,EACfN,EAAOO,aACNC,UAAW,GACXC,IAAK,GAENT,EAAOU,iBC1BTC,OAAAC,mBAA4Bb", "file": "./themes/amcharts.js", "sourcesContent": ["import { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { color } from \"../core/utils/Color\";\r\nimport { BaseObject } from \"../core/Base\";\r\nimport { ColorSet } from \"../core/utils/ColorSet\";\r\nimport { Grid } from \"../charts/axes/Grid\";\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\tif (is<Grid>(object, \"Grid\")) {\r\n\t\tobject.strokeOpacity = 0.07;\r\n\t}\r\n\r\n\tif (is<ColorSet>(object, \"ColorSet\")) {\r\n\t\tobject.list = [\r\n\t\t\tcolor(\"#86ce86\"),\r\n\t\t\tcolor(\"#0975da\"),\r\n\t\t\tcolor(\"#0996f2\"),\r\n\t\t\tcolor(\"#1fb0ff\"),\r\n\t\t\tcolor(\"#41baff\"),\r\n\t\t\tcolor(\"#5ec5ff\"),\r\n\t\t\tcolor(\"#3db7ff\")\r\n\t\t];\r\n\t\tobject.reuse = false;\r\n\t\tobject.stepOptions = {\r\n\t\t\tlightness: 0.1,\r\n\t\t\thue: 0\r\n\t\t};\r\n\t\tobject.passOptions = {};\r\n\t}\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/amcharts.ts", "import m from \"../../es2015/themes/amcharts\";\nwindow.am4themes_amcharts = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/amcharts.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}