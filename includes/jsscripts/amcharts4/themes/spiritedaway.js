/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["bec2"],{AZyi:function(e,c,t){"use strict";Object.defineProperty(c,"__esModule",{value:!0});var b=t("aCit"),i=t("8ZqG"),j=function(e){Object(b.b)(e,"ColorSet")&&(e.list=[Object(i.c)("#65738e"),Object(i.c)("#766c91"),Object(i.c)("#78566f"),Object(i.c)("#523b58"),Object(i.c)("#813b3d"),Object(i.c)("#bc5e52"),Object(i.c)("#ee8b78"),Object(i.c)("#f9c885"),Object(i.c)("#eba05c"),Object(i.c)("#9b5134")],e.minLightness=.2,e.maxLightness=.7,e.reuse=!0)};window.am4themes_spiritedaway=j}},["AZyi"]);
//# sourceMappingURL=spiritedaway.js.map