{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/patterns.ts", "webpack:///./themes/patterns.js"], "names": ["patterns", "object", "Object", "Registry", "PatternSet", "tooltip", "ic", "InterfaceColorSet", "getFillFromObject", "fill", "getFor", "label", "background", "stroke", "backgroundOpacity", "window", "am4themes_patterns"], "mappings": ";;;;;;;;;;;;;;;;;;;8JAqCeA,EA3BO,SAACC,GAQtB,IALIC,OAAAC,EAAA,EAAAD,CAAYD,EAAQ,YAAcC,OAAAC,EAAA,EAAAD,CAAkBD,EAAQ,oBAC/DA,EAAOD,SAAW,IAAII,EAAA,IAInBF,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,aAAeC,OAAAC,EAAA,EAAAD,CAAkBD,EAAQ,mBAG7DA,EAAOI,QAAS,CACnB,IAAMC,EAAK,IAAIC,EAAA,EACfN,EAAOI,QAAQG,mBAAoB,EACnCP,EAAOI,QAAQI,KAAOH,EAAGI,OAAO,yBAChCT,EAAOI,QAAQM,MAAMF,KAAOH,EAAGI,OAAO,QACtCT,EAAOI,QAAQO,WAAWC,OAASP,EAAGI,OAAO,yBAK3CR,OAAAC,EAAA,EAAAD,CAAYD,EAAQ,aACvBA,EAAOa,kBAAoB,IC/B7BC,OAAAC,mBAA4BhB", "file": "./themes/patterns.js", "sourcesContent": ["import { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { BaseObject } from \"../core/Base\";\r\nimport { XYChart } from \"../charts/types/XYChart\";\r\nimport { PercentSeries } from \"../charts/series/PercentSeries\";\r\nimport { XYSeries } from \"../charts/series/XYSeries\";\r\nimport { PatternSet } from \"../core/utils/PatternSet\";\r\nimport { InterfaceColorSet } from \"../core/utils/InterfaceColorSet\";\r\nimport { Pattern } from \"../core/rendering/fills/Pattern\";\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\r\n\t// Create PatternSet\r\n\tif (is<XYChart>(object, \"XYChart\") || is<PercentSeries>(object, \"PercentSeries\")) {\r\n\t\tobject.patterns = new PatternSet();\r\n\t}\r\n\r\n\t// Set up compatible series\r\n\tif (is<XYSeries>(object, \"XYSeries\") || is<PercentSeries>(object, \"PercentSeries\")) {\r\n\r\n\t\t// Set up fill for series' tooltip\r\n\t\tif (object.tooltip) {\r\n\t\t\tconst ic = new InterfaceColorSet;\r\n\t\t\tobject.tooltip.getFillFromObject = false;\r\n\t\t\tobject.tooltip.fill = ic.getFor(\"alternativeBackground\");\r\n\t\t\tobject.tooltip.label.fill = ic.getFor(\"text\");\r\n\t\t\tobject.tooltip.background.stroke = ic.getFor(\"alternativeBackground\");\r\n\t\t}\r\n\r\n\t}\r\n\r\n\tif (is<Pattern>(object, \"Pattern\")) {\r\n\t\tobject.backgroundOpacity = 1;\r\n\t}\r\n\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/patterns.ts", "import m from \"../../es2015/themes/patterns\";\nwindow.am4themes_patterns = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/patterns.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}