{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/spiritedaway.ts", "webpack:///./themes/spiritedaway.js"], "names": ["spiritedaway", "object", "Object", "Registry", "list", "Color", "minLightness", "maxLightness", "reuse", "window", "am4themes_spiritedaway"], "mappings": ";;;;;;;;;;;;;;;;;;;kJA+BeA,EArBO,SAACC,GAElBC,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,cACxBA,EAAOG,MACNF,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,YAEPD,EAAOK,aAAe,GACtBL,EAAOM,aAAe,GACtBN,EAAOO,OAAQ,IC1BjBC,OAAAC,uBAAgCV", "file": "./themes/spiritedaway.js", "sourcesContent": ["/**\r\n * Color set from Spirited away movie borrowed from https://twitter.com/CINEMAPALETTES\r\n */\r\n\r\nimport { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { color } from \"../core/utils/Color\";\r\nimport { ColorSet } from \"../core/utils/ColorSet\";\r\nimport { BaseObject } from \"../core/Base\";\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\r\n\tif (is<ColorSet>(object, \"ColorSet\")) {\r\n\t\tobject.list = [\r\n\t\t\tcolor(\"#65738e\"),\r\n\t\t\tcolor(\"#766c91\"),\r\n\t\t\tcolor(\"#78566f\"),\r\n\t\t\tcolor(\"#523b58\"),\r\n\t\t\tcolor(\"#813b3d\"),\r\n\t\t\tcolor(\"#bc5e52\"),\r\n\t\t\tcolor(\"#ee8b78\"),\r\n\t\t\tcolor(\"#f9c885\"),\r\n\t\t\tcolor(\"#eba05c\"),\r\n\t\t\tcolor(\"#9b5134\")\r\n\t\t];\r\n\t\tobject.minLightness = 0.2;\r\n\t\tobject.maxLightness = 0.7;\r\n\t\tobject.reuse = true;\r\n\t}\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/spiritedaway.ts", "import m from \"../../es2015/themes/spiritedaway\";\nwindow.am4themes_spiritedaway = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/spiritedaway.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}