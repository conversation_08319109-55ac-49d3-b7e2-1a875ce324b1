/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["f6f5"],{JjGr:function(e,t,c){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=c("aCit"),i=c("8ZqG"),n=function(e){Object(s.b)(e,"ColorSet")&&(e.list=[Object(i.c)("#283250"),Object(i.c)("#902c2d"),Object(i.c)("#d5433d"),Object(i.c)("#f05440")],e.reuse=!1,e.stepOptions={lightness:.05,hue:0},e.passOptions={})};window.am4themes_dataviz=n}},["JjGr"]);
//# sourceMappingURL=dataviz.js.map