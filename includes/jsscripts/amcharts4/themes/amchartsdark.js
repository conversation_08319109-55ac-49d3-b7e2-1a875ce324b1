/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["b430"],{RfQD:function(t,e,c){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=c("aCit"),o=c("8ZqG"),b=function(t){Object(r.b)(t,"InterfaceColorSet")&&(t.setFor("stroke",Object(o.c)("#000000")),t.setFor("fill",Object(o.c)("#ffffff")),t.setFor("primaryButton",Object(o.c)("#0975da").lighten(-.2)),t.setFor("primaryButtonHover",Object(o.c)("#0975da").lighten(-.2)),t.setFor("primaryButtonDown",Object(o.c)("#0975da").lighten(-.2)),t.setFor("primaryButtonActive",Object(o.c)("#0975da").lighten(-.2)),t.setFor("primaryButtonText",Object(o.c)("#FFFFFF")),t.setFor("primaryButtonStroke",Object(o.c)("#0975da")),t.setFor("secondaryButton",Object(o.c)("#41baff")),t.setFor("secondaryButtonHover",Object(o.c)("#41baff").lighten(.1)),t.setFor("secondaryButtonDown",Object(o.c)("#41baff").lighten(.15)),t.setFor("secondaryButtonActive",Object(o.c)("#41baff").lighten(.15)),t.setFor("secondaryButtonText",Object(o.c)("#ffffff")),t.setFor("secondaryButtonStroke",Object(o.c)("#41baff").lighten(-.2)),t.setFor("grid",Object(o.c)("#ffffff")),t.setFor("background",Object(o.c)("#000000")),t.setFor("alternativeBackground",Object(o.c)("#000000")),t.setFor("text",Object(o.c)("#ffffff")),t.setFor("alternativeText",Object(o.c)("#ffffff")),t.setFor("disabledBackground",Object(o.c)("#bbbbbb"))),Object(r.b)(t,"Grid")&&(t.strokeOpacity=.07),Object(r.b)(t,"Scrollbar")&&(t.background.fillOpacity=.2,t.thumb.background.fillOpacity=.5),Object(r.b)(t,"ColorSet")&&(t.list=[Object(o.c)("#eeeab5"),Object(o.c)("#0975da"),Object(o.c)("#0996f2"),Object(o.c)("#1fb0ff"),Object(o.c)("#41baff"),Object(o.c)("#5ec5ff"),Object(o.c)("#3db7ff")],t.reuse=!1,t.stepOptions={lightness:.1,hue:0},t.passOptions={}),Object(r.b)(t,"Button")&&(t.background.fillOpacity=1,t.background.strokeOpacity=.5,t.background.fill=Object(o.c)("#303950"))};window.am4themes_amchartsdark=b}},["RfQD"]);
//# sourceMappingURL=amchartsdark.js.map