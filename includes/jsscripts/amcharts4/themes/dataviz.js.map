{"version": 3, "sources": ["webpack:///../../../../src/.internal/themes/dataviz.ts", "webpack:///./themes/dataviz.js"], "names": ["dataviz", "object", "Object", "Registry", "list", "Color", "reuse", "stepOptions", "lightness", "hue", "passOptions", "window", "am4themes_dataviz"], "mappings": ";;;;;;;;;;;;;;;;;;;kJA6BeA,EAnBO,SAACC,GAElBC,OAAAC,EAAA,EAAAD,CAAaD,EAAQ,cACxBA,EAAOG,MACNF,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,WACNA,OAAAG,EAAA,EAAAH,CAAM,YAEPD,EAAOK,OAAQ,EACfL,EAAOM,aACNC,UAAW,IACXC,IAAK,GAENR,EAAOS,iBCvBTC,OAAAC,kBAA2BZ", "file": "./themes/dataviz.js", "sourcesContent": ["/**\r\n * A color scheme inspired by {@link https://datavizproject.com/}\r\n */\r\n\r\nimport { ITheme } from \"./ITheme\";\r\nimport { is } from \"../core/Registry\";\r\nimport { color } from \"../core/utils/Color\";\r\nimport { ColorSet } from \"../core/utils/ColorSet\";\r\nimport { BaseObject } from \"../core/Base\";\r\n\r\nconst theme: ITheme = (object: BaseObject) => {\r\n\r\n\tif (is<ColorSet>(object, \"ColorSet\")) {\r\n\t\tobject.list = [\r\n\t\t\tcolor(\"#283250\"),\r\n\t\t\tcolor(\"#902c2d\"),\r\n\t\t\tcolor(\"#d5433d\"),\r\n\t\t\tcolor(\"#f05440\")\r\n\t\t];\r\n\t\tobject.reuse = false;\r\n\t\tobject.stepOptions = {\r\n\t\t\tlightness: 0.05,\r\n\t\t\thue: 0\r\n\t\t};\r\n\t\tobject.passOptions = {};\r\n\t}\r\n\r\n};\r\n\r\nexport default theme;\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../../src/.internal/themes/dataviz.ts", "import m from \"../../es2015/themes/dataviz\";\nwindow.am4themes_dataviz = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./themes/dataviz.js\n// module id = null\n// module chunks = "], "sourceRoot": ""}