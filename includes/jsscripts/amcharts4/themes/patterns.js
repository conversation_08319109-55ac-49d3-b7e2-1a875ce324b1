/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["08fc"],{ubb1:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=r("aCit"),a=r("W0E6"),i=r("MIZb"),n=function(t){if((Object(o.b)(t,"XYChart")||Object(o.b)(t,"PercentSeries"))&&(t.patterns=new a.a),(Object(o.b)(t,"XYSeries")||Object(o.b)(t,"PercentSeries"))&&t.tooltip){var e=new i.a;t.tooltip.getFillFromObject=!1,t.tooltip.fill=e.getFor("alternativeBackground"),t.tooltip.label.fill=e.getFor("text"),t.tooltip.background.stroke=e.getFor("alternativeBackground")}Object(o.b)(t,"Pattern")&&(t.backgroundOpacity=1)};window.am4themes_patterns=n}},["ubb1"]);
//# sourceMappingURL=patterns.js.map