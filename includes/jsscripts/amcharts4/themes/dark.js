/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["356d"],{"J7F+":function(t,e,c){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var b=c("aCit"),r=c("8ZqG"),o=function(t){Object(b.b)(t,"InterfaceColorSet")&&(t.setFor("stroke",Object(r.c)("#000000")),t.setFor("fill",Object(r.c)("#2b2b2b")),t.setFor("primaryButton",Object(r.c)("#6794dc").lighten(-.2)),t.setFor("primaryButtonHover",Object(r.c)("#6771dc").lighten(-.2)),t.setFor("primaryButtonDown",Object(r.c)("#68dc75").lighten(-.2)),t.setFor("primaryButtonActive",Object(r.c)("#68dc75").lighten(-.2)),t.setFor("primaryButtonText",Object(r.c)("#FFFFFF")),t.setFor("primaryButtonStroke",Object(r.c)("#6794dc")),t.setFor("secondaryButton",Object(r.c)("#3b3b3b")),t.setFor("secondaryButtonHover",Object(r.c)("#3b3b3b").lighten(.1)),t.setFor("secondaryButtonDown",Object(r.c)("#3b3b3b").lighten(.15)),t.setFor("secondaryButtonActive",Object(r.c)("#3b3b3b").lighten(.15)),t.setFor("secondaryButtonText",Object(r.c)("#bbbbbb")),t.setFor("secondaryButtonStroke",Object(r.c)("#3b3b3b").lighten(-.2)),t.setFor("grid",Object(r.c)("#bbbbbb")),t.setFor("background",Object(r.c)("#000000")),t.setFor("alternativeBackground",Object(r.c)("#ffffff")),t.setFor("text",Object(r.c)("#ffffff")),t.setFor("alternativeText",Object(r.c)("#000000")),t.setFor("disabledBackground",Object(r.c)("#bbbbbb"))),Object(b.b)(t,"Scrollbar")&&(t.background.fillOpacity=.4,t.thumb.background.fillOpacity=.5)};window.am4themes_dark=o}},["J7F+"]);
//# sourceMappingURL=dark.js.map