{"version": 3, "sources": ["webpack:///./lang/nl_NL.js", "webpack:///../../../src/lang/nl_NL.ts"], "names": ["window", "am4lang_nl_NL", "_decimalSeparator", "_thousandSeparator", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_second", "_duration_minute", "_duration_hour", "_duration_day", "_duration_week", "_duration_month", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "res", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Chord diagram", "Flow diagram", "TreeMap chart", "Series", "Candlestick Series", "Column Series", "Line Series", "Pie Slice Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;sHACAA,OAAAC,eCYCC,kBAAqB,IACrBC,mBAAsB,IAWtBC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,QACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OASdC,sBAAyB,MACzBC,iBAAoB,KACpBC,iBAAoB,KACpBC,eAAkB,KAClBC,cAAiB,KACjBC,eAAkB,KAClBC,gBAAmB,KACnBC,eAAkB,OAGlBC,QAAW,KACXC,QAAW,OAUXC,EAAK,IACLC,EAAK,IACLC,GAAM,KACNC,GAAM,KACNC,OAAQ,OACRC,OAAQ,OAWRC,QAAW,UACXC,SAAY,WACZC,MAAS,QACTC,MAAS,QACTC,IAAO,MACPC,KAAQ,OACRC,KAAQ,OACRC,OAAU,WACVC,UAAa,YACbC,QAAW,UACXC,SAAY,WACZC,SAAY,WACZC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,aAAc,MACdC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAGPC,OAAU,SACVC,OAAU,UACVC,QAAW,UACXC,UAAa,WACbC,SAAY,YACZC,OAAU,UACVC,SAAY,WACZC,IAAO,KACPC,IAAO,KACPC,IAAO,KACPC,IAAO,KACPC,IAAO,KACPC,IAAO,KACPC,IAAO,KAWPC,SAAY,SAASC,GACpB,IAAIC,EAAM,KAIV,OAHU,GAAPD,GAAmB,GAAPA,GAAYA,EAAM,MAC/BC,EAAM,OAEDA,GAKRC,WAAY,YAGZC,KAAQ,WACRC,KAAQ,UAGRC,OAAU,UAGVC,sCAAuC,qDAGvCC,QAAW,QAIXC,KAAQ,OAKRC,MAAS,UACTC,eAAgB,qBAChBC,YAAa,cACbC,YAAa,eACbC,cAAe,eACfC,cAAe,eACfC,iBAAkB,iBAClBC,gBAAiB,gBACjBC,eAAgB,eAChBC,gBAAiB,kBAKjBC,OAAU,QACVC,qBAAsB,oBACtBC,gBAAiB,aACjBC,cAAe,YACfC,mBAAoB,iBACpBC,aAAc,WAGdC,IAAO,QACPC,yBAA0B,gCAC1BC,0BAA2B,iCAC3BC,oCAAqC,wCACrCC,8DAA+D,8CAY/DC,OAAU,aACVC,MAAS,aACTC,KAAQ,OACRC,MAAS,UACTC,oCAAqC,0CACrCC,sCAAuC,2CACvCC,6CAA8C,qDAC9CC,2EAA4E,4GAC5EC,uFAAwF,qHACxFC,oCAAqC,0CACrCC,wBAAyB,+BACzBC,+EAAgF,4EAChFC,aAAc,kBACdC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,KAAQ,OACRC,IAAO,MACPC,KAAQ,OAYRC,8EAA+E,oGAC/EC,8CAA+C,kEAC/CC,mDAAoD,wEACpDC,oDAAqD,yEACrDC,wEAAyE,mGACzEC,2CAA4C,iEAC5CC,iDAAkD,0EAClDC,iDAAkD,0EAClDC,gBAAiB,gBACjBC,UAAW,SACXC,QAAS,SAGTC,mCAAoC,oDACpCC,yBAA0B,sCAC1BC,0BAA2B,6BAC3BC,eAAgB", "file": "./lang/nl_NL.js", "sourcesContent": ["import m from \"../../es2015/lang/nl_NL\";\nwindow.am4lang_nl_NL = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/nl_NL.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n * \n * Locale: nl_NL\n * Language: Dutch\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// Number formatting options.\n\t//\n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \".\",\n\n\t// Default date formats for various periods.\n\t//\n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-date-time/#Format_codes\n\t//\n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_day\": \"d MMM\",\n\t\"_date_week\": \"ww\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t//\n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t//\n\t// Available codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era translations\n\t\"_era_ad\": \"AD\",\n\t\"_era_bc\": \"v.C.\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t//\n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"A\",\n\t\"P\": \"P\",\n\t\"AM\": \"AM\",\n\t\"PM\": \"PM\",\n\t\"A.M.\": \"a.m.\",\n\t\"P.M.\": \"p.m.\",\n\n\t// Date-related stuff.\n\t//\n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t//\n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t\"January\": \"januari\",\n\t\"February\": \"februari\",\n\t\"March\": \"maart\",\n\t\"April\": \"april\",\n\t\"May\": \"mei\",\n\t\"June\": \"juni\",\n\t\"July\": \"juli\",\n\t\"August\": \"augustus\",\n\t\"September\": \"september\",\n\t\"October\": \"oktober\",\n\t\"November\": \"november\",\n\t\"December\": \"december\",\n\t\"Jan\": \"jan\",\n\t\"Feb\": \"feb\",\n\t\"Mar\": \"mrt\",\n\t\"Apr\": \"apr\",\n\t\"May(short)\": \"mei\",\n\t\"Jun\": \"jun\",\n\t\"Jul\": \"jul\",\n\t\"Aug\": \"aug\",\n\t\"Sep\": \"sep\",\n\t\"Oct\": \"okt\",\n\t\"Nov\": \"nov\",\n\t\"Dec\": \"dec\",\n\n\t// Weekdays.\n\t\"Sunday\": \"zondag\",\n\t\"Monday\": \"maandag\",\n\t\"Tuesday\": \"dinsdag\",\n\t\"Wednesday\": \"woensdag\",\n\t\"Thursday\": \"donderdag\",\n\t\"Friday\": \"vrijdag\",\n\t\"Saturday\": \"zaterdag\",\n\t\"Sun\": \"Zo\",\n\t\"Mon\": \"Ma\",\n\t\"Tue\": \"Di\",\n\t\"Wed\": \"Wo\",\n\t\"Thu\": \"Do\",\n\t\"Fri\": \"Vr\",\n\t\"Sat\": \"Za\",\n\n\t// Date ordinal function.\n\t//\n\t// This is used when adding number ordinal when formatting days in dates.\n\t//\n\t// E.g. \"January 1st\", \"February 2nd\".\n\t//\n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(day: number): string {\n\t\tlet res = \"de\";\n\t\tif(day == 1 || day == 8 || day > 19){\n\t\t  res = \"ste\";\n\t\t}\n\t\treturn res;\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Uitzoomen\",\n\n\t// Timeline buttons\n\t\"Play\": \"Afspelen\",\n\t\"Stop\": \"Stoppen\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Legenda\",\n\n\t// Legend's item screen reader indicator.\n\t\"Click, tap or press ENTER to toggle\": \"Klik, tik of druk op Enter om aan of uit te zetten\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Laden\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Home\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"Grafiek\",\n\t\"Serial chart\": \"Periodieke grafiek\",\n\t\"X/Y chart\": \"X-Y grafiek\",\n\t\"Pie chart\": \"Taartdiagram\",\n\t\"Gauge chart\": \"Meterdiagram\",\n\t\"Radar chart\": \"Radardiagram\",\n\t\"Sankey diagram\": \"Sankey-diagram\",\n\t\"Chord diagram\": \"Chord-diagram\",\n\t\"Flow diagram\": \"Flow-diagram\",\n\t\"TreeMap chart\": \"Treemap-grafiek\",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"Reeks\",\n\t\"Candlestick Series\": \"Candlestick-reeks\",\n\t\"Column Series\": \"Kolomreeks\",\n\t\"Line Series\": \"Lijnreeks\",\n\t\"Pie Slice Series\": \"Taartpuntreeks\",\n\t\"X/Y Series\": \"XY reeks\",\n\n\t// Map-related stuff.\n\t\"Map\": \"Kaart\",\n\t\"Press ENTER to zoom in\": \"Druk op Enter om in te zoomen\",\n\t\"Press ENTER to zoom out\": \"Druk op Enter om uit te zoomen\",\n\t\"Use arrow keys to zoom in and out\": \"Zoom in of uit met de pijltjestoetsen\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Zoom in of uit met de plus- en minustoetsen\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t//\n\t// \"Export\" is the top-level menu item.\n\t//\n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t//\n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Exporteren\",\n\t\"Image\": \"Afbeelding\",\n\t\"Data\": \"Data\",\n\t\"Print\": \"Printen\",\n\t\"Click, tap or press ENTER to open\": \"Klik, tik of druk op Enter om te openen\",\n\t\"Click, tap or press ENTER to print.\": \"Klik, tik of druk op Enter om te printen\",\n\t\"Click, tap or press ENTER to export as %1.\": \"Klik, tik of druk op Enter om te exporteren als %1\",\n\t'To save the image, right-click this link and choose \"Save picture as...\"': 'Klik met de rechtermuisknop op deze link en kies \"Afbeelding opslaan als...\" om de afbeelding op te slaan',\n\t'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': 'Klik met de rechtermuisknop op de miniatuur links en kies \"Afbeelding opslaan als...\" om de afbeelding op te slaan',\n\t\"(Press ESC to close this message)\": \"(Druk op ESC om dit bericht te sluiten)\",\n\t\"Image Export Complete\": \"Afbeelding exporteren gereed\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"Exportproces duurt langer dan verwacht. Er is misschien iets fout gegaan.\",\n\t\"Saved from\": \"Opgeslagen via:\",\n\t\"PNG\": \"PNG\",\n\t\"JPG\": \"JPG\",\n\t\"GIF\": \"GIF\",\n\t\"SVG\": \"SVG\",\n\t\"PDF\": \"PDF\",\n\t\"JSON\": \"JSON\",\n\t\"CSV\": \"CSV\",\n\t\"XLSX\": \"XLSX\",\n\n\t// Scrollbar-related stuff.\n\t//\n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t//\n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t//\n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t//\n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"Gebruik Tab om de hendels te selecteren of linker- en rechterpijltje om de selectie te veranderen\",\n\t\"Use left and right arrows to move selection\": \"Gebruik linker- en rechterpijltje om de selectie te verplaatsen\",\n\t\"Use left and right arrows to move left selection\": \"Gebruik linker- en rechterpijltje om de linkerselectie te verplaatsen\",\n\t\"Use left and right arrows to move right selection\": \"Gebruik linker- en rechterpijltje om de rechterselectie te verplaatsen\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"Gebruik Tab om de hendels te selecteren of pijltje omhoog en omlaag om de selectie te veranderen\",\n\t\"Use up and down arrows to move selection\": \"Gebruik pijltje omhoog en omlaag om de selectie te verplaatsen\",\n\t\"Use up and down arrows to move lower selection\": \"Gebruik pijltje omhoog en omlaag om de onderste selectie te verplaatsen\",\n\t\"Use up and down arrows to move upper selection\": \"Gebruik pijltje omhoog en omlaag om de bovenste selectie te verplaatsen\",\n\t\"From %1 to %2\": \"Van %1 tot %2\",\n\t\"From %1\": \"Van %1\",\n\t\"To %1\": \"Tot %2\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"Geen data-parser beschikbaar voor dit bestand: %1\",\n\t\"Error parsing file: %1\": \"Fout tijdens parsen van bestand: %1\",\n\t\"Unable to load file: %1\": \"Kan bestand niet laden: %1\",\n\t\"Invalid date\": \"Ongeldige datum\",\n};\n\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/nl_NL.ts"], "sourceRoot": ""}