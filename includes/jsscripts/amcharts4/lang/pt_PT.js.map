{"version": 3, "sources": ["webpack:///./lang/pt_PT.js", "webpack:///../../../src/lang/pt_PT.ts"], "names": ["window", "am4lang_pt_PT", "_decimalSeparator", "_thousandSeparator", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_second", "_duration_minute", "_duration_hour", "_duration_day", "_duration_week", "_duration_month", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Chord diagram", "Flow diagram", "TreeMap chart", "Series", "Candlestick Series", "Column Series", "Line Series", "Pie Slice Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;sHACAA,OAAAC,eCYCC,kBAAqB,IACrBC,mBAAsB,IAWtBC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,SACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OASdC,sBAAyB,MACzBC,iBAAoB,KACpBC,iBAAoB,KACpBC,eAAkB,KAClBC,cAAiB,KACjBC,eAAkB,KAClBC,gBAAmB,KACnBC,eAAkB,OAGlBC,QAAW,KACXC,QAAW,KAUXC,EAAK,GACLC,EAAK,GACLC,GAAM,GACNC,GAAM,GACNC,OAAQ,GACRC,OAAQ,GAWRC,QAAW,UACXC,SAAY,YACZC,MAAS,QACTC,MAAS,QACTC,IAAO,OACPC,KAAQ,QACRC,KAAQ,QACRC,OAAU,SACVC,UAAa,WACbC,QAAW,UACXC,SAAY,WACZC,SAAY,WACZC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,aAAc,MACdC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAGPC,OAAU,UACVC,OAAU,gBACVC,QAAW,cACXC,UAAa,eACbC,SAAY,eACZC,OAAU,cACVC,SAAY,SACZC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAWPC,SAAY,SAASC,GACpB,MAAO,KAKRC,WAAY,eAGZC,KAAQ,OACRC,KAAQ,QAGRC,OAAU,UAGVC,sCAAuC,iDAGvCC,QAAW,aAIXC,KAAQ,SAKRC,MAAS,UACTC,eAAgB,iBAChBC,YAAa,aACbC,YAAa,mBACbC,cAAe,oBACfC,cAAe,mBACfC,iBAAkB,kBAClBC,gBAAiB,gBACjBC,eAAgB,gBAChBC,gBAAiB,4BAKjBC,OAAU,SACVC,qBAAsB,wBACtBC,gBAAiB,oBACjBC,cAAe,mBACfC,mBAAoB,4BACpBC,aAAc,eAGdC,IAAO,OACPC,yBAA0B,uCAC1BC,0BAA2B,uCAC3BC,oCAAqC,gDACrCC,8DAA+D,8EAY/DC,OAAU,WACVC,MAAS,SACTC,KAAQ,QACRC,MAAS,WACTC,oCAAqC,8CACrCC,sCAAuC,iDACvCC,6CAA8C,0DAC9CC,2EAA4E,gGAC5EC,uFAAwF,iGACxFC,oCAAqC,4CACrCC,wBAAyB,wCACzBC,+EAAgF,oFAChFC,aAAc,WACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAYRC,8EAA+E,6FAC/EC,8CAA+C,+DAC/CC,mDAAoD,2EACpDC,oDAAqD,0EACrDC,wEAAyE,0FACzEC,2CAA4C,4DAC5CC,iDAAkD,qEAClDC,iDAAkD,oEAClDC,gBAAiB,eACjBC,UAAW,QACXC,QAAS,SAGTC,mCAAoC,6DACpCC,yBAA0B,iCAC1BC,0BAA2B,uCAC3BC,eAAgB", "file": "./lang/pt_PT.js", "sourcesContent": ["import m from \"../../es2015/lang/pt_PT\";\nwindow.am4lang_pt_PT = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/pt_PT.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n * \n * Locale: pt_PT\n * Language: Portuguese\n * \n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// Number formatting options.\n\t// \n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \".\",\n\n\t// Default date formats for various periods.\n\t// \n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-date-time/#Format_codes\n\t// \n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_day\": \"dd MMM\",\n\t\"_date_week\": \"ww\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t// \n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t// \n\t// Available codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era translations\n\t\"_era_ad\": \"DC\",\n\t\"_era_bc\": \"AC\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t// \n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"\",\n\t\"P\": \"\",\n\t\"AM\": \"\",\n\t\"PM\": \"\",\n\t\"A.M.\": \"\",\n\t\"P.M.\": \"\",\n\n\t// Date-related stuff.\n\t// \n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t// \n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t\"January\": \"Janeiro\",\n\t\"February\": \"Fevereiro\",\n\t\"March\": \"Março\",\n\t\"April\": \"Abril\",\n\t\"May\": \"Maio\",\n\t\"June\": \"Junho\",\n\t\"July\": \"Julho\",\n\t\"August\": \"Agosto\",\n\t\"September\": \"Setembro\",\n\t\"October\": \"Outubro\",\n\t\"November\": \"Novembro\",\n\t\"December\": \"Dezembro\",\n\t\"Jan\": \"Jan\",\n\t\"Feb\": \"Fev\",\n\t\"Mar\": \"Mar\",\n\t\"Apr\": \"Abr\",\n\t\"May(short)\": \"Mai\",\n\t\"Jun\": \"Jun\",\n\t\"Jul\": \"Jul\",\n\t\"Aug\": \"Ago\",\n\t\"Sep\": \"Set\",\n\t\"Oct\": \"Out\",\n\t\"Nov\": \"Nov\",\n\t\"Dec\": \"Dez\",\n\n\t// Weekdays.\n\t\"Sunday\": \"Domingo\",\n\t\"Monday\": \"Segunda-feira\",\n\t\"Tuesday\": \"Terça-feira\",\n\t\"Wednesday\": \"Quarta-feira\",\n\t\"Thursday\": \"Quinta-feira\",\n\t\"Friday\": \"Sexta-feira\",\n\t\"Saturday\": \"Sábado\",\n\t\"Sun\": \"Dom\",\n\t\"Mon\": \"Seg\",\n\t\"Tue\": \"Ter\",\n\t\"Wed\": \"Qua\",\n\t\"Thu\": \"Qui\",\n\t\"Fri\": \"Sex\",\n\t\"Sat\": \"Sáb\",\n\n\t// Date ordinal function.\n\t// \n\t// This is used when adding number ordinal when formatting days in dates.\n\t// \n\t// E.g. \"January 1st\", \"February 2nd\".\n\t// \n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(day: number): string {\n\t\treturn \"º\";\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Reduzir Zoom\",\n\n\t// Timeline buttons\n\t\"Play\": \"Play\",\n\t\"Stop\": \"Parar\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Legenda\",\n\n\t// Legend's item screen reader indicator.\n\t\"Click, tap or press ENTER to toggle\": \"Clique, toque ou pressione ENTER para alternar\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Carregando\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Início\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"Gráfico\",\n\t\"Serial chart\": \"Gráfico Serial\",\n\t\"X/Y chart\": \"Gráfico XY\",\n\t\"Pie chart\": \"Gráfico de Pizza\",\n\t\"Gauge chart\": \"Gráfico Indicador\",\n\t\"Radar chart\": \"Gráfico de Radar\",\n\t\"Sankey diagram\": \"Diagrama Sankey\",\n\t\"Chord diagram\": \"Diagram Chord\",\n\t\"Flow diagram\": \"Diagrama Flow\",\n\t\"TreeMap chart\": \"Gráfico de Mapa de Árvore\",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"Séries\",\n\t\"Candlestick Series\": \"Séries do Candlestick\",\n\t\"Column Series\": \"Séries de Colunas\",\n\t\"Line Series\": \"Séries de Linhas\",\n\t\"Pie Slice Series\": \"Séries de Fatias de Pizza\",\n\t\"X/Y Series\": \"Séries de XY\",\n\n\t// Map-related stuff.\n\t\"Map\": \"Mapa\",\n\t\"Press ENTER to zoom in\": \"Pressione ENTER para aumentar o zoom\",\n\t\"Press ENTER to zoom out\": \"Pressione ENTER para diminuir o zoom\",\n\t\"Use arrow keys to zoom in and out\": \"Use as setas para diminuir ou aumentar o zoom\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Use as teclas mais ou menos no seu teclado para diminuir ou aumentar o zoom\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t// \n\t// \"Export\" is the top-level menu item.\n\t// \n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t// \n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Exportar\",\n\t\"Image\": \"Imagem\",\n\t\"Data\": \"Dados\",\n\t\"Print\": \"Imprimir\",\n\t\"Click, tap or press ENTER to open\": \"Clique, toque ou pressione ENTER para abrir\",\n\t\"Click, tap or press ENTER to print.\": \"Clique, toque ou pressione ENTER para imprimir\",\n\t\"Click, tap or press ENTER to export as %1.\": \"Clique, toque ou pressione ENTER para exportar como %1.\",\n\t'To save the image, right-click this link and choose \"Save picture as...\"': \"Para salvar a imagem, clique no link com o botão da direira e escolha \\\"Salvar imagem como...\\\"\",\n\t'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': \"Para salvar, clique na imagem à esquerda com o botão direito e escolha \\\"Salvar imagem como...\\\"\",\n\t\"(Press ESC to close this message)\": \"(Pressione ESC para fechar esta mensagem)\",\n\t\"Image Export Complete\": \"A exportação da imagem foi completada\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"A exportação da imagem demorou mais do que o experado. Algo deve ter dado errado.\",\n\t\"Saved from\": \"Salvo de\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\n\t// Scrollbar-related stuff.\n\t// \n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t// \n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t// \n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t// \n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"Use TAB para selecionar os botões ou setas para a direita ou esquerda para mudar a seleção\",\n\t\"Use left and right arrows to move selection\": \"Use as setas para a esquerda ou direita para mover a seleção\",\n\t\"Use left and right arrows to move left selection\": \"Use as setas para a esquerda ou direita para mover a seleção da esquerda\",\n\t\"Use left and right arrows to move right selection\": \"Use as setas para a esquerda ou direita para mover a seleção da direita\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"Use TAB para selecionar os botões ou setas para cima ou para baixo para mudar a seleção\",\n\t\"Use up and down arrows to move selection\": \"Use as setas para cima ou para baixo para mover a seleção\",\n\t\"Use up and down arrows to move lower selection\": \"Use as setas para cima ou para baixo para mover a seleção de baixo\",\n\t\"Use up and down arrows to move upper selection\": \"Use as setas para cima ou para baixo para mover a seleção de cima\",\n\t\"From %1 to %2\": \"De %1 até %2\",\n\t\"From %1\": \"De %1\",\n\t\"To %1\": \"Até %1\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"Nenhum interpretador está disponível para este arquivo: %1\",\n\t\"Error parsing file: %1\": \"Erro ao analizar o arquivo: %1\",\n\t\"Unable to load file: %1\": \"O arquivo não pôde ser carregado: %1\",\n\t\"Invalid date\": \"Data inválida\",\n};\n\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/pt_PT.ts"], "sourceRoot": ""}