{"version": 3, "sources": ["webpack:///./lang/en_US.js", "webpack:///../../../src/lang/en_US.ts"], "names": ["window", "am4lang_en_US", "_decimalSeparator", "_thousandSeparator", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_second", "_duration_minute", "_duration_hour", "_duration_day", "_duration_week", "_duration_month", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "res", "Play", "Stop", "Zoom Out", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Chord diagram", "Flow diagram", "TreeMap chart", "Series", "Candlestick Series", "Column Series", "Line Series", "Pie Slice Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;sHACAA,OAAAC,eCSCC,kBAAqB,IACrBC,mBAAsB,IAGtBC,kBAAqB,aACrBC,aAAgB,aAChBC,aAAgB,UAChBC,WAAc,UACdC,UAAa,SACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OAGdC,sBAAyB,MACzBC,iBAAoB,KACpBC,iBAAoB,KACpBC,eAAkB,KAClBC,cAAiB,KACjBC,eAAkB,KAClBC,gBAAmB,KACnBC,eAAkB,OAGlBC,QAAW,KACXC,QAAW,KAGXC,EAAK,GACLC,EAAK,GACLC,GAAM,GACNC,GAAM,GACNC,OAAQ,GACRC,OAAQ,GAGRC,QAAW,GACXC,SAAY,GACZC,MAAS,GACTC,MAAS,GACTC,IAAO,GACPC,KAAQ,GACRC,KAAQ,GACRC,OAAU,GACVC,UAAa,GACbC,QAAW,GACXC,SAAY,GACZC,SAAY,GACZC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,aAAc,MACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,OAAU,GACVC,OAAU,GACVC,QAAW,GACXC,UAAa,GACbC,SAAY,GACZC,OAAU,GACVC,SAAY,GACZC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GAGPC,SAAY,SAASC,GACpB,IAAIC,EAAM,KACV,GAAKD,EAAM,IAAQA,EAAM,GACxB,OAAQA,EAAM,IACb,KAAK,EACJC,EAAM,KACN,MACD,KAAK,EACJA,EAAM,KACN,MACD,KAAK,EACJA,EAAM,KAIT,OAAOA,GAIRC,KAAQ,GACRC,KAAQ,GACRC,WAAY,GACZC,OAAU,GACVC,sCAAuC,GACvCC,QAAW,GACXC,KAAQ,GAGRC,MAAS,GACTC,eAAgB,GAChBC,YAAa,GACbC,YAAa,GACbC,cAAe,GACfC,cAAe,GACfC,iBAAkB,GAClBC,gBAAiB,GACjBC,eAAgB,GAChBC,gBAAiB,GAGjBC,OAAU,GACVC,qBAAsB,GACtBC,gBAAiB,GACjBC,cAAe,GACfC,mBAAoB,GACpBC,aAAc,GAGdC,IAAO,GACPC,yBAA0B,GAC1BC,0BAA2B,GAC3BC,oCAAqC,GACrCC,8DAA+D,GAG/DC,OAAU,GACVC,MAAS,GACTC,KAAQ,GACRC,MAAS,GACTC,oCAAqC,GACrCC,sCAAuC,GACvCC,6CAA8C,GAC9CC,2EAA4E,GAC5EC,uFAAwF,GACxFC,oCAAqC,GACrCC,wBAAyB,GACzBC,+EAAgF,GAChFC,aAAc,GACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAGRC,8EAA+E,GAC/EC,8CAA+C,GAC/CC,mDAAoD,GACpDC,oDAAqD,GACrDC,wEAAyE,GACzEC,2CAA4C,GAC5CC,iDAAkD,GAClDC,iDAAkD,GAClDC,gBAAiB,GACjBC,UAAW,GACXC,QAAS,GAGTC,mCAAoC,GACpCC,yBAA0B,GAC1BC,0BAA2B,GAC3BC,eAAgB", "file": "./lang/en_US.js", "sourcesContent": ["import m from \"../../es2015/lang/en_US\";\nwindow.am4lang_en_US = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/en_US.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n * \n * Locale: en_US\n * Language: U.S. English\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// number formatter related\n\t\"_decimalSeparator\": \".\",\n\t\"_thousandSeparator\": \",\",\n\n\t// Default date formats for various periods\n\t\"_date_millisecond\": \"mm::ss SSS\",\n\t\"_date_second\": \"hh:mm:ss a\",\n\t\"_date_minute\": \"hh:mm a\",\n\t\"_date_hour\": \"hh:mm a\",\n\t\"_date_day\": \"MMM dd\",\n\t\"_date_week\": \"ww\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era\n\t\"_era_ad\": \"AD\",\n\t\"_era_bc\": \"BC\",\n\n\t// Period\n\t\"A\": \"\",\n\t\"P\": \"\",\n\t\"AM\": \"\",\n\t\"PM\": \"\",\n\t\"A.M.\": \"\",\n\t\"P.M.\": \"\",\n\n\t// Dates\n\t\"January\": \"\",\n\t\"February\": \"\",\n\t\"March\": \"\",\n\t\"April\": \"\",\n\t\"May\": \"\",\n\t\"June\": \"\",\n\t\"July\": \"\",\n\t\"August\": \"\",\n\t\"September\": \"\",\n\t\"October\": \"\",\n\t\"November\": \"\",\n\t\"December\": \"\",\n\t\"Jan\": \"\",\n\t\"Feb\": \"\",\n\t\"Mar\": \"\",\n\t\"Apr\": \"\",\n\t\"May(short)\": \"May\",\n\t\"Jun\": \"\",\n\t\"Jul\": \"\",\n\t\"Aug\": \"\",\n\t\"Sep\": \"\",\n\t\"Oct\": \"\",\n\t\"Nov\": \"\",\n\t\"Dec\": \"\",\n\t\"Sunday\": \"\",\n\t\"Monday\": \"\",\n\t\"Tuesday\": \"\",\n\t\"Wednesday\": \"\",\n\t\"Thursday\": \"\",\n\t\"Friday\": \"\",\n\t\"Saturday\": \"\",\n\t\"Sun\": \"\",\n\t\"Mon\": \"\",\n\t\"Tue\": \"\",\n\t\"Wed\": \"\",\n\t\"Thu\": \"\",\n\t\"Fri\": \"\",\n\t\"Sat\": \"\",\n\n\t// ordinal function\n\t\"_dateOrd\": function(day: number): string {\n\t\tlet res = \"th\";\n\t\tif ((day < 11) || (day > 13)) {\n\t\t\tswitch (day % 10) {\n\t\t\t\tcase 1:\n\t\t\t\t\tres = \"st\";\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tres = \"nd\";\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tres = \"rd\"\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn res;\n\t},\n\n\t// Chart elements\n\t\"Play\": \"\",\n\t\"Stop\": \"\",\n\t\"Zoom Out\": \"\",\n\t\"Legend\": \"\",\n\t\"Click, tap or press ENTER to toggle\": \"\",\n\t\"Loading\": \"\",\n\t\"Home\": \"\",\n\n\t// Chart types\n\t\"Chart\": \"\",\n\t\"Serial chart\": \"\",\n\t\"X/Y chart\": \"\",\n\t\"Pie chart\": \"\",\n\t\"Gauge chart\": \"\",\n\t\"Radar chart\": \"\",\n\t\"Sankey diagram\": \"\",\n\t\"Chord diagram\": \"\",\n\t\"Flow diagram\": \"\",\n\t\"TreeMap chart\": \"\",\n\n\t// Series types\n\t\"Series\": \"\",\n\t\"Candlestick Series\": \"\",\n\t\"Column Series\": \"\",\n\t\"Line Series\": \"\",\n\t\"Pie Slice Series\": \"\",\n\t\"X/Y Series\": \"\",\n\n\t// Map-related\n\t\"Map\": \"\",\n\t\"Press ENTER to zoom in\": \"\",\n\t\"Press ENTER to zoom out\": \"\",\n\t\"Use arrow keys to zoom in and out\": \"\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"\",\n\n\t// Export-related\n\t\"Export\": \"\",\n\t\"Image\": \"\",\n\t\"Data\": \"\",\n\t\"Print\": \"\",\n\t\"Click, tap or press ENTER to open\": \"\",\n\t\"Click, tap or press ENTER to print.\": \"\",\n\t\"Click, tap or press ENTER to export as %1.\": \"\",\n\t'To save the image, right-click this link and choose \"Save picture as...\"': \"\",\n\t'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': \"\",\n\t\"(Press ESC to close this message)\": \"\",\n\t\"Image Export Complete\": \"\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"\",\n\t\"Saved from\": \"\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\n\t// Scrollbar-related\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"\",\n\t\"Use left and right arrows to move selection\": \"\",\n\t\"Use left and right arrows to move left selection\": \"\",\n\t\"Use left and right arrows to move right selection\": \"\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"\",\n\t\"Use up and down arrows to move selection\": \"\",\n\t\"Use up and down arrows to move lower selection\": \"\",\n\t\"Use up and down arrows to move upper selection\": \"\",\n\t\"From %1 to %2\": \"\",\n\t\"From %1\": \"\",\n\t\"To %1\": \"\",\n\n\t// Data loader-related\n\t\"No parser available for file: %1\": \"\",\n\t\"Error parsing file: %1\": \"\",\n\t\"Unable to load file: %1\": \"\",\n\t\"Invalid date\": \"\",\n};\n\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/en_US.ts"], "sourceRoot": ""}