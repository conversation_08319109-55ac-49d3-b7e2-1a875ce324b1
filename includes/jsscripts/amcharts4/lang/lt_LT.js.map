{"version": 3, "sources": ["webpack:///./lang/lt_LT.js", "webpack:///../../../src/lang/lt_LT.ts"], "names": ["window", "am4lang_lt_LT", "_decimalSeparator", "_thousandSeparator", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_second", "_duration_minute", "_duration_hour", "_duration_day", "_duration_week", "_duration_month", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Chord diagram", "Flow diagram", "TreeMap chart", "Series", "Candlestick Series", "Column Series", "Line Series", "Pie Slice Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;sHACAA,OAAAC,eCSCC,kBAAqB,IACrBC,mBAAsB,IAGtBC,kBAAqB,aACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,aACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OAGdC,sBAAyB,MACzBC,iBAAoB,KACpBC,iBAAoB,KACpBC,eAAkB,KAClBC,cAAiB,KACjBC,eAAkB,KAClBC,gBAAmB,KACnBC,eAAkB,OAGlBC,QAAW,OACXC,QAAW,SAGXC,EAAK,IACLC,EAAK,IACLC,GAAM,OACNC,GAAM,SACNC,OAAQ,OACRC,OAAQ,SAGRC,QAAW,SACXC,SAAY,UACZC,MAAS,OACTC,MAAS,YACTC,IAAO,UACPC,KAAQ,WACRC,KAAQ,SACRC,OAAU,YACVC,UAAa,UACbC,QAAW,SACXC,SAAY,YACZC,SAAY,WACZC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,aAAc,OACdC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,QACPC,OAAU,cACVC,OAAU,cACVC,QAAW,cACXC,UAAa,eACbC,SAAY,iBACZC,OAAU,eACVC,SAAY,cACZC,IAAO,QACPC,IAAO,QACPC,IAAO,QACPC,IAAO,QACPC,IAAO,QACPC,IAAO,QACPC,IAAO,QAGPC,SAAY,SAASC,GACpB,MAAO,SAIRC,WAAY,eACZC,KAAQ,WACRC,KAAQ,aACRC,OAAU,UACVC,sCAAuC,mEACvCC,QAAW,WACXC,KAAQ,UAGRC,MAAS,WACTC,eAAgB,qBAChBC,YAAa,eACbC,YAAa,uBACbC,cAAe,yBACfC,cAAe,uBACfC,iBAAkB,kBAClBC,gBAAiB,iBACjBC,eAAgB,gBAChBC,gBAAiB,mBAGjBC,OAAU,SACVC,qBAAsB,oCACtBC,gBAAiB,6BACjBC,cAAe,2BACfC,mBAAoB,qBACpBC,aAAc,aAGdC,IAAO,YACPC,yBAA0B,6CAC1BC,0BAA2B,6CAC3BC,oCAAqC,2DACrCC,8DAA+D,sGAG/DC,OAAU,cACVC,MAAS,gBACTC,KAAQ,WACRC,MAAS,aACTC,oCAAqC,uDACrCC,sCAAuC,yDACvCC,6CAA8C,kEAC9CC,2EAA4E,kHAC5EC,oCAAqC,iDACrCC,wBAAyB,iCACzBC,+EAAgF,mEAChFC,aAAc,eACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAGRC,8EAA+E,gHAC/EC,8CAA+C,mEAC/CC,mDAAoD,qEACpDC,oDAAqD,sEACrDC,wEAAyE,iHACzEC,2CAA4C,oEAC5CC,iDAAkD,wEAClDC,iDAAkD,0EAClDC,gBAAiB,gBACjBC,UAAW,SACXC,QAAS,SAGTC,mCAAoC,0CACpCC,yBAA0B,iCAC1BC,0BAA2B,6BAC3BC,eAAgB", "file": "./lang/lt_LT.js", "sourcesContent": ["import m from \"../../es2015/lang/lt_LT\";\nwindow.am4lang_lt_LT = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/lt_LT.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n * \n * Locale: lt_LT\n * Language: Lithuanian\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// number formatter related\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \" \",\n\n\t// Default date formats for various periods\n\t\"_date_millisecond\": \"mm::ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_day\": \"yyyy-MM-dd\",\n\t\"_date_week\": \"ww\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era\n\t\"_era_ad\": \"m.e.\",\n\t\"_era_bc\": \"p.m.e.\",\n\n\t// Period\n\t\"A\": \"R\",\n\t\"P\": \"V\",\n\t\"AM\": \"ryto\",\n\t\"PM\": \"vakaro\",\n\t\"A.M.\": \"ryto\",\n\t\"P.M.\": \"vakaro\",\n\n\t// Dates\n\t\"January\": \"sausio\",\n\t\"February\": \"vasario\",\n\t\"March\": \"kovo\",\n\t\"April\": \"balandžio\",\n\t\"May\": \"gegužės\",\n\t\"June\": \"birželio\",\n\t\"July\": \"liepos\",\n\t\"August\": \"rugpjūčio\",\n\t\"September\": \"rugsėjo\",\n\t\"October\": \"spalio\",\n\t\"November\": \"lapkričio\",\n\t\"December\": \"gruodžio\",\n\t\"Jan\": \"sau.\",\n\t\"Feb\": \"vas.\",\n\t\"Mar\": \"kov.\",\n\t\"Apr\": \"bal.\",\n\t\"May(short)\": \"geg.\",\n\t\"Jun\": \"bir.\",\n\t\"Jul\": \"lie.\",\n\t\"Aug\": \"rgp.\",\n\t\"Sep\": \"rgs.\",\n\t\"Oct\": \"spa.\",\n\t\"Nov\": \"lap.\",\n\t\"Dec\": \"gruo.\",\n\t\"Sunday\": \"sekmadienis\",\n\t\"Monday\": \"pirmadienis\",\n\t\"Tuesday\": \"antradienis\",\n\t\"Wednesday\": \"trečiadienis\",\n\t\"Thursday\": \"ketvirtadienis\",\n\t\"Friday\": \"penktadienis\",\n\t\"Saturday\": \"šeštadienis\",\n\t\"Sun\": \"sekm.\",\n\t\"Mon\": \"pirm.\",\n\t\"Tue\": \"antr.\",\n\t\"Wed\": \"treč.\",\n\t\"Thu\": \"ketv.\",\n\t\"Fri\": \"penk.\",\n\t\"Sat\": \"šešt.\",\n\n\t// ordinal function\n\t\"_dateOrd\": function(day: number): string {\n\t\treturn \"-a(s)\";\n\t},\n\n\t// Chart elements\n\t\"Zoom Out\": \"Rodyti viską\",\n\t\"Play\": \"Paleisti\",\n\t\"Stop\": \"Sustabdyti\",\n\t\"Legend\": \"Legenda\",\n\t\"Click, tap or press ENTER to toggle\": \"Spragtelkite, palieskite arba spauskite ENTER, kad perjungtumėte\",\n\t\"Loading\": \"Kraunama\",\n\t\"Home\": \"Pradžia\",\n\n\t// Chart types\n\t\"Chart\": \"Grafikas\",\n\t\"Serial chart\": \"Serijinis grafikas\",\n\t\"X/Y chart\": \"X/Y grafikas\",\n\t\"Pie chart\": \"Pyrago tipo grafikas\",\n\t\"Gauge chart\": \"Daviklio tipo grafikas\",\n\t\"Radar chart\": \"Radaro tipo grafikas\",\n\t\"Sankey diagram\": \"Sankey diagrama\",\n\t\"Chord diagram\": \"Chord diagrama\",\n\t\"Flow diagram\": \"Flow diagrama\",\n\t\"TreeMap chart\": \"TreeMap grafikas\",\n\n\t// Series types\n\t\"Series\": \"Serija\",\n\t\"Candlestick Series\": \"\\\"Candlestick\\\" tipo grafiko serija\",\n\t\"Column Series\": \"Stulpelinio grafiko serija\",\n\t\"Line Series\": \"Linijinio grafiko serija\",\n\t\"Pie Slice Series\": \"Pyrago tipo serija\",\n\t\"X/Y Series\": \"X/Y serija\",\n\n\t// Map-related\n\t\"Map\": \"Žemėlapis\",\n\t\"Press ENTER to zoom in\": \"Spauskite ENTER, kad pritrauktumėte vaizdą\",\n\t\"Press ENTER to zoom out\": \"Spauskite ENTER, kad atitolintumėte vaizdą\",\n\t\"Use arrow keys to zoom in and out\": \"Naudokitės royklėmis vaizdo pritraukimui ar atitolinimui\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Spauskite pliuso arba minuso klavišus ant klaviatūros, kad pritrautumėte arba atitolintumėte vaizdą\",\n\n\t// Export-related\n\t\"Export\": \"Eksportuoti\",\n\t\"Image\": \"Paveiksliukas\",\n\t\"Data\": \"Duomenys\",\n\t\"Print\": \"Spausdinti\",\n\t\"Click, tap or press ENTER to open\": \"Spragtelkite arba spauskite ENTER, kad atidarytumėte\",\n\t\"Click, tap or press ENTER to print.\": \"Spragtelkite arba spauskite ENTER, kad spausdintumėte.\",\n\t\"Click, tap or press ENTER to export as %1.\": \"Spragtelkite arba spauskite ENTER, kad eksportuotumėte kaip %1.\",\n\t'To save the image, right-click this link and choose \"Save picture as...\"': 'Kad išsaugotumėte paveiksliuką, spauskite dešinį pelės klavišą ir pasirinkite \"Išsaugoti, kaip paveiksliuką...\"',\n\t\"(Press ESC to close this message)\": \"(Spauskite ESC, kad uždarytumėte šį pranešimą)\",\n\t\"Image Export Complete\": \"Paveiksliuko eksportas baigtas\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"Eksportas užtruko ilgiau negu turėtų. Greičiausiai įvyko klaida.\",\n\t\"Saved from\": \"Išsaugota iš\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\n\t// Scrollbar-related\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"Spauskite TAB klavišą, kad pasirinktumėte žymeklius, arba kairė/dešinė klavišus, kad pakeistumėte pasirinkimą\",\n\t\"Use left and right arrows to move selection\": \"Naudokitės klavišais kairė/dešinė, kad pajudintumėte pasirinkimą\",\n\t\"Use left and right arrows to move left selection\": \"Naudokitės klavišais kairė/dešinė, kad pajudintumėte kairį žymeklį\",\n\t\"Use left and right arrows to move right selection\": \"Naudokitės klavišais kairė/dešinė, kad pajudintumėte dešinį žymeklį\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"Spauskite TAB klavišą, kad pasirinktumėte žymeklius, arba aukštyn/žemyn klavišus, kad pakeistumėte pasirinkimą\",\n\t\"Use up and down arrows to move selection\": \"Naudokitės klavišais aukštyn/žemyn, kad pajudintumėte pasirinkimą\",\n\t\"Use up and down arrows to move lower selection\": \"Naudokitės klavišais aukštyn/žemyn, kad pajudintumėte apatinį žymeklį\",\n\t\"Use up and down arrows to move upper selection\": \"Naudokitės klavišais aukštyn/žemyn, kad pajudintumėte viršutinį žymeklį\",\n\t\"From %1 to %2\": \"Nuo %1 iki %2\",\n\t\"From %1\": \"Nuo %1\",\n\t\"To %1\": \"Iki %1\",\n\n\t// Data loader-related\n\t\"No parser available for file: %1\": \"Failui %1 neturime tinkamo dešifruotojo\",\n\t\"Error parsing file: %1\": \"Skaitant failą %1 įvyko klaida\",\n\t\"Unable to load file: %1\": \"Nepavyko užkrauti failo %1\",\n\t\"Invalid date\": \"Klaidinga data\",\n\n};\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/lt_LT.ts"], "sourceRoot": ""}