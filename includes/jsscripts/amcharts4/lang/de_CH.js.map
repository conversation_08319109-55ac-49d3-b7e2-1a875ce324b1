{"version": 3, "sources": ["webpack:///./lang/de_CH.js", "webpack:///../../../src/lang/de_CH.ts"], "names": ["window", "am4lang_de_CH", "_decimalSeparator", "_thousandSeparator", "_big_number_suffix_3", "_big_number_suffix_6", "_big_number_suffix_9", "_big_number_suffix_12", "_big_number_suffix_15", "_big_number_suffix_18", "_big_number_suffix_21", "_big_number_suffix_24", "_small_number_suffix_3", "_small_number_suffix_6", "_small_number_suffix_9", "_small_number_suffix_12", "_small_number_suffix_15", "_small_number_suffix_18", "_small_number_suffix_21", "_small_number_suffix_24", "_byte_suffix_B", "_byte_suffix_KB", "_byte_suffix_MB", "_byte_suffix_GB", "_byte_suffix_TB", "_byte_suffix_PB", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_second", "_duration_minute", "_duration_hour", "_duration_day", "_duration_week", "_duration_month", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Chord diagram", "Flow diagram", "TreeMap chart", "Series", "Candlestick Series", "Column Series", "Line Series", "Pie Slice Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;wHACAA,OAAAC,eC6DIC,kBAAqB,IACrBC,mBAAsB,IAUtBC,qBAAwB,IACxBC,qBAAwB,MACxBC,qBAAwB,MACxBC,sBAAyB,MACzBC,sBAAyB,MACzBC,sBAAyB,QACzBC,sBAAyB,MACzBC,sBAAyB,IAEzBC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAE3BC,eAAkB,IAClBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KAWnBC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,UACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OASdC,sBAAyB,MACzBC,iBAAoB,KACpBC,iBAAoB,KACpBC,eAAkB,KAClBC,cAAiB,KACjBC,eAAkB,KAClBC,gBAAmB,KACnBC,eAAkB,OAGlBC,QAAW,UACXC,QAAW,UAUXC,EAAK,GACLC,EAAK,GACLC,GAAM,GACNC,GAAM,GACNC,OAAQ,GACRC,OAAQ,GAaRC,QAAW,SACXC,SAAY,UACZC,MAAS,OACTC,MAAS,QACTC,IAAO,MACPC,KAAQ,OACRC,KAAQ,OACRC,OAAU,SACVC,UAAa,YACbC,QAAW,UACXC,SAAY,WACZC,SAAY,WACZC,IAAO,OACPC,IAAO,QACPC,IAAO,OACPC,IAAO,OACPC,aAAc,MACdC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,QACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OAIPC,OAAU,UACVC,OAAU,SACVC,QAAW,WACXC,UAAa,WACbC,SAAY,aACZC,OAAU,UACVC,SAAY,UACZC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAWPC,SAAY,SAAUC,GAClB,OAAOA,EAAM,KAKjBC,WAAY,eAGZC,KAAQ,YACRC,KAAQ,OAGRC,OAAU,UAGVC,sCAAuC,oDAGvCC,QAAW,eAIXC,KAAQ,OAKRC,MAAS,WACTC,eAAgB,iBAChBC,YAAa,eACbC,YAAa,gBACbC,cAAe,eACfC,cAAe,eACfC,iBAAkB,kBAClBC,gBAAiB,GACjBC,eAAgB,gBAChBC,gBAAiB,eAKjBC,OAAU,QACVC,qBAAsB,iBACtBC,gBAAiB,iBACjBC,cAAe,iBACfC,mBAAoB,gBACpBC,aAAc,gBAGdC,IAAO,QACPC,yBAA0B,gCAC1BC,0BAA2B,gCAC3BC,oCAAqC,qCACrCC,8DAA+D,2CAY/DC,OAAU,SACVC,MAAS,OACTC,KAAQ,QACRC,MAAS,UACTC,oCAAqC,gDACrCC,sCAAuC,kDACvCC,6CAA8C,8DAC9CC,2EAA4E,mFAC5EC,uFAAwF,4GACxFC,oCAAqC,8CACrCC,wBAAyB,sBACzBC,+EAAgF,6EAChFC,aAAc,kBACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAYRC,8EAA+E,mGAC/EC,8CAA+C,mEAC/CC,mDAAoD,yEACpDC,oDAAqD,0EACrDC,wEAAyE,+GACzEC,2CAA4C,wEAC5CC,iDAAkD,+EAClDC,iDAAkD,8EAClDC,gBAAiB,gBACjBC,UAAW,SACXC,QAAS,SAGTC,mCAAoC,qCACpCC,yBAA0B,kCAC1BC,0BAA2B,uCAC3BC,eAAgB", "file": "./lang/de_CH.js", "sourcesContent": ["import m from \"../../es2015/lang/de_CH\";\nwindow.am4lang_de_CH = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/de_CH.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n *\n * Locale: de_CH\n * Language: German\n * Country: Switzerland\n * Author: <PERSON> (@chnoch)\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n *\n * ---\n * Edit but leave the header section above this line. You can remove any\n * subsequent comment sections.\n * ---\n *\n * Use this file as a template to create translations. Leave the key part in\n * English intact. Fill the value with a translation.\n *\n * Empty string means no translation, so default \"International English\"\n * will be used.\n *\n * If you need the translation to literally be an empty string, use `null`\n * instead.\n *\n * IMPORTANT:\n * When translating make good effort to keep the translation length\n * at least the same chartcount as the English, especially for short prompts.\n *\n * Having significantly longer prompts may distort the actual charts.\n *\n * NOTE:\n * Some prompts - like months or weekdays - come in two versions: full and\n * shortened.\n *\n * If there's no official shortened version of these in your language, and it\n * would not be possible to invent such short versions that don't seem weird\n * to native speakers of that language, fill those with the same as full\n * version.\n *\n * PLACEHOLDERS:\n * Some prompts have placeholders like \"%1\". Those will be replaced by actual\n * values during translation and should be retained in the translated prompts.\n *\n * Placeholder positions may be changed to better suit structure of the\n * sentence.\n *\n * For example \"From %1 to %2\", when actually used will replace \"%1\" with an\n * actual value representing range start, and \"%2\" will be replaced by end\n * value.\n *\n * E.g. in a Scrollbar for Value axis \"From %1 to %2\" will become\n * \"From 100 to 200\". You may translate \"From\" and \"to\", as well as re-arrange\n * the order of the prompt itself, but make sure the \"%1\" and \"%2\" remain, in\n * places where they will make sense.\n *\n * Save the file as language_LOCALE, i.e. `en_GB.ts`, `fr_FR.ts`, etc.\n */\nexport default {\n    // Number formatting options.\n    //\n    // Please check with the local standards which separator is accepted to be\n    // used for separating decimals, and which for thousands.\n    \"_decimalSeparator\": \".\",\n    \"_thousandSeparator\": \"'\",\n\n    // Suffixes for numbers\n    // When formatting numbers, big or small numers might be reformatted to\n    // shorter version, by applying a suffix.\n    //\n    // For example, 1000000 might become \"1m\".\n    // Or 1024 might become \"1KB\" if we're formatting byte numbers.\n    //\n    // This section defines such suffixes for all such cases.\n    \"_big_number_suffix_3\": \"K\",\n    \"_big_number_suffix_6\": \"Mio\",\n    \"_big_number_suffix_9\": \"Mrd\",\n    \"_big_number_suffix_12\": \"Bio\",\n    \"_big_number_suffix_15\": \"Brd\",\n    \"_big_number_suffix_18\": \"Trill\",\n    \"_big_number_suffix_21\": \"Trd\",\n    \"_big_number_suffix_24\": \"Y\",\n\n    \"_small_number_suffix_3\": \"m\",\n    \"_small_number_suffix_6\": \"μ\",\n    \"_small_number_suffix_9\": \"n\",\n    \"_small_number_suffix_12\": \"p\",\n    \"_small_number_suffix_15\": \"f\",\n    \"_small_number_suffix_18\": \"a\",\n    \"_small_number_suffix_21\": \"z\",\n    \"_small_number_suffix_24\": \"y\",\n\n    \"_byte_suffix_B\": \"B\",\n    \"_byte_suffix_KB\": \"KB\",\n    \"_byte_suffix_MB\": \"MB\",\n    \"_byte_suffix_GB\": \"GB\",\n    \"_byte_suffix_TB\": \"TB\",\n    \"_byte_suffix_PB\": \"PB\",\n\n    // Default date formats for various periods.\n    //\n    // This should reflect official or de facto formatting universally accepted\n    // in the country translation is being made for\n    // Available format codes here:\n    // https://www.amcharts.com/docs/v4/concepts/formatters/formatting-date-time/#Format_codes\n    //\n    // This will be used when formatting date/time for particular granularity,\n    // e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n    \"_date_millisecond\": \"mm:ss SSS\",\n    \"_date_second\": \"HH:mm:ss\",\n    \"_date_minute\": \"HH:mm\",\n    \"_date_hour\": \"HH:mm\",\n    \"_date_day\": \"dd. MMM\",\n    \"_date_week\": \"ww\",\n    \"_date_month\": \"MMM\",\n    \"_date_year\": \"yyyy\",\n\n    // Default duration formats for various base units.\n    //\n    // This will be used by DurationFormatter to format numeric values into\n    // duration.\n    //\n    // Available codes here:\n    // https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n    \"_duration_millisecond\": \"SSS\",\n    \"_duration_second\": \"ss\",\n    \"_duration_minute\": \"mm\",\n    \"_duration_hour\": \"hh\",\n    \"_duration_day\": \"dd\",\n    \"_duration_week\": \"ww\",\n    \"_duration_month\": \"MM\",\n    \"_duration_year\": \"yyyy\",\n\n    // Era translations\n    \"_era_ad\": \"v. Chr.\",\n    \"_era_bc\": \"n. Chr.\",\n\n    // Day part, used in 12-hour formats, e.g. 5 P.M.\n    // Please note that these come in 3 variants:\n    // * one letter (e.g. \"A\")\n    // * two letters (e.g. \"AM\")\n    // * two letters with dots (e.g. \"A.M.\")\n    //\n    // All three need to to be translated even if they are all the same. Some\n    // users might use one, some the other.\n    \"A\": \"\",\n    \"P\": \"\",\n    \"AM\": \"\",\n    \"PM\": \"\",\n    \"A.M.\": \"\",\n    \"P.M.\": \"\",\n\n    // Date-related stuff.\n    //\n    // When translating months, if there's a difference, use the form which is\n    // best for a full date, e.g. as you would use it in \"2018 January 1\".\n    //\n    // Note that May is listed twice. This is because in English May is the same\n    // in both long and short forms, while in other languages it may not be the\n    // case. Translate \"May\" to full word, while \"May(short)\" to shortened\n    // version.\n    //\n    // Short versions follow the guidelines of the the german \"Duden\" (https://de.wikipedia.org/wiki/Monat#Kurzformen)\n    \"January\": \"Januar\",\n    \"February\": \"Februar\",\n    \"March\": \"März\",\n    \"April\": \"April\",\n    \"May\": \"Mai\",\n    \"June\": \"Juni\",\n    \"July\": \"Juli\",\n    \"August\": \"August\",\n    \"September\": \"September\",\n    \"October\": \"Oktober\",\n    \"November\": \"November\",\n    \"December\": \"Dezember\",\n    \"Jan\": \"Jan.\",\n    \"Feb\": \"Febr.\",\n    \"Mar\": \"März\",\n    \"Apr\": \"Apr.\",\n    \"May(short)\": \"Mai\",\n    \"Jun\": \"Juni\",\n    \"Jul\": \"Juli\",\n    \"Aug\": \"Aug.\",\n    \"Sep\": \"Sept.\",\n    \"Oct\": \"Okt.\",\n    \"Nov\": \"Nov.\",\n    \"Dec\": \"Dez.\",\n\n    // Weekdays.\n    // Short versions follow the guidelines of the the german \"Duden\"\n    \"Sunday\": \"Sonntag\",\n    \"Monday\": \"Montag\",\n    \"Tuesday\": \"Dienstag\",\n    \"Wednesday\": \"Mittwoch\",\n    \"Thursday\": \"Donnerstag\",\n    \"Friday\": \"Freitag\",\n    \"Saturday\": \"Samstag\",\n    \"Sun\": \"So.\",\n    \"Mon\": \"Mo.\",\n    \"Tue\": \"Di.\",\n    \"Wed\": \"Mi.\",\n    \"Thu\": \"Do.\",\n    \"Fri\": \"Fr.\",\n    \"Sat\": \"Sa.\",\n\n    // Date ordinal function.\n    //\n    // This is used when adding number ordinal when formatting days in dates.\n    //\n    // E.g. \"January 1st\", \"February 2nd\".\n    //\n    // The function accepts day number, and returns a string to be added to the\n    // day, like in default English translation, if we pass in 2, we will receive\n    // \"nd\" back.\n    \"_dateOrd\": function (day: number): string {\n        return day + '.';\n    },\n\n    // Various chart controls.\n    // Shown as a tooltip on zoom out button.\n    \"Zoom Out\": \"Herauszoomen\",\n\n    // Timeline buttons\n    \"Play\": \"Abspielen\",\n    \"Stop\": \"Stop\",\n\n    // Chart's Legend screen reader title.\n    \"Legend\": \"Legende\",\n\n    // Legend's item screen reader indicator.\n    \"Click, tap or press ENTER to toggle\": \"Klicken, tippen oder ENTER drücken zum Umschalten\",\n\n    // Shown when the chart is busy loading something.\n    \"Loading\": \"Wird geladen\",\n\n    // Shown as the first button in the breadcrumb navigation, e.g.:\n    // Home > First level > ...\n    \"Home\": \"Home\",\n\n    // Chart types.\n    // Those are used as default screen reader titles for the main chart element\n    // unless developer has set some more descriptive title.\n    \"Chart\": \"Diagramm\",\n    \"Serial chart\": \"Seriendiagramm\",\n    \"X/Y chart\": \"X-Y-Diagramm\",\n    \"Pie chart\": \"Kreisdiagramm\",\n    \"Gauge chart\": \"Messdiagramm\",\n    \"Radar chart\": \"Netzdiagramm\",\n    \"Sankey diagram\": \"Sankey-Diagramm\",\n    \"Chord diagram\": \"\",\n    \"Flow diagram\": \"Flussdiagramm\",\n    \"TreeMap chart\": \"Baumdiagramm\",\n\n    // Series types.\n    // Used to name series by type for screen readers if they do not have their\n    // name set.\n    \"Series\": \"Serie\",\n    \"Candlestick Series\": \"Kerzendiagramm\",\n    \"Column Series\": \"Balkendiagramm\",\n    \"Line Series\": \"Liniendiagramm\",\n    \"Pie Slice Series\": \"Kreisdiagramm\",\n    \"X/Y Series\": \"Punktdiagramm\",\n\n    // Map-related stuff.\n    \"Map\": \"Karte\",\n    \"Press ENTER to zoom in\": \"Drücke ENTER zum Hereinzoomen\",\n    \"Press ENTER to zoom out\": \"Drücke ENTER zum Herauszoomen\",\n    \"Use arrow keys to zoom in and out\": \"Benutze die Pfeiltasten zum Zoomen\",\n    \"Use plus and minus keys on your keyboard to zoom in and out\": \"Benutze Plus- und Minustasten zum Zoomen\",\n\n    // Export-related stuff.\n    // These prompts are used in Export menu labels.\n    //\n    // \"Export\" is the top-level menu item.\n    //\n    // \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n    // operation.\n    //\n    // Leave actual format untranslated, unless you absolutely know that they\n    // would convey more meaning in some other way.\n    \"Export\": \"Export\",\n    \"Image\": \"Bild\",\n    \"Data\": \"Daten\",\n    \"Print\": \"Drucken\",\n    \"Click, tap or press ENTER to open\": \"Zum Öffnen klicken, tippen oder ENTER drücken\",\n    \"Click, tap or press ENTER to print.\": \"Zum Drucken klicken, tippen oder ENTER drücken.\",\n    \"Click, tap or press ENTER to export as %1.\": \"Klicken, tippen oder ENTER drücken um als %1 zu exportieren\",\n    'To save the image, right-click this link and choose \"Save picture as...\"': 'Um das Bild zu speichern, Rechtsklicken und \"Bild speichern unter ...\" auswählen',\n    'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': 'Um das Bild zu speichern, Rechtsklick auf das Vorschaubild links und \"Bild speichern unter ...\" auswählen',\n    \"(Press ESC to close this message)\": \"ESC drücken um diese Nachricht zu schließen\",\n    \"Image Export Complete\": \"Bildexport komplett\",\n    \"Export operation took longer than expected. Something might have gone wrong.\": \"Der Export dauert länger als geplant. Vielleicht ist etwas schiefgelaufen.\",\n    \"Saved from\": \"Gespeichert von\",\n    \"PNG\": \"\",\n    \"JPG\": \"\",\n    \"GIF\": \"\",\n    \"SVG\": \"\",\n    \"PDF\": \"\",\n    \"JSON\": \"\",\n    \"CSV\": \"\",\n    \"XLSX\": \"\",\n\n    // Scrollbar-related stuff.\n    //\n    // Scrollbar is a control which can zoom and pan the axes on the chart.\n    //\n    // Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n    // upper and lower (for vertical one).\n    //\n    // Prompts change in relation to whether Scrollbar is vertical or horizontal.\n    //\n    // The final section is used to indicate the current range of selection.\n    \"Use TAB to select grip buttons or left and right arrows to change selection\": \"TAB nutzen, um Ankerpunkte auszuwählen oder linke und rechte Pfeiltaste um die Auswahl zu ändern\",\n    \"Use left and right arrows to move selection\": \"Linke und rechte Pfeiltaste nutzen um die Auswahl zu verschieben\",\n    \"Use left and right arrows to move left selection\": \"Linke und rechte Pfeiltaste nutzen um die linke Auswahl zu verschieben\",\n    \"Use left and right arrows to move right selection\": \"Linke und rechte Pfeiltaste nutzen um die rechte Auswahl zu verschieben\",\n    \"Use TAB select grip buttons or up and down arrows to change selection\": \"TAB nutzen, um Ankerpunkte auszuwählen oder Pfeiltaste nach oben und unten drücken, um die Auswahl zu ändern\",\n    \"Use up and down arrows to move selection\": \"Pfeiltaste nach oben und unten drücken, um die Auswahl zu verschieben\",\n    \"Use up and down arrows to move lower selection\": \"Pfeiltaste nach oben und unten drücken, um die untere Auswahl zu verschieben\",\n    \"Use up and down arrows to move upper selection\": \"Pfeiltaste nach oben und unten drücken, um die obere Auswahl zu verschieben\",\n    \"From %1 to %2\": \"Von %1 bis %2\",\n    \"From %1\": \"Von %1\",\n    \"To %1\": \"Bis %1\",\n\n    // Data loader-related.\n    \"No parser available for file: %1\": \"Kein Parser für Datei %1 verfügbar\",\n    \"Error parsing file: %1\": \"Fehler beim Parsen von Datei %1\",\n    \"Unable to load file: %1\": \"Datei %1 konnte nicht geladen werden\",\n    \"Invalid date\": \"Kein Datum\",\n};\n\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/de_CH.ts"], "sourceRoot": ""}