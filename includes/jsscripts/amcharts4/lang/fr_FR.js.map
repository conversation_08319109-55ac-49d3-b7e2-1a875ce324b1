{"version": 3, "sources": ["webpack:///./lang/fr_FR.js", "webpack:///../../../src/lang/fr_FR.ts"], "names": ["window", "am4lang_fr_FR", "_decimalSeparator", "_thousandSeparator", "_big_number_suffix_3", "_big_number_suffix_6", "_big_number_suffix_9", "_big_number_suffix_12", "_big_number_suffix_15", "_big_number_suffix_18", "_big_number_suffix_21", "_big_number_suffix_24", "_small_number_suffix_3", "_small_number_suffix_6", "_small_number_suffix_9", "_small_number_suffix_12", "_small_number_suffix_15", "_small_number_suffix_18", "_small_number_suffix_21", "_small_number_suffix_24", "_byte_suffix_B", "_byte_suffix_KB", "_byte_suffix_MB", "_byte_suffix_GB", "_byte_suffix_TB", "_byte_suffix_PB", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_millisecond_second", "_duration_millisecond_minute", "_duration_millisecond_hour", "_duration_millisecond_day", "_duration_millisecond_week", "_duration_millisecond_month", "_duration_millisecond_year", "_duration_second", "_duration_second_minute", "_duration_second_hour", "_duration_second_day", "_duration_second_week", "_duration_second_month", "_duration_second_year", "_duration_minute", "_duration_minute_hour", "_duration_minute_day", "_duration_minute_week", "_duration_minute_month", "_duration_minute_year", "_duration_hour", "_duration_hour_day", "_duration_hour_week", "_duration_hour_month", "_duration_hour_year", "_duration_day", "_duration_day_week", "_duration_day_month", "_duration_day_year", "_duration_week", "_duration_week_month", "_duration_week_year", "_duration_month", "_duration_month_year", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "res", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Flow diagram", "Chord diagram", "TreeMap chart", "Sliced chart", "Series", "Candlestick Series", "OHLC Series", "Column Series", "Line Series", "Pie Slice Series", "Funnel Series", "Pyramid Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;sHACAA,OAAAC,eC4DCC,kBAAqB,IACrBC,mBAAsB,IAUtBC,qBAAwB,IACxBC,qBAAwB,IACxBC,qBAAwB,IACxBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IAEzBC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAE3BC,eAAkB,IAClBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KAWnBC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,SACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OAuBdC,sBAAyB,MACzBC,6BAAgC,SAChCC,6BAAgC,YAChCC,2BAA8B,eAC9BC,0BAA6B,iBAC7BC,2BAA8B,iBAC9BC,4BAA+B,uBAC/BC,2BAA8B,6BAE9BC,iBAAoB,KACpBC,wBAA2B,QAC3BC,sBAAyB,WACzBC,qBAAwB,gBACxBC,sBAAyB,gBACzBC,uBAA0B,sBAC1BC,sBAAyB,4BAEzBC,iBAAoB,KACpBC,sBAAyB,QACzBC,qBAAwB,aACxBC,sBAAyB,aACzBC,uBAA0B,mBAC1BC,sBAAyB,yBAEzBC,eAAkB,QAClBC,mBAAsB,aACtBC,oBAAuB,aACvBC,qBAAwB,mBACxBC,oBAAuB,yBAEvBC,cAAiB,OACjBC,mBAAsB,OACtBC,oBAAuB,aACvBC,mBAAsB,mBAEtBC,eAAkB,OAClBC,qBAAwB,OACxBC,oBAAuB,OAEvBC,gBAAmB,OACnBC,qBAAwB,aAExBC,eAAkB,OAGlBC,QAAW,KACXC,QAAW,KAUXC,EAAK,IACLC,EAAK,IACLC,GAAM,KACNC,GAAM,KACNC,OAAQ,OACRC,OAAQ,OAoBRC,QAAW,UACXC,SAAY,UACZC,MAAS,OACTC,MAAS,QACTC,IAAO,MACPC,KAAQ,OACRC,KAAQ,UACRC,OAAU,OACVC,UAAa,YACbC,QAAW,UACXC,SAAY,WACZC,SAAY,WACZC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,aAAc,MACdC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAGPC,OAAU,WACVC,OAAU,QACVC,QAAW,QACXC,UAAa,WACbC,SAAY,QACZC,OAAU,WACVC,SAAY,SACZC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAWPC,SAAY,SAAUC,GACrB,IAAIC,EAAM,IACV,GAAKD,EAAM,IAAQA,EAAM,GACxB,OAAQA,EAAM,IACb,KAAK,EACJC,EAAM,KAIT,OAAOA,GAKRC,WAAY,eAGZC,KAAQ,OACRC,KAAQ,SAGRC,OAAU,UAGVC,sCAAuC,uDAGvCC,QAAW,UAIXC,KAAQ,UAKRC,MAAS,YACTC,eAAgB,mBAChBC,YAAa,gBACbC,YAAa,YACbC,cAAe,kBACfC,cAAe,cACfC,iBAAkB,mBAClBC,eAAgB,6BAChBC,gBAAiB,qBACjBC,gBAAiB,mBACjBC,eAAgB,wBAKhBC,OAAU,SACVC,qBAAsB,oBACtBC,cAAe,cACfC,gBAAiB,qBACjBC,cAAe,kBACfC,mBAAoB,uBACpBC,gBAAiB,qBACjBC,iBAAkB,oBAClBC,aAAc,aAGdC,IAAO,QACPC,yBAA0B,gCAC1BC,0BAA2B,mDAC3BC,oCAAqC,wDACrCC,8DAA+D,8FAY/DC,OAAU,WACVC,MAAS,QACTC,KAAQ,OACRC,MAAS,WACTC,oCAAqC,oDACrCC,sCAAuC,sDACvCC,6CAA8C,+DAC9CC,2EAA4E,kHAC5EC,uFAAwF,6IACxFC,oCAAqC,2CACrCC,wBAAyB,+BACzBC,+EAAgF,iFAChFC,aAAc,gBACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAYRC,8EAA+E,gIAC/EC,8CAA+C,mEAC/CC,mDAAoD,0EACpDC,oDAAqD,0EACrDC,wEAAyE,0GACzEC,2CAA4C,8DAC5CC,iDAAkD,yEAClDC,iDAAkD,yEAClDC,gBAAiB,aACjBC,UAAW,QACXC,QAAS,OAGTC,mCAAoC,iDACpCC,yBAA0B,kCAC1BC,0BAA2B,uCAC3BC,eAAgB", "file": "./lang/fr_FR.js", "sourcesContent": ["import m from \"../../es2015/lang/fr_FR\";\nwindow.am4lang_fr_FR = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/fr_FR.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n * \n * Locale: fr_FR\n * Language: French\n * Author: <PERSON>\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n *\n * ---\n * Edit but leave the header section above this line. You can remove any\n * subsequent comment sections.\n * ---\n *\n * Use this file as a template to create translations. Leave the key part in\n * English intact. Fill the value with a translation.\n *\n * Empty string means no translation, so default \"International English\"\n * will be used.\n *\n * If you need the translation to literally be an empty string, use `null`\n * instead.\n *\n * IMPORTANT:\n * When translating make good effort to keep the translation length\n * at least the same chartcount as the English, especially for short prompts.\n *\n * Having significantly longer prompts may distort the actual charts.\n *\n * NOTE:\n * Some prompts - like months or weekdays - come in two versions: full and\n * shortened.\n *\n * If there's no official shortened version of these in your language, and it\n * would not be possible to invent such short versions that don't seem weird\n * to native speakers of that language, fill those with the same as full\n * version.\n *\n * PLACEHOLDERS:\n * Some prompts have placeholders like \"%1\". Those will be replaced by actual\n * values during translation and should be retained in the translated prompts.\n *\n * Placeholder positions may be changed to better suit structure of the\n * sentence.\n *\n * For example \"From %1 to %2\", when actually used will replace \"%1\" with an\n * actual value representing range start, and \"%2\" will be replaced by end\n * value.\n *\n * E.g. in a Scrollbar for Value axis \"From %1 to %2\" will become\n * \"From 100 to 200\". You may translate \"From\" and \"to\", as well as re-arrange\n * the order of the prompt itself, but make sure the \"%1\" and \"%2\" remain, in\n * places where they will make sense.\n *\n * Save the file as language_LOCALE, i.e. `en_GB.ts`, `fr_FR.ts`, etc.\n */\nexport default {\n\t// Number formatting options.\n\t// \n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \" \",\n\n\t// Suffixes for numbers\n\t// When formatting numbers, big or small numers might be reformatted to\n\t// shorter version, by applying a suffix.\n\t// \n\t// For example, 1000000 might become \"1m\".\n\t// Or 1024 might become \"1KB\" if we're formatting byte numbers.\n\t// \n\t// This section defines such suffixes for all such cases.\n\t\"_big_number_suffix_3\": \"k\",\n\t\"_big_number_suffix_6\": \"M\",\n\t\"_big_number_suffix_9\": \"G\",\n\t\"_big_number_suffix_12\": \"T\",\n\t\"_big_number_suffix_15\": \"P\",\n\t\"_big_number_suffix_18\": \"E\",\n\t\"_big_number_suffix_21\": \"Z\",\n\t\"_big_number_suffix_24\": \"Y\",\n\n\t\"_small_number_suffix_3\": \"m\",\n\t\"_small_number_suffix_6\": \"μ\",\n\t\"_small_number_suffix_9\": \"n\",\n\t\"_small_number_suffix_12\": \"p\",\n\t\"_small_number_suffix_15\": \"f\",\n\t\"_small_number_suffix_18\": \"a\",\n\t\"_small_number_suffix_21\": \"z\",\n\t\"_small_number_suffix_24\": \"y\",\n\n\t\"_byte_suffix_B\": \"B\",\n\t\"_byte_suffix_KB\": \"KB\",\n\t\"_byte_suffix_MB\": \"MB\",\n\t\"_byte_suffix_GB\": \"GB\",\n\t\"_byte_suffix_TB\": \"TB\",\n\t\"_byte_suffix_PB\": \"PB\",\n\n\t// Default date formats for various periods.\n\t// \n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-date-time/#Format_codes\n\t// \n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_day\": \"dd MMM\",\n\t\"_date_week\": \"ww\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t// \n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t// \n\t// Notice how each duration unit comes in several versions. This is to ensure\n\t// that each base unit is shown correctly.\n\t// \n\t// For example, if we have baseUnit set to \"second\", meaning our duration is\n\t// in seconds.\n\t// \n\t// If we pass in `50` to formatter, it will know that we have just 50 seconds\n\t// (less than a minute) so it will use format in `\"_duration_second\"` (\"ss\"),\n\t// and the formatted result will be in like `\"50\"`.\n\t// \n\t// If we pass in `70`, which is more than a minute, the formatter will switch\n\t// to `\"_duration_second_minute\"` (\"mm:ss\"), resulting in \"01:10\" formatted\n\t// text.\n\t// \n\t// Available codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_millisecond_second\": \"ss.SSS\",\n\t\"_duration_millisecond_minute\": \"mm:ss SSS\",\n\t\"_duration_millisecond_hour\": \"hh:mm:ss SSS\",\n\t\"_duration_millisecond_day\": \"d'd' mm:ss SSS\",\n\t\"_duration_millisecond_week\": \"d'd' mm:ss SSS\",\n\t\"_duration_millisecond_month\": \"M'm' dd'd' mm:ss SSS\",\n\t\"_duration_millisecond_year\": \"y'y' MM'm' dd'd' mm:ss SSS\",\n\n\t\"_duration_second\": \"ss\",\n\t\"_duration_second_minute\": \"mm:ss\",\n\t\"_duration_second_hour\": \"hh:mm:ss\",\n\t\"_duration_second_day\": \"d'd' hh:mm:ss\",\n\t\"_duration_second_week\": \"d'd' hh:mm:ss\",\n\t\"_duration_second_month\": \"M'm' dd'd' hh:mm:ss\",\n\t\"_duration_second_year\": \"y'y' MM'm' dd'd' hh:mm:ss\",\n\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_minute_hour\": \"hh:mm\",\n\t\"_duration_minute_day\": \"d'd' hh:mm\",\n\t\"_duration_minute_week\": \"d'd' hh:mm\",\n\t\"_duration_minute_month\": \"M'm' dd'd' hh:mm\",\n\t\"_duration_minute_year\": \"y'y' MM'm' dd'd' hh:mm\",\n\n\t\"_duration_hour\": \"hh'h'\",\n\t\"_duration_hour_day\": \"d'd' hh'h'\",\n\t\"_duration_hour_week\": \"d'd' hh'h'\",\n\t\"_duration_hour_month\": \"M'm' dd'd' hh'h'\",\n\t\"_duration_hour_year\": \"y'y' MM'm' dd'd' hh'h'\",\n\n\t\"_duration_day\": \"d'd'\",\n\t\"_duration_day_week\": \"d'd'\",\n\t\"_duration_day_month\": \"M'm' dd'd'\",\n\t\"_duration_day_year\": \"y'y' MM'm' dd'd'\",\n\n\t\"_duration_week\": \"w'w'\",\n\t\"_duration_week_month\": \"w'w'\",\n\t\"_duration_week_year\": \"w'w'\",\n\n\t\"_duration_month\": \"M'm'\",\n\t\"_duration_month_year\": \"y'y' MM'm'\",\n\n\t\"_duration_year\": \"y'y'\",\n\n\t// Era translations\n\t\"_era_ad\": \"AD\",\n\t\"_era_bc\": \"BC\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t// \n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"A\",\n\t\"P\": \"P\",\n\t\"AM\": \"AM\",\n\t\"PM\": \"PM\",\n\t\"A.M.\": \"A.M.\",\n\t\"P.M.\": \"P.M.\",\n\n\t// Date-related stuff.\n\t// \n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t// \n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t// \n\t// Should month names and weekdays be capitalized or not?\n\t// \n\t// Rule of thumb is this: if the names should always be capitalized,\n\t// regardless of name position within date (\"January\", \"21st January 2018\",\n\t// etc.) use capitalized names. Otherwise enter all lowercase.\n\t// \n\t// The date formatter will automatically capitalize names if they are the\n\t// first (or only) word in resulting date.\n\t\"January\": \"Janvier\",\n\t\"February\": \"Février\",\n\t\"March\": \"Mars\",\n\t\"April\": \"Avril\",\n\t\"May\": \"Mai\",\n\t\"June\": \"Juin\",\n\t\"July\": \"Juillet\",\n\t\"August\": \"Août\",\n\t\"September\": \"Septembre\",\n\t\"October\": \"Octobre\",\n\t\"November\": \"Novembre\",\n\t\"December\": \"Décembre\",\n\t\"Jan\": \"Jan\",\n\t\"Feb\": \"Fév\",\n\t\"Mar\": \"Mar\",\n\t\"Apr\": \"Avr\",\n\t\"May(short)\": \"Mai\",\n\t\"Jun\": \"Jui\",\n\t\"Jul\": \"Jul\",\n\t\"Aug\": \"Aoû\",\n\t\"Sep\": \"Sep\",\n\t\"Oct\": \"Oct\",\n\t\"Nov\": \"Nov\",\n\t\"Dec\": \"Déc\",\n\n\t// Weekdays.\n\t\"Sunday\": \"Dimanche\",\n\t\"Monday\": \"Lundi\",\n\t\"Tuesday\": \"Mardi\",\n\t\"Wednesday\": \"Mercredi\",\n\t\"Thursday\": \"Jeudi\",\n\t\"Friday\": \"Vendredi\",\n\t\"Saturday\": \"Samedi\",\n\t\"Sun\": \"Dim\",\n\t\"Mon\": \"Lun\",\n\t\"Tue\": \"Mar\",\n\t\"Wed\": \"Mer\",\n\t\"Thu\": \"Jeu\",\n\t\"Fri\": \"Ven\",\n\t\"Sat\": \"Sam\",\n\n\t// Date ordinal function.\n\t// \n\t// This is used when adding number ordinal when formatting days in dates.\n\t// \n\t// E.g. \"January 1st\", \"February 2nd\".\n\t// \n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function (day: number): string {\n\t\tlet res = \"e\";\n\t\tif ((day < 11) || (day > 13)) {\n\t\t\tswitch (day % 10) {\n\t\t\t\tcase 1:\n\t\t\t\t\tres = \"er\";\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn res;\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Zoom Arrière\",\n\n\t// Timeline buttons\n\t\"Play\": \"Joue\",\n\t\"Stop\": \"Arrête\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Légende\",\n\n\t// Legend's item screen reader indicator.\n\t\"Click, tap or press ENTER to toggle\": \"cliquez, appuyez ou appuyez sur entrée pour basculer\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Charger\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Accueil\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"Graphique\",\n\t\"Serial chart\": \"Graphique sérial\",\n\t\"X/Y chart\": \"Graphique X/Y\",\n\t\"Pie chart\": \"Camembert\",\n\t\"Gauge chart\": \"Jauge graphique\",\n\t\"Radar chart\": \"Carte radar\",\n\t\"Sankey diagram\": \"Graphique Sankey\",\n\t\"Flow diagram\": \"représentation schématique\",\n\t\"Chord diagram\": \"diagramme d'accord\",\n\t\"TreeMap chart\": \"carte de l'arbre\",\n\t\"Sliced chart\": \"graphique en tranches\",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"Séries\",\n\t\"Candlestick Series\": \"Séries chandelier\",\n\t\"OHLC Series\": \"Séries OHLC\",\n\t\"Column Series\": \"Séries de colonnes\",\n\t\"Line Series\": \"Série de lignes\",\n\t\"Pie Slice Series\": \"Tarte tranche Séries\",\n\t\"Funnel Series\": \"Séries d'entonnoir\",\n\t\"Pyramid Series\": \"Séries pyramidale\",\n\t\"X/Y Series\": \"Séries X/Y\",\n\n\t// Map-related stuff.\n\t\"Map\": \"Mappe\",\n\t\"Press ENTER to zoom in\": \"Appuyez sur ENTER pour zoomer\",\n\t\"Press ENTER to zoom out\": \"Appuyez sur ENTER pour effectuer un zoom arrière\",\n\t\"Use arrow keys to zoom in and out\": \"Utilisez les touches fléchées pour zoomer et dézoomer\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Utilisez les touches plus et moins de votre clavier pour effectuer un zoom avant ou arrière\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t// \n\t// \"Export\" is the top-level menu item.\n\t// \n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t// \n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Exporter\",\n\t\"Image\": \"Image\",\n\t\"Data\": \"Data\",\n\t\"Print\": \"Imprimer\",\n\t\"Click, tap or press ENTER to open\": \"Cliquez, appuyez ou appuyez sur ENTER pour ouvrir\",\n\t\"Click, tap or press ENTER to print.\": \"Cliquez, appuyez ou appuyez sur ENTER pour imprimer\",\n\t\"Click, tap or press ENTER to export as %1.\": \"Cliquez, appuyez ou appuyez sur ENTER pour exporter comme %1\",\n\t'To save the image, right-click this link and choose \"Save picture as...\"': \"Pour enregistrer l'image, cliquez avec le bouton droit sur ce lien et choisissez 'Enregistrer l'image sous ...'\",\n\t'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': \"'Pour enregistrer l'image, cliquez sur la vignette à gauche avec le bouton droit de la souris et choisissez 'Enregistrer l'image sous ...'\",\n\t\"(Press ESC to close this message)\": \"(Appuyez sur ESC pour fermer ce message)\",\n\t\"Image Export Complete\": \"Exportation d'image terminée\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"L'exportation a pris plus de temps que prévu. Quelque chose aurait mal tourné.\",\n\t\"Saved from\": \"Enregistré de\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\n\t// Scrollbar-related stuff.\n\t// \n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t// \n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t// \n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t// \n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"Utilisez la touche TAB pour sélectionner les boutons des poignées ou les flèches gauche et droite pour modifier la sélection.\",\n\t\"Use left and right arrows to move selection\": \"Utilisez les flèches gauche et droite pour déplacer la sélection\",\n\t\"Use left and right arrows to move left selection\": \"Utilisez les flèches gauche et droite pour déplacer la sélection gauche\",\n\t\"Use left and right arrows to move right selection\": \"Utilisez les flèches gauche et droite pour déplacer la sélection droite\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"Utilisez les boutons de sélection TAB ou les flèches vers le haut et le bas pour modifier la sélection.\",\n\t\"Use up and down arrows to move selection\": \"Utilisez les flèches haut et bas pour déplacer la sélection\",\n\t\"Use up and down arrows to move lower selection\": \"Utilisez les flèches haut et bas pour déplacer la sélection inférieure\",\n\t\"Use up and down arrows to move upper selection\": \"Utilisez les flèches haut et bas pour déplacer la sélection supérieure\",\n\t\"From %1 to %2\": \"De %1 à %2\",\n\t\"From %1\": \"De %1\",\n\t\"To %1\": \"à %1\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"Aucun analyseur disponible pour le fichier: %1\",\n\t\"Error parsing file: %1\": \"Erreur d'analyse du fichier: %1\",\n\t\"Unable to load file: %1\": \"Impossible de charger le fichier: %1\",\n\t\"Invalid date\": \"Date invalide\",\n};\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/fr_FR.ts"], "sourceRoot": ""}