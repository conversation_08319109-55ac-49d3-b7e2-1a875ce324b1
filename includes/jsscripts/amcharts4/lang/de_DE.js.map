{"version": 3, "sources": ["webpack:///./lang/de_DE.js", "webpack:///../../../src/lang/de_DE.ts"], "names": ["window", "am4lang_de_DE", "_decimalSeparator", "_thousandSeparator", "_big_number_suffix_3", "_big_number_suffix_6", "_big_number_suffix_9", "_big_number_suffix_12", "_big_number_suffix_15", "_big_number_suffix_18", "_big_number_suffix_21", "_big_number_suffix_24", "_small_number_suffix_3", "_small_number_suffix_6", "_small_number_suffix_9", "_small_number_suffix_12", "_small_number_suffix_15", "_small_number_suffix_18", "_small_number_suffix_21", "_small_number_suffix_24", "_byte_suffix_B", "_byte_suffix_KB", "_byte_suffix_MB", "_byte_suffix_GB", "_byte_suffix_TB", "_byte_suffix_PB", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_second", "_duration_minute", "_duration_hour", "_duration_day", "_duration_week", "_duration_month", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Chord diagram", "Flow diagram", "TreeMap chart", "Series", "Candlestick Series", "Column Series", "Line Series", "Pie Slice Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;sHACAA,OAAAC,eC4DCC,kBAAqB,IACrBC,mBAAsB,IAUnBC,qBAAwB,IACxBC,qBAAwB,MACxBC,qBAAwB,MACxBC,sBAAyB,MACzBC,sBAAyB,MACzBC,sBAAyB,QACzBC,sBAAyB,MACzBC,sBAAyB,IAEzBC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAE3BC,eAAkB,IAClBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KAWtBC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,UACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OASdC,sBAAyB,MACzBC,iBAAoB,KACpBC,iBAAoB,KACpBC,eAAkB,KAClBC,cAAiB,KACjBC,eAAkB,KAClBC,gBAAmB,KACnBC,eAAkB,OAGlBC,QAAW,UACXC,QAAW,UAUXC,EAAK,GACLC,EAAK,GACLC,GAAM,GACNC,GAAM,GACNC,OAAQ,GACRC,OAAQ,GAaRC,QAAW,SACXC,SAAY,UACZC,MAAS,OACTC,MAAS,QACTC,IAAO,MACPC,KAAQ,OACRC,KAAQ,OACRC,OAAU,SACVC,UAAa,YACbC,QAAW,UACXC,SAAY,WACZC,SAAY,WACZC,IAAO,OACPC,IAAO,QACPC,IAAO,OACPC,IAAO,OACPC,aAAc,MACdC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,QACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OAIPC,OAAU,UACVC,OAAU,SACVC,QAAW,WACXC,UAAa,WACbC,SAAY,aACZC,OAAU,UACVC,SAAY,UACZC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAWPC,SAAY,SAASC,GACjB,OAAOA,EAAM,KAKjBC,WAAY,eAGZC,KAAQ,YACRC,KAAQ,OAGRC,OAAU,UAGVC,sCAAuC,oDAGvCC,QAAW,eAIXC,KAAQ,OAKRC,MAAS,WACTC,eAAgB,iBAChBC,YAAa,eACbC,YAAa,gBACbC,cAAe,eACfC,cAAe,eACfC,iBAAkB,kBAClBC,gBAAiB,GACjBC,eAAgB,gBAChBC,gBAAiB,eAKjBC,OAAU,QACVC,qBAAsB,iBACtBC,gBAAiB,iBACjBC,cAAe,iBACfC,mBAAoB,gBACpBC,aAAc,gBAGdC,IAAO,QACPC,yBAA0B,gCAC1BC,0BAA2B,gCAC3BC,oCAAqC,qCACrCC,8DAA+D,2CAY/DC,OAAU,SACVC,MAAS,OACTC,KAAQ,QACRC,MAAS,UACTC,oCAAqC,gDACrCC,sCAAuC,kDACvCC,6CAA8C,8DAC9CC,2EAA4E,mFAC5EC,uFAAwF,4GACxFC,oCAAqC,8CACrCC,wBAAyB,sBACzBC,+EAAgF,6EAChFC,aAAc,kBACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAYRC,8EAA+E,mGAC/EC,8CAA+C,mEAC/CC,mDAAoD,yEACpDC,oDAAqD,0EACrDC,wEAAyE,+GACzEC,2CAA4C,wEAC5CC,iDAAkD,+EAClDC,iDAAkD,8EAClDC,gBAAiB,gBACjBC,UAAW,SACXC,QAAS,SAGTC,mCAAoC,qCACpCC,yBAA0B,kCAC1BC,0BAA2B,uCAC3BC,eAAgB", "file": "./lang/de_DE.js", "sourcesContent": ["import m from \"../../es2015/lang/de_DE\";\nwindow.am4lang_de_DE = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/de_DE.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n *\n * Locale: de\n * Language: German\n * Author: <PERSON>\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n *\n * ---\n * Edit but leave the header section above this line. You can remove any\n * subsequent comment sections.\n * ---\n *\n * Use this file as a template to create translations. Leave the key part in\n * English intact. Fill the value with a translation.\n *\n * Empty string means no translation, so default \"International English\"\n * will be used.\n *\n * If you need the translation to literally be an empty string, use `null`\n * instead.\n *\n * IMPORTANT:\n * When translating make good effort to keep the translation length\n * at least the same chartcount as the English, especially for short prompts.\n *\n * Having significantly longer prompts may distort the actual charts.\n *\n * NOTE:\n * Some prompts - like months or weekdays - come in two versions: full and\n * shortened.\n *\n * If there's no official shortened version of these in your language, and it\n * would not be possible to invent such short versions that don't seem weird\n * to native speakers of that language, fill those with the same as full\n * version.\n *\n * PLACEHOLDERS:\n * Some prompts have placeholders like \"%1\". Those will be replaced by actual\n * values during translation and should be retained in the translated prompts.\n *\n * Placeholder positions may be changed to better suit structure of the\n * sentence.\n *\n * For example \"From %1 to %2\", when actually used will replace \"%1\" with an\n * actual value representing range start, and \"%2\" will be replaced by end\n * value.\n *\n * E.g. in a Scrollbar for Value axis \"From %1 to %2\" will become\n * \"From 100 to 200\". You may translate \"From\" and \"to\", as well as re-arrange\n * the order of the prompt itself, but make sure the \"%1\" and \"%2\" remain, in\n * places where they will make sense.\n *\n * Save the file as language_LOCALE, i.e. `en_GB.ts`, `fr_FR.ts`, etc.\n */\nexport default {\n\t// Number formatting options.\n\t//\n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \".\",\n\n    // Suffixes for numbers\n    // When formatting numbers, big or small numers might be reformatted to\n    // shorter version, by applying a suffix.\n    //\n    // For example, 1000000 might become \"1m\".\n    // Or 1024 might become \"1KB\" if we're formatting byte numbers.\n    //\n    // This section defines such suffixes for all such cases.\n    \"_big_number_suffix_3\": \"K\",\n    \"_big_number_suffix_6\": \"Mio\",\n    \"_big_number_suffix_9\": \"Mrd\",\n    \"_big_number_suffix_12\": \"Bio\",\n    \"_big_number_suffix_15\": \"Brd\",\n    \"_big_number_suffix_18\": \"Trill\",\n    \"_big_number_suffix_21\": \"Trd\",\n    \"_big_number_suffix_24\": \"Y\",\n\n    \"_small_number_suffix_3\": \"m\",\n    \"_small_number_suffix_6\": \"μ\",\n    \"_small_number_suffix_9\": \"n\",\n    \"_small_number_suffix_12\": \"p\",\n    \"_small_number_suffix_15\": \"f\",\n    \"_small_number_suffix_18\": \"a\",\n    \"_small_number_suffix_21\": \"z\",\n    \"_small_number_suffix_24\": \"y\",\n\n    \"_byte_suffix_B\": \"B\",\n    \"_byte_suffix_KB\": \"KB\",\n    \"_byte_suffix_MB\": \"MB\",\n    \"_byte_suffix_GB\": \"GB\",\n    \"_byte_suffix_TB\": \"TB\",\n    \"_byte_suffix_PB\": \"PB\",\n\n    // Default date formats for various periods.\n\t//\n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-date-time/#Format_codes\n\t//\n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_day\": \"dd. MMM\",\n\t\"_date_week\": \"ww\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t//\n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t//\n\t// Available codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era translations\n\t\"_era_ad\": \"v. Chr.\",\n\t\"_era_bc\": \"n. Chr.\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t//\n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"\",\n\t\"P\": \"\",\n\t\"AM\": \"\",\n\t\"PM\": \"\",\n\t\"A.M.\": \"\",\n\t\"P.M.\": \"\",\n\n\t// Date-related stuff.\n\t//\n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t//\n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t//\n\t// Short versions follow the guidelines of the the german \"Duden\" (https://de.wikipedia.org/wiki/Monat#Kurzformen)\n\t\"January\": \"Januar\",\n\t\"February\": \"Februar\",\n\t\"March\": \"März\",\n\t\"April\": \"April\",\n\t\"May\": \"Mai\",\n\t\"June\": \"Juni\",\n\t\"July\": \"Juli\",\n\t\"August\": \"August\",\n\t\"September\": \"September\",\n\t\"October\": \"Oktober\",\n\t\"November\": \"November\",\n\t\"December\": \"Dezember\",\n\t\"Jan\": \"Jan.\",\n\t\"Feb\": \"Febr.\",\n\t\"Mar\": \"März\",\n\t\"Apr\": \"Apr.\",\n\t\"May(short)\": \"Mai\",\n\t\"Jun\": \"Juni\",\n\t\"Jul\": \"Juli\",\n\t\"Aug\": \"Aug.\",\n\t\"Sep\": \"Sept.\",\n\t\"Oct\": \"Okt.\",\n\t\"Nov\": \"Nov.\",\n\t\"Dec\": \"Dez.\",\n\n\t// Weekdays.\n\t// Short versions follow the guidelines of the the german \"Duden\"\n\t\"Sunday\": \"Sonntag\",\n\t\"Monday\": \"Montag\",\n\t\"Tuesday\": \"Dienstag\",\n\t\"Wednesday\": \"Mittwoch\",\n\t\"Thursday\": \"Donnerstag\",\n\t\"Friday\": \"Freitag\",\n\t\"Saturday\": \"Samstag\",\n\t\"Sun\": \"So.\",\n\t\"Mon\": \"Mo.\",\n\t\"Tue\": \"Di.\",\n\t\"Wed\": \"Mi.\",\n\t\"Thu\": \"Do.\",\n\t\"Fri\": \"Fr.\",\n\t\"Sat\": \"Sa.\",\n\n\t// Date ordinal function.\n\t//\n\t// This is used when adding number ordinal when formatting days in dates.\n\t//\n\t// E.g. \"January 1st\", \"February 2nd\".\n\t//\n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(day: number): string {\n\t    return day + '.';\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Herauszoomen\",\n\n\t// Timeline buttons\n\t\"Play\": \"Abspielen\",\n\t\"Stop\": \"Stop\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Legende\",\n\n\t// Legend's item screen reader indicator.\n\t\"Click, tap or press ENTER to toggle\": \"Klicken, tippen oder ENTER drücken zum Umschalten\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Wird geladen\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Home\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"Diagramm\",\n\t\"Serial chart\": \"Seriendiagramm\",\n\t\"X/Y chart\": \"X-Y-Diagramm\",\n\t\"Pie chart\": \"Kreisdiagramm\",\n\t\"Gauge chart\": \"Messdiagramm\",\n\t\"Radar chart\": \"Netzdiagramm\",\n\t\"Sankey diagram\": \"Sankey-Diagramm\",\n\t\"Chord diagram\": \"\",\n\t\"Flow diagram\": \"Flussdiagramm\",\n\t\"TreeMap chart\": \"Baumdiagramm\",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"Serie\",\n\t\"Candlestick Series\": \"Kerzendiagramm\",\n\t\"Column Series\": \"Balkendiagramm\",\n\t\"Line Series\": \"Liniendiagramm\",\n\t\"Pie Slice Series\": \"Kreisdiagramm\",\n\t\"X/Y Series\": \"Punktdiagramm\",\n\n\t// Map-related stuff.\n\t\"Map\": \"Karte\",\n\t\"Press ENTER to zoom in\": \"Drücke ENTER zum Hereinzoomen\",\n\t\"Press ENTER to zoom out\": \"Drücke ENTER zum Herauszoomen\",\n\t\"Use arrow keys to zoom in and out\": \"Benutze die Pfeiltasten zum Zoomen\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Benutze Plus- und Minustasten zum Zoomen\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t//\n\t// \"Export\" is the top-level menu item.\n\t//\n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t//\n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Export\",\n\t\"Image\": \"Bild\",\n\t\"Data\": \"Daten\",\n\t\"Print\": \"Drucken\",\n\t\"Click, tap or press ENTER to open\": \"Zum Öffnen klicken, tippen oder ENTER drücken\",\n\t\"Click, tap or press ENTER to print.\": \"Zum Drucken klicken, tippen oder ENTER drücken.\",\n\t\"Click, tap or press ENTER to export as %1.\": \"Klicken, tippen oder ENTER drücken um als %1 zu exportieren\",\n\t'To save the image, right-click this link and choose \"Save picture as...\"': 'Um das Bild zu speichern, Rechtsklicken und \"Bild speichern unter ...\" auswählen',\n\t'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': 'Um das Bild zu speichern, Rechtsklick auf das Vorschaubild links und \"Bild speichern unter ...\" auswählen',\n\t\"(Press ESC to close this message)\": \"ESC drücken um diese Nachricht zu schließen\",\n\t\"Image Export Complete\": \"Bildexport komplett\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"Der Export dauert länger als geplant. Vielleicht ist etwas schiefgelaufen.\",\n\t\"Saved from\": \"Gespeichert von\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\n\t// Scrollbar-related stuff.\n\t//\n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t//\n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t//\n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t//\n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"TAB nutzen, um Ankerpunkte auszuwählen oder linke und rechte Pfeiltaste um die Auswahl zu ändern\",\n\t\"Use left and right arrows to move selection\": \"Linke und rechte Pfeiltaste nutzen um die Auswahl zu verschieben\",\n\t\"Use left and right arrows to move left selection\": \"Linke und rechte Pfeiltaste nutzen um die linke Auswahl zu verschieben\",\n\t\"Use left and right arrows to move right selection\": \"Linke und rechte Pfeiltaste nutzen um die rechte Auswahl zu verschieben\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"TAB nutzen, um Ankerpunkte auszuwählen oder Pfeiltaste nach oben und unten drücken, um die Auswahl zu ändern\",\n\t\"Use up and down arrows to move selection\": \"Pfeiltaste nach oben und unten drücken, um die Auswahl zu verschieben\",\n\t\"Use up and down arrows to move lower selection\": \"Pfeiltaste nach oben und unten drücken, um die untere Auswahl zu verschieben\",\n\t\"Use up and down arrows to move upper selection\": \"Pfeiltaste nach oben und unten drücken, um die obere Auswahl zu verschieben\",\n\t\"From %1 to %2\": \"Von %1 bis %2\",\n\t\"From %1\": \"Von %1\",\n\t\"To %1\": \"Bis %1\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"Kein Parser für Datei %1 verfügbar\",\n\t\"Error parsing file: %1\": \"Fehler beim Parsen von Datei %1\",\n\t\"Unable to load file: %1\": \"Datei %1 konnte nicht geladen werden\",\n\t\"Invalid date\": \"Kein Datum\",\n};\n\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/de_DE.ts"], "sourceRoot": ""}