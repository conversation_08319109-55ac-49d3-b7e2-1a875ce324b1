{"version": 3, "sources": ["webpack:///./lang/ru_RU.js", "webpack:///../../../src/lang/ru_RU.ts"], "names": ["window", "am4lang_ru_RU", "_decimalSeparator", "_thousandSeparator", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_second", "_duration_minute", "_duration_hour", "_duration_day", "_duration_week", "_duration_month", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Chord diagram", "Flow diagram", "TreeMap chart", "Series", "Candlestick Series", "Column Series", "Line Series", "Pie Slice Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;sHACAA,OAAAC,eCSCC,kBAAqB,IACrBC,mBAAsB,IAGtBC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,SACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OAGdC,sBAAyB,MACzBC,iBAAoB,KACpBC,iBAAoB,KACpBC,eAAkB,KAClBC,cAAiB,KACjBC,eAAkB,KAClBC,gBAAmB,KACnBC,eAAkB,OAGlBC,QAAW,OACXC,QAAW,UAGXC,EAAK,IACLC,EAAK,IACLC,GAAM,OACNC,GAAM,SACNC,OAAQ,aACRC,OAAQ,gBAGRC,QAAW,SACXC,SAAY,UACZC,MAAS,QACTC,MAAS,SACTC,IAAO,MACPC,KAAQ,OACRC,KAAQ,OACRC,OAAU,UACVC,UAAa,WACbC,QAAW,UACXC,SAAY,SACZC,SAAY,UACZC,IAAO,OACPC,IAAO,QACPC,IAAO,OACPC,IAAO,OACPC,aAAc,MACdC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,QACPC,IAAO,OACPC,IAAO,QACPC,IAAO,OACPC,OAAU,cACVC,OAAU,cACVC,QAAW,UACXC,UAAa,QACbC,SAAY,UACZC,OAAU,UACVC,SAAY,UACZC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAGPC,SAAY,SAASC,GACpB,MAAO,OAIRC,WAAY,YACZC,KAAQ,QACRC,KAAQ,OACRC,OAAU,UACVC,sCAAuC,0DACvCC,QAAW,gBACXC,KAAQ,SAGRC,MAAS,SACTC,eAAgB,qBAChBC,YAAa,gBACbC,YAAa,qBACbC,cAAe,mBACfC,cAAe,wBACfC,iBAAkB,kBAClBC,gBAAiB,kBACjBC,eAAgB,iBAChBC,gBAAiB,0BAGjBC,OAAU,QACVC,qBAAsB,mBACtBC,gBAAiB,mBACjBC,cAAe,iBACfC,mBAAoB,iBACpBC,aAAc,YAGdC,IAAO,QACPC,yBAA0B,+BAC1BC,0BAA2B,+BAC3BC,oCAAqC,0DACrCC,8DAA+D,6EAG/DC,OAAU,iBACVC,MAAS,cACTC,KAAQ,SACRC,MAAS,WACTC,oCAAqC,qDACrCC,sCAAuC,yDACvCC,6CAA8C,mEAC9CC,2EAA4E,2GAC5EC,uFAAwF,mHACxFC,oCAAqC,4CACrCC,wBAAyB,+BACzBC,+EAAgF,kFAChFC,aAAc,eACdC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,KAAQ,OACRC,IAAO,MACPC,KAAQ,OAGRC,8EAA+E,+GAC/EC,8CAA+C,gEAC/CC,mDAAoD,sEACpDC,oDAAqD,uEACrDC,wEAAyE,2FACzEC,2CAA4C,8DAC5CC,iDAAkD,qEAClDC,iDAAkD,sEAClDC,gBAAiB,cACjBC,UAAW,QACXC,QAAS,QAGTC,mCAAoC,gCACpCC,yBAA0B,+BAC1BC,0BAA2B,gCAC3BC,eAAgB", "file": "./lang/ru_RU.js", "sourcesContent": ["import m from \"../../es2015/lang/ru_RU\";\nwindow.am4lang_ru_RU = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/ru_RU.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n * \n * Locale: ru_RU\n * Language: Russian\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// number formatter related\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \" \",\n\n\t// Default date formats for various periods\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_day\": \"dd MMM\",\n\t\"_date_week\": \"ww\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era\n\t\"_era_ad\": \"н.э.\",\n\t\"_era_bc\": \"до н.э.\",\n\n\t// Period\n\t\"A\": \"У\",\n\t\"P\": \"В\",\n\t\"AM\": \"утра\",\n\t\"PM\": \"вечера\",\n\t\"A.M.\": \"до полудня\",\n\t\"P.M.\": \"после полудня\",\n\n\t// Dates\n\t\"January\": \"января\",\n\t\"February\": \"февраля\",\n\t\"March\": \"марта\",\n\t\"April\": \"апреля\",\n\t\"May\": \"мая\",\n\t\"June\": \"июня\",\n\t\"July\": \"июля\",\n\t\"August\": \"августа\",\n\t\"September\": \"сентября\",\n\t\"October\": \"октября\",\n\t\"November\": \"ноября\",\n\t\"December\": \"декабря\",\n\t\"Jan\": \"янв.\",\n\t\"Feb\": \"февр.\",\n\t\"Mar\": \"март\",\n\t\"Apr\": \"апр.\",\n\t\"May(short)\": \"май\",\n\t\"Jun\": \"июнь\",\n\t\"Jul\": \"июль\",\n\t\"Aug\": \"авг.\",\n\t\"Sep\": \"сент.\",\n\t\"Oct\": \"окт.\",\n\t\"Nov\": \"нояб.\",\n\t\"Dec\": \"дек.\",\n\t\"Sunday\": \"воскресенье\",\n\t\"Monday\": \"понедельник\",\n\t\"Tuesday\": \"вторник\",\n\t\"Wednesday\": \"среда\",\n\t\"Thursday\": \"четверг\",\n\t\"Friday\": \"пятница\",\n\t\"Saturday\": \"суббота\",\n\t\"Sun\": \"вс.\",\n\t\"Mon\": \"пн.\",\n\t\"Tue\": \"вт.\",\n\t\"Wed\": \"ср.\",\n\t\"Thu\": \"чт.\",\n\t\"Fri\": \"пт.\",\n\t\"Sat\": \"сб.\",\n\n\t// ordinal function\n\t\"_dateOrd\": function(day: number): string {\n\t\treturn '-ое';\n\t},\n\n\t// Chart elements\n\t\"Zoom Out\": \"Уменьшить\",\n\t\"Play\": \"Старт\",\n\t\"Stop\": \"Стоп\",\n\t\"Legend\": \"Легенда\",\n\t\"Click, tap or press ENTER to toggle\": \"Щелкните, коснитесь или нажмите ВВОД, чтобы переключить\",\n\t\"Loading\": \"Идет загрузка\",\n\t\"Home\": \"Начало\",\n\n\t// Chart types\n\t\"Chart\": \"График\",\n\t\"Serial chart\": \"Серийная диаграмма\",\n\t\"X/Y chart\": \"Диаграмма X/Y\",\n\t\"Pie chart\": \"Круговая диаграмма\",\n\t\"Gauge chart\": \"Датчик-диаграмма\",\n\t\"Radar chart\": \"Лепестковая диаграмма\",\n\t\"Sankey diagram\": \"Диаграмма Сэнки\",\n\t\"Chord diagram\": \"Диаграмма Chord\",\n\t\"Flow diagram\": \"Диаграмма флоу\",\n\t\"TreeMap chart\": \"Иерархическая диаграмма\",\n\n\t// Series types\n\t\"Series\": \"Серия\",\n\t\"Candlestick Series\": \"Серия-подсвечник\",\n\t\"Column Series\": \"Столбчатая серия\",\n\t\"Line Series\": \"Линейная серия\",\n\t\"Pie Slice Series\": \"Круговая серия\",\n\t\"X/Y Series\": \"X/Y серия\",\n\n\t// Map-related\n\t\"Map\": \"Карта\",\n\t\"Press ENTER to zoom in\": \"Нажмите ВВОД чтобу увеличить\",\n\t\"Press ENTER to zoom out\": \"Нажмите ВВОД чтобы уменьшить\",\n\t\"Use arrow keys to zoom in and out\": \"Используйте клавиши-стрелки чтобы увеличить и уменьшить\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Используйте клавиши плюс и минус на клавиатуре чтобы увеличить и уменьшить\",\n\n\t// Export-related\n\t\"Export\": \"Экспортировать\",\n\t\"Image\": \"Изображение\",\n\t\"Data\": \"Данные\",\n\t\"Print\": \"Печатать\",\n\t\"Click, tap or press ENTER to open\": \"Щелкните, коснитесь или нажмите ВВОД чтобы открыть\",\n\t\"Click, tap or press ENTER to print.\": \"Щелкните, коснитесь или нажмите ВВОД чтобы распечатать\",\n\t\"Click, tap or press ENTER to export as %1.\": \"Щелкните, коснитесь или нажмите ВВОД чтобы экспортировать как %1\",\n\t'To save the image, right-click this link and choose \"Save picture as...\"': 'Чтобы сохранить изображение, щелкните правой кнопкой на ссылке и выберите \"Сохранить изображение как...\"',\n\t'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': 'Чтобы сохранить изображение, щелкните правой кнопкой на картинке слева и выберите \"Сохранить изображение как...\"',\n\t\"(Press ESC to close this message)\": \"(Нажмите ESC чтобы закрыть это сообщение)\",\n\t\"Image Export Complete\": \"Экспорт изображения завершен\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"Экспортирование заняло дольше, чем планировалось. Возможно что-то пошло не так.\",\n\t\"Saved from\": \"Сохранено из\",\n\t\"PNG\": \"PNG\",\n\t\"JPG\": \"JPG\",\n\t\"GIF\": \"GIF\",\n\t\"SVG\": \"SVG\",\n\t\"PDF\": \"PDF\",\n\t\"JSON\": \"JSON\",\n\t\"CSV\": \"CSV\",\n\t\"XLSX\": \"XLSX\",\n\n\t// Scrollbar-related\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"Используйте клавишу TAB, чтобы выбрать рукоятки или клавиши стрелок влево и вправо, чтобы изменить выделение\",\n\t\"Use left and right arrows to move selection\": \"Используйте стрелки влево-вправо, чтобы передвинуть выделение\",\n\t\"Use left and right arrows to move left selection\": \"Используйте стрелки влево-вправо, чтобы передвинуть левое выделение\",\n\t\"Use left and right arrows to move right selection\": \"Используйте стрелки влево-вправо, чтобы передвинуть правое выделение\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"Используйте TAB, чтобы выбрать рукоятки или клавиши вверх-вниз, чтобы изменить выделение\",\n\t\"Use up and down arrows to move selection\": \"Используйте стрелки вверх-вниз, чтобы передвинуть выделение\",\n\t\"Use up and down arrows to move lower selection\": \"Используйте стрелки вверх-вниз, чтобы передвинуть нижнее выделение\",\n\t\"Use up and down arrows to move upper selection\": \"Используйте стрелки вверх-вниз, чтобы передвинуть верхнее выделение\",\n\t\"From %1 to %2\": \"От %1 до %2\",\n\t\"From %1\": \"От %1\",\n\t\"To %1\": \"До %1\",\n\n\t// Data loader-related\n\t\"No parser available for file: %1\": \"Нет анализатора для файла: %1\",\n\t\"Error parsing file: %1\": \"Ошибка при разборе файла: %1\",\n\t\"Unable to load file: %1\": \"Не удалось загрузить файл: %1\",\n\t\"Invalid date\": \"Некорректная дата\",\n};\n\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/ru_RU.ts"], "sourceRoot": ""}