{"version": 3, "sources": ["webpack:///./lang/sv_SE.js", "webpack:///../../../src/lang/sv_SE.ts"], "names": ["window", "am4lang_sv_SE", "_decimalSeparator", "_thousandSeparator", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_second", "_duration_minute", "_duration_hour", "_duration_day", "_duration_week", "_duration_month", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Chord diagram", "Flow diagram", "TreeMap chart", "Series", "Candlestick Series", "Column Series", "Line Series", "Pie Slice Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;sHACAA,OAAAC,eCwBCC,kBAAqB,IACrBC,mBAAsB,IAWtBC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,aACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OASdC,sBAAyB,MACzBC,iBAAoB,KACpBC,iBAAoB,KACpBC,eAAkB,KAClBC,cAAiB,KACjBC,eAAkB,KAClBC,gBAAmB,KACnBC,eAAkB,OAGlBC,QAAW,QACXC,QAAW,QAUXC,EAAK,KACLC,EAAK,KACLC,GAAM,KACNC,GAAM,KACNC,OAAQ,OACRC,OAAQ,OAWRC,QAAW,UACXC,SAAY,WACZC,MAAS,OACTC,MAAS,QACTC,IAAO,MACPC,KAAQ,OACRC,KAAQ,OACRC,OAAU,UACVC,UAAa,YACbC,QAAW,UACXC,SAAY,WACZC,SAAY,WACZC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,aAAc,MACdC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OAGPC,OAAU,SACVC,OAAU,SACVC,QAAW,SACXC,UAAa,SACbC,SAAY,UACZC,OAAU,SACVC,SAAY,SACZC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAWPC,SAAY,SAASC,GAEpB,MAAO,IAKRC,WAAY,WAGZC,KAAQ,QACRC,KAAQ,SAGRC,OAAU,mBAGVC,sCAAuC,yCAGvCC,QAAW,WAIXC,KAAQ,MAKRC,MAAS,UACTC,eAAgB,eAChBC,YAAa,aACbC,YAAa,cACbC,cAAe,oBACfC,cAAe,eACfC,iBAAkB,gBAClBC,gBAAiB,gBACjBC,eAAgB,eAChBC,gBAAiB,eAKjBC,OAAU,SACVC,qBAAsB,qBACtBC,gBAAiB,eACjBC,cAAe,cACfC,mBAAoB,aACpBC,aAAc,aAGdC,IAAO,QACPC,yBAA0B,+BAC1BC,0BAA2B,+BAC3BC,oCAAqC,+CACrCC,8DAA+D,2DAY/DC,OAAU,YACVC,MAAS,OACTC,KAAQ,OACRC,MAAS,WACTC,oCAAqC,yCACrCC,sCAAuC,8CACvCC,6CAA8C,sDAC9CC,2EAA4E,yEAC5EC,uFAAwF,8FACxFC,oCAAqC,6BACrCC,wBAAyB,kBACzBC,+EAAgF,GAChFC,aAAc,cACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAYRC,8EAA+E,GAC/EC,8CAA+C,6DAC/CC,mDAAoD,kEACpDC,oDAAqD,gEACrDC,wEAAyE,GACzEC,2CAA4C,uDAC5CC,iDAAkD,6DAClDC,iDAAkD,4DAClDC,gBAAiB,kBACjBC,UAAW,UACXC,QAAS,UAGTC,mCAAoC,GACpCC,yBAA0B,GAC1BC,0BAA2B,GAC3BC,eAAgB", "file": "./lang/sv_SE.js", "sourcesContent": ["import m from \"../../es2015/lang/sv_SE\";\nwindow.am4lang_sv_SE = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/sv_SE.js\n// module id = null\n// module chunks = ", "/**\n * amCharts 4 locale\n * \n * Locale: sv\n * Language: Swedish\n * Author: <PERSON><PERSON><PERSON>\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\n *\n * ---\n * Empty string means no translation, so default \"International English\"\n * will be used.\n *\n * If you need the translation to literally be an empty string, use `null`\n * instead.\n *\n * IMPORTANT:\n * When translating make good effort to keep the translation length\n * at least the same chartcount as the English, especially for short prompts.\n */\nexport default {\n\t// Number formatting options.\n\t// \n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \" \",\n\n\t// Default date formats for various periods.\n\t// \n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-date-time/#Format_codes\n\t// \n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_day\": \"yyyy-MM-dd\",\n\t\"_date_week\": \"ww\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t// \n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t// \n\t// Available codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era translations\n\t\"_era_ad\": \"e.Kr.\",\n\t\"_era_bc\": \"f.Kr.\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t// \n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"fm\",\n\t\"P\": \"em\",\n\t\"AM\": \"fm\",\n\t\"PM\": \"em\",\n\t\"A.M.\": \"f.m.\",\n\t\"P.M.\": \"e.m.\",\n\n\t// Date-related stuff.\n\t// \n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t// \n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t\"January\": \"januari\",\n\t\"February\": \"februari\",\n\t\"March\": \"mars\",\n\t\"April\": \"april\",\n\t\"May\": \"maj\",\n\t\"June\": \"juni\",\n\t\"July\": \"juli\",\n\t\"August\": \"augusti\",\n\t\"September\": \"september\",\n\t\"October\": \"oktober\",\n\t\"November\": \"november\",\n\t\"December\": \"december\",\n\t\"Jan\": \"jan.\",\n\t\"Feb\": \"feb.\",\n\t\"Mar\": \"mars\",\n\t\"Apr\": \"apr.\",\n\t\"May(short)\": \"maj\",\n\t\"Jun\": \"juni\",\n\t\"Jul\": \"juli\",\n\t\"Aug\": \"aug.\",\n\t\"Sep\": \"sep.\",\n\t\"Oct\": \"okt.\",\n\t\"Nov\": \"nov.\",\n\t\"Dec\": \"dec.\",\n\n\t// Weekdays.\n\t\"Sunday\": \"söndag\",\n\t\"Monday\": \"måndag\",\n\t\"Tuesday\": \"tisdag\",\n\t\"Wednesday\": \"onsdag\",\n\t\"Thursday\": \"torsdag\",\n\t\"Friday\": \"fredag\",\n\t\"Saturday\": \"lördag\",\n\t\"Sun\": \"sön\",\n\t\"Mon\": \"mån\",\n\t\"Tue\": \"tis\",\n\t\"Wed\": \"ons\",\n\t\"Thu\": \"tor\",\n\t\"Fri\": \"fre\",\n\t\"Sat\": \"lör\",\n\n\t// Date ordinal function.\n\t// \n\t// This is used when adding number ordinal when formatting days in dates.\n\t// \n\t// E.g. \"January 1st\", \"February 2nd\".\n\t// \n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(day: number): string {\n\t\t// When indicating dates, suffixes are never used in Swedish.\n\t\treturn \"\";\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Zooma ut\",\n\n\t// Timeline buttons\n\t\"Play\": \"Spela\",\n\t\"Stop\": \"Stoppa\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Teckenförklaring\",\n\n\t// Legend's item screen reader indicator.\n\t\"Click, tap or press ENTER to toggle\": \"Klicka eller tryck ENTER för att ändra\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Läser in\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Hem\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"Diagram\",\n\t\"Serial chart\": \"Seriediagram\", // ???\n\t\"X/Y chart\": \"XY-diagram\",\n\t\"Pie chart\": \"Tårtdiagram\", // aka cirkeldiagram\n\t\"Gauge chart\": \"Instrumentdiagram\", // ???\n\t\"Radar chart\": \"Radardiagram\", // aka Spindelnätsdiagram\n\t\"Sankey diagram\": \"Sankeydiagram\",\n\t\"Chord diagram\": \"Strängdiagram\",\n\t\"Flow diagram\": \"Flödesschema\",\n\t\"TreeMap chart\": \"Träddiagram \",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"Serier\",\n\t\"Candlestick Series\": \"Candlestick-serier\",\n\t\"Column Series\": \"Kolumnserier\",\n\t\"Line Series\": \"Linjeserier\",\n\t\"Pie Slice Series\": \"Tårtserier\",\n\t\"X/Y Series\": \"X/Y-serier\",\n\n\t// Map-related stuff.\n\t\"Map\": \"Karta\",\n\t\"Press ENTER to zoom in\": \"Tryck RETUR för att zooma in\",\n\t\"Press ENTER to zoom out\": \"Tryck RETUR för att zooma ut\",\n\t\"Use arrow keys to zoom in and out\": \"Använd pil-knapparna för att zooma in och ut\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Använd plus- och minus-knapparna för att zooma in och ut\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t// \n\t// \"Export\" is the top-level menu item.\n\t// \n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t// \n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Exportera\",\n\t\"Image\": \"Bild\",\n\t\"Data\": \"Data\",\n\t\"Print\": \"Skriv ut\",\n\t\"Click, tap or press ENTER to open\": \"Klicka eller tryck ENTER för att öppna\",\n\t\"Click, tap or press ENTER to print.\": \"Klicka eller tryck ENTER för att skriva ut.\",\n\t\"Click, tap or press ENTER to export as %1.\": \"Klicka eller tryck ENTER för att exportera till %1.\",\n\t'To save the image, right-click this link and choose \"Save picture as...\"': 'För att spara bilden, höger-klicka länken och välj \"Spara bild som...\"',\n\t'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': 'För att spara bilden, höger-klicka miniatyrbilden till vänster och välj \"Spara bild som...\"',\n\t\"(Press ESC to close this message)\": \"(Tryck ESC för att stänga)\",\n\t\"Image Export Complete\": \"Bildexport klar\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"\",\n\t\"Saved from\": \"Sparad från\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\n\t// Scrollbar-related stuff.\n\t// \n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t// \n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t// \n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t// \n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"\",\n\t\"Use left and right arrows to move selection\": \"Använd vänster och höger pilknappar för att flytta urvalet\",\n\t\"Use left and right arrows to move left selection\": \"Använd vänster och höger pilknappar för att flytta vänsterurval\",\n\t\"Use left and right arrows to move right selection\": \"Använd vänster och höger pilknappar för att flytta högerurval\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"\",\n\t\"Use up and down arrows to move selection\": \"Använd upp och ner pilknappar för att flytta urvalet\",\n\t\"Use up and down arrows to move lower selection\": \"Använd upp och ner pilknappar för att flytta nedre urvalet\",\n\t\"Use up and down arrows to move upper selection\": \"Använd upp och ner pilknappar för att flytta övre urvalet\",\n\t\"From %1 to %2\": \"Från %1 till %2\",\n\t\"From %1\": \"Från %1\",\n\t\"To %1\": \"Till %1\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"\",\n\t\"Error parsing file: %1\": \"\",\n\t\"Unable to load file: %1\": \"\",\n\t\"Invalid date\": \"Ogiltigt datum\",\n};\n\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/sv_SE.ts"], "sourceRoot": ""}