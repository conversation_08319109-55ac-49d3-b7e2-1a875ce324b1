{"version": 3, "sources": ["webpack:///./lang/ko_KR.js", "webpack:///../../../src/lang/ko_KR.ts"], "names": ["window", "am4lang_ko_KR", "_decimalSeparator", "_thousandSeparator", "_big_number_suffix_3", "_big_number_suffix_6", "_big_number_suffix_9", "_big_number_suffix_12", "_big_number_suffix_15", "_big_number_suffix_18", "_big_number_suffix_21", "_big_number_suffix_24", "_small_number_suffix_3", "_small_number_suffix_6", "_small_number_suffix_9", "_small_number_suffix_12", "_small_number_suffix_15", "_small_number_suffix_18", "_small_number_suffix_21", "_small_number_suffix_24", "_byte_suffix_B", "_byte_suffix_KB", "_byte_suffix_MB", "_byte_suffix_GB", "_byte_suffix_TB", "_byte_suffix_PB", "_date", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_millisecond_second", "_duration_millisecond_minute", "_duration_millisecond_hour", "_duration_millisecond_day", "_duration_millisecond_week", "_duration_millisecond_month", "_duration_millisecond_year", "_duration_second", "_duration_second_minute", "_duration_second_hour", "_duration_second_day", "_duration_second_week", "_duration_second_month", "_duration_second_year", "_duration_minute", "_duration_minute_hour", "_duration_minute_day", "_duration_minute_week", "_duration_minute_month", "_duration_minute_year", "_duration_hour", "_duration_hour_day", "_duration_hour_week", "_duration_hour_month", "_duration_hour_year", "_duration_day", "_duration_day_week", "_duration_day_month", "_duration_day_year", "_duration_week", "_duration_week_month", "_duration_week_year", "_duration_month", "_duration_month_year", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "res", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Flow diagram", "Chord diagram", "TreeMap chart", "Force directed tree", "Sliced chart", "Series", "Candlestick Series", "OHLC Series", "Column Series", "Line Series", "Pie Slice Series", "Funnel Series", "Pyramid Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;wHACAA,OAAAC,eCaEC,kBAAqB,IACrBC,mBAAsB,IAUtBC,qBAAwB,IACxBC,qBAAwB,IACxBC,qBAAwB,IACxBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IAEzBC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAE3BC,eAAkB,IAClBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KAanBC,MAAS,aACTC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,SACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OAuBdC,sBAAyB,MACzBC,6BAAgC,SAChCC,6BAAgC,YAChCC,2BAA8B,eAC9BC,0BAA6B,iBAC7BC,2BAA8B,iBAC9BC,4BAA+B,uBAC/BC,2BAA8B,6BAE9BC,iBAAoB,KACpBC,wBAA2B,QAC3BC,sBAAyB,WACzBC,qBAAwB,gBACxBC,sBAAyB,gBACzBC,uBAA0B,sBAC1BC,sBAAyB,4BAEzBC,iBAAoB,KACpBC,sBAAyB,QACzBC,qBAAwB,aACxBC,sBAAyB,aACzBC,uBAA0B,mBAC1BC,sBAAyB,yBAEzBC,eAAkB,QAClBC,mBAAsB,aACtBC,oBAAuB,aACvBC,qBAAwB,mBACxBC,oBAAuB,yBAEvBC,cAAiB,OACjBC,mBAAsB,OACtBC,oBAAuB,aACvBC,mBAAsB,mBAEtBC,eAAkB,OAClBC,qBAAwB,OACxBC,oBAAuB,OAEvBC,gBAAmB,OACnBC,qBAAwB,aAExBC,eAAkB,OAGlBC,QAAW,KACXC,QAAW,KAUXC,EAAK,KACLC,EAAK,KACLC,GAAM,KACNC,GAAM,KACNC,OAAQ,KACRC,OAAQ,KAoBRC,QAAW,KACXC,SAAY,KACZC,MAAS,KACTC,MAAS,KACTC,IAAO,KACPC,KAAQ,KACRC,KAAQ,KACRC,OAAU,KACVC,UAAa,KACbC,QAAW,MACXC,SAAY,MACZC,SAAY,MACZC,IAAO,KACPC,IAAO,KACPC,IAAO,KACPC,IAAO,KACPC,aAAc,KACdC,IAAO,KACPC,IAAO,KACPC,IAAO,KACPC,IAAO,KACPC,IAAO,MACPC,IAAO,MACPC,IAAO,MAGPC,OAAU,MACVC,OAAU,MACVC,QAAW,MACXC,UAAa,MACbC,SAAY,MACZC,OAAU,MACVC,SAAY,MACZC,IAAO,IACPC,IAAO,IACPC,IAAO,IACPC,IAAO,IACPC,IAAO,IACPC,IAAO,IACPC,IAAO,IAWPC,SAAY,SAASC,GACnB,IAAIC,EAAM,IACV,GAAKD,EAAM,IAAQA,EAAM,GACvB,OAAQA,EAAM,IACZ,KAAK,EAGL,KAAK,EAGL,KAAK,EACHC,EAAM,IAIZ,OAAOA,GAKTC,WAAY,KAGZC,KAAQ,KACRC,KAAQ,KAGRC,OAAU,KAGVC,sCAAuC,6BAGvCC,QAAW,SAIXC,KAAQ,IAKRC,MAAS,KACTC,eAAgB,SAChBC,YAAa,SACbC,YAAa,QACbC,cAAe,SACfC,cAAe,SACfC,iBAAkB,WAClBC,eAAgB,YAChBC,gBAAiB,WACjBC,gBAAiB,SACjBC,sBAAuB,aACvBC,eAAgB,UAKhBC,OAAU,MACVC,qBAAsB,WACtBC,cAAe,WACfC,gBAAiB,SACjBC,cAAe,SACfC,mBAAoB,cACpBC,gBAAiB,SACjBC,iBAAkB,WAClBC,aAAc,UAGdC,IAAO,IACPC,yBAA0B,kBAC1BC,0BAA2B,kBAC3BC,oCAAqC,0BACrCC,8DAA+D,iCAY/DC,OAAU,OACVC,MAAS,MACTC,KAAQ,MACRC,MAAS,KACTC,oCAAqC,0BACrCC,sCAAuC,4BACvCC,6CAA8C,kCAC9CC,2EAA4E,oDAC5EC,uFAAwF,sDACxFC,oCAAqC,0BACrCC,wBAAyB,cACzBC,+EAAgF,sCAChFC,aAAc,eACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAYRC,8EAA+E,sCAC/EC,8CAA+C,8BAC/CC,mDAAoD,iCACpDC,oDAAqD,kCACrDC,wEAAyE,sCACzEC,2CAA4C,8BAC5CC,iDAAkD,iCAClDC,iDAAkD,iCAClDC,gBAAiB,cACjBC,UAAW,QACXC,QAAS,QAGTC,mCAAoC,gBACpCC,yBAA0B,eAC1BC,0BAA2B,gBAC3BC,eAAgB", "file": "./lang/ko_KR.js", "sourcesContent": ["import m from \"../../es2015/lang/ko_KR\";\nwindow.am4lang_ko_KR = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/ko_KR.js\n// module id = null\n// module chunks = ", "/**\r\n * amCharts 4 locale\r\n *\r\n * Locale: ko_KR\r\n * Language: Korean\r\n * Author: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\r\n *\r\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\r\n */\r\nexport default {\r\n  // Number formatting options.\r\n  //\r\n  // Please check with the local standards which separator is accepted to be\r\n  // used for separating decimals, and which for thousands.\r\n  \"_decimalSeparator\": \".\",\r\n  \"_thousandSeparator\": \",\",\r\n\r\n  // Suffixes for numbers\r\n  // When formatting numbers, big or small numers might be reformatted to\r\n  // shorter version, by applying a suffix.\r\n  //\r\n  // For example, 1000000 might become \"1m\".\r\n  // Or 1024 might become \"1KB\" if we're formatting byte numbers.\r\n  //\r\n  // This section defines such suffixes for all such cases.\r\n  \"_big_number_suffix_3\": \"k\",\r\n  \"_big_number_suffix_6\": \"M\",\r\n  \"_big_number_suffix_9\": \"G\",\r\n  \"_big_number_suffix_12\": \"T\",\r\n  \"_big_number_suffix_15\": \"P\",\r\n  \"_big_number_suffix_18\": \"E\",\r\n  \"_big_number_suffix_21\": \"Z\",\r\n  \"_big_number_suffix_24\": \"Y\",\r\n\r\n  \"_small_number_suffix_3\": \"m\",\r\n  \"_small_number_suffix_6\": \"μ\",\r\n  \"_small_number_suffix_9\": \"n\",\r\n  \"_small_number_suffix_12\": \"p\",\r\n  \"_small_number_suffix_15\": \"f\",\r\n  \"_small_number_suffix_18\": \"a\",\r\n  \"_small_number_suffix_21\": \"z\",\r\n  \"_small_number_suffix_24\": \"y\",\r\n\r\n  \"_byte_suffix_B\": \"B\",\r\n  \"_byte_suffix_KB\": \"KB\",\r\n  \"_byte_suffix_MB\": \"MB\",\r\n  \"_byte_suffix_GB\": \"GB\",\r\n  \"_byte_suffix_TB\": \"TB\",\r\n  \"_byte_suffix_PB\": \"PB\",\r\n\r\n  // Default date formats for various periods.\r\n  //\r\n  // This should reflect official or de facto formatting universally accepted\r\n  // in the country translation is being made for\r\n  // Available format codes here:\r\n  // https://www.amcharts.com/docs/v4/concepts/formatters/formatting-date-time/#Format_codes\r\n  //\r\n  // This will be used when formatting date/time for particular granularity,\r\n  // e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\r\n  // \r\n  // \"date\" is used as in default date format when showing standalone dates.\r\n  \"_date\": \"yyyy-MM-dd\",\r\n  \"_date_millisecond\": \"mm:ss SSS\",\r\n  \"_date_second\": \"HH:mm:ss\",\r\n  \"_date_minute\": \"HH:mm\",\r\n  \"_date_hour\": \"HH:mm\",\r\n  \"_date_day\": \"MMM dd\",\r\n  \"_date_week\": \"ww\",\r\n  \"_date_month\": \"MMM\",\r\n  \"_date_year\": \"yyyy\",\r\n\r\n  // Default duration formats for various base units.\r\n  //\r\n  // This will be used by DurationFormatter to format numeric values into\r\n  // duration.\r\n  //\r\n  // Notice how each duration unit comes in several versions. This is to ensure\r\n  // that each base unit is shown correctly.\r\n  //\r\n  // For example, if we have baseUnit set to \"second\", meaning our duration is\r\n  // in seconds.\r\n  //\r\n  // If we pass in `50` to formatter, it will know that we have just 50 seconds\r\n  // (less than a minute) so it will use format in `\"_duration_second\"` (\"ss\"),\r\n  // and the formatted result will be in like `\"50\"`.\r\n  //\r\n  // If we pass in `70`, which is more than a minute, the formatter will switch\r\n  // to `\"_duration_second_minute\"` (\"mm:ss\"), resulting in \"01:10\" formatted\r\n  // text.\r\n  //\r\n  // Available codes here:\r\n  // https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\r\n  \"_duration_millisecond\": \"SSS\",\r\n  \"_duration_millisecond_second\": \"ss.SSS\",\r\n  \"_duration_millisecond_minute\": \"mm:ss SSS\",\r\n  \"_duration_millisecond_hour\": \"hh:mm:ss SSS\",\r\n  \"_duration_millisecond_day\": \"d'd' mm:ss SSS\",\r\n  \"_duration_millisecond_week\": \"d'd' mm:ss SSS\",\r\n  \"_duration_millisecond_month\": \"M'm' dd'd' mm:ss SSS\",\r\n  \"_duration_millisecond_year\": \"y'y' MM'm' dd'd' mm:ss SSS\",\r\n\r\n  \"_duration_second\": \"ss\",\r\n  \"_duration_second_minute\": \"mm:ss\",\r\n  \"_duration_second_hour\": \"hh:mm:ss\",\r\n  \"_duration_second_day\": \"d'd' hh:mm:ss\",\r\n  \"_duration_second_week\": \"d'd' hh:mm:ss\",\r\n  \"_duration_second_month\": \"M'm' dd'd' hh:mm:ss\",\r\n  \"_duration_second_year\": \"y'y' MM'm' dd'd' hh:mm:ss\",\r\n\r\n  \"_duration_minute\": \"mm\",\r\n  \"_duration_minute_hour\": \"hh:mm\",\r\n  \"_duration_minute_day\": \"d'd' hh:mm\",\r\n  \"_duration_minute_week\": \"d'd' hh:mm\",\r\n  \"_duration_minute_month\": \"M'm' dd'd' hh:mm\",\r\n  \"_duration_minute_year\": \"y'y' MM'm' dd'd' hh:mm\",\r\n\r\n  \"_duration_hour\": \"hh'h'\",\r\n  \"_duration_hour_day\": \"d'd' hh'h'\",\r\n  \"_duration_hour_week\": \"d'd' hh'h'\",\r\n  \"_duration_hour_month\": \"M'm' dd'd' hh'h'\",\r\n  \"_duration_hour_year\": \"y'y' MM'm' dd'd' hh'h'\",\r\n\r\n  \"_duration_day\": \"d'd'\",\r\n  \"_duration_day_week\": \"d'd'\",\r\n  \"_duration_day_month\": \"M'm' dd'd'\",\r\n  \"_duration_day_year\": \"y'y' MM'm' dd'd'\",\r\n\r\n  \"_duration_week\": \"w'w'\",\r\n  \"_duration_week_month\": \"w'w'\",\r\n  \"_duration_week_year\": \"w'w'\",\r\n\r\n  \"_duration_month\": \"M'm'\",\r\n  \"_duration_month_year\": \"y'y' MM'm'\",\r\n\r\n  \"_duration_year\": \"y'y'\",\r\n\r\n  // Era translations\r\n  \"_era_ad\": \"AD\",\r\n  \"_era_bc\": \"BC\",\r\n\r\n  // Day part, used in 12-hour formats, e.g. 5 P.M.\r\n  // Please note that these come in 3 variants:\r\n  // * one letter (e.g. \"A\")\r\n  // * two letters (e.g. \"AM\")\r\n  // * two letters with dots (e.g. \"A.M.\")\r\n  //\r\n  // All three need to to be translated even if they are all the same. Some\r\n  // users might use one, some the other.\r\n  \"A\": \"AM\",\r\n  \"P\": \"PM\",\r\n  \"AM\": \"AM\",\r\n  \"PM\": \"PM\",\r\n  \"A.M.\": \"오전\",\r\n  \"P.M.\": \"오후\",\r\n\r\n  // Date-related stuff.\r\n  //\r\n  // When translating months, if there's a difference, use the form which is\r\n  // best for a full date, e.g. as you would use it in \"2018 January 1\".\r\n  //\r\n  // Note that May is listed twice. This is because in English May is the same\r\n  // in both long and short forms, while in other languages it may not be the\r\n  // case. Translate \"May\" to full word, while \"May(short)\" to shortened\r\n  // version.\r\n  //\r\n  // Should month names and weekdays be capitalized or not?\r\n  //\r\n  // Rule of thumb is this: if the names should always be capitalized,\r\n  // regardless of name position within date (\"January\", \"21st January 2018\",\r\n  // etc.) use capitalized names. Otherwise enter all lowercase.\r\n  //\r\n  // The date formatter will automatically capitalize names if they are the\r\n  // first (or only) word in resulting date.\r\n  \"January\": \"1월\",\r\n  \"February\": \"2월\",\r\n  \"March\": \"3월\",\r\n  \"April\": \"4월\",\r\n  \"May\": \"5월\",\r\n  \"June\": \"6월\",\r\n  \"July\": \"7월\",\r\n  \"August\": \"8월\",\r\n  \"September\": \"9월\",\r\n  \"October\": \"10월\",\r\n  \"November\": \"11월\",\r\n  \"December\": \"12월\",\r\n  \"Jan\": \"1월\",\r\n  \"Feb\": \"2월\",\r\n  \"Mar\": \"3월\",\r\n  \"Apr\": \"4월\",\r\n  \"May(short)\": \"5월\",\r\n  \"Jun\": \"6월\",\r\n  \"Jul\": \"7월\",\r\n  \"Aug\": \"8월\",\r\n  \"Sep\": \"9월\",\r\n  \"Oct\": \"10월\",\r\n  \"Nov\": \"11월\",\r\n  \"Dec\": \"12월\",\r\n\r\n  // Weekdays.\r\n  \"Sunday\": \"일요일\",\r\n  \"Monday\": \"월요일\",\r\n  \"Tuesday\": \"화요일\",\r\n  \"Wednesday\": \"수요일\",\r\n  \"Thursday\": \"목요일\",\r\n  \"Friday\": \"금요일\",\r\n  \"Saturday\": \"토요일\",\r\n  \"Sun\": \"일\",\r\n  \"Mon\": \"월\",\r\n  \"Tue\": \"화\",\r\n  \"Wed\": \"수\",\r\n  \"Thu\": \"목\",\r\n  \"Fri\": \"금\",\r\n  \"Sat\": \"토\",\r\n\r\n  // Date ordinal function.\r\n  //\r\n  // This is used when adding number ordinal when formatting days in dates.\r\n  //\r\n  // E.g. \"January 1st\", \"February 2nd\".\r\n  //\r\n  // The function accepts day number, and returns a string to be added to the\r\n  // day, like in default English translation, if we pass in 2, we will receive\r\n  // \"nd\" back.\r\n  \"_dateOrd\": function(day: number): string {\r\n    let res = \"일\";\r\n    if ((day < 11) || (day > 13)) {\r\n      switch (day % 10) {\r\n        case 1:\r\n          res = \"일\";\r\n          break;\r\n        case 2:\r\n          res = \"일\";\r\n          break;\r\n        case 3:\r\n          res = \"일\"\r\n          break;\r\n      }\r\n    }\r\n    return res;\r\n  },\r\n\r\n  // Various chart controls.\r\n  // Shown as a tooltip on zoom out button.\r\n  \"Zoom Out\": \"축소\",\r\n\r\n  // Timeline buttons\r\n  \"Play\": \"시작\",\r\n  \"Stop\": \"정지\",\r\n\r\n  // Chart's Legend screen reader title.\r\n  \"Legend\": \"범례\",\r\n\r\n  // Legend's item screen reader indicator.\r\n  \"Click, tap or press ENTER to toggle\": \"켜고 끄려면 클릭, 탭 혹은 엔터를 눌러주세요.\",\r\n\r\n  // Shown when the chart is busy loading something.\r\n  \"Loading\": \"불러오는 중\",\r\n\r\n  // Shown as the first button in the breadcrumb navigation, e.g.:\r\n  // Home > First level > ...\r\n  \"Home\": \"홈\",\r\n\r\n  // Chart types.\r\n  // Those are used as default screen reader titles for the main chart element\r\n  // unless developer has set some more descriptive title.\r\n  \"Chart\": \"차트\",\r\n  \"Serial chart\": \"시리얼 차트\",\r\n  \"X/Y chart\": \"X/Y 차트\",\r\n  \"Pie chart\": \"파이 차트\",\r\n  \"Gauge chart\": \"게이지 차트\",\r\n  \"Radar chart\": \"레이더 차트\",\r\n  \"Sankey diagram\": \"생키 다이어그램\",\r\n  \"Flow diagram\": \"플로우 다이어그램\",\r\n  \"Chord diagram\": \"코드 다이어그램\",\r\n  \"TreeMap chart\": \"트리맵 차트\",\r\n  \"Force directed tree\": \"포스 디렉티드 트리\",\r\n  \"Sliced chart\": \"슬라이스 차트\",\r\n\r\n  // Series types.\r\n  // Used to name series by type for screen readers if they do not have their\r\n  // name set.\r\n  \"Series\": \"시리즈\",\r\n  \"Candlestick Series\": \"캔들스틱 시리즈\",\r\n  \"OHLC Series\": \"OHLC 시리즈\",\r\n  \"Column Series\": \"컬럼 시리즈\",\r\n  \"Line Series\": \"라인 시리즈\",\r\n  \"Pie Slice Series\": \"파이 슬라이스 시리즈\",\r\n  \"Funnel Series\": \"퍼널 시리즈\",\r\n  \"Pyramid Series\": \"피라미드 시리즈\",\r\n  \"X/Y Series\": \"X/Y 시리즈\",\r\n\r\n  // Map-related stuff.\r\n  \"Map\": \"맵\",\r\n  \"Press ENTER to zoom in\": \"확대하려면 엔터를 누르세요.\",\r\n  \"Press ENTER to zoom out\": \"축소하려면 엔터를 누르세요.\",\r\n  \"Use arrow keys to zoom in and out\": \"확대 혹은 축소하려면 방향키를 이용하세요.\",\r\n  \"Use plus and minus keys on your keyboard to zoom in and out\": \"확대 혹은 축소하려면 키보드의 +/- 키를 이용하세요.\",\r\n\r\n  // Export-related stuff.\r\n  // These prompts are used in Export menu labels.\r\n  //\r\n  // \"Export\" is the top-level menu item.\r\n  //\r\n  // \"Image\", \"Data\", \"Print\" as second-level indicating type of export\r\n  // operation.\r\n  //\r\n  // Leave actual format untranslated, unless you absolutely know that they\r\n  // would convey more meaning in some other way.\r\n  \"Export\": \"내보내기\",\r\n  \"Image\": \"이미지\",\r\n  \"Data\": \"데이터\",\r\n  \"Print\": \"인쇄\",\r\n  \"Click, tap or press ENTER to open\": \"열려면, 클릭, 탭 또는 엔터를 누르세요.\",\r\n  \"Click, tap or press ENTER to print.\": \"출력하려면, 클릭, 탭 또는 엔터를 누르세요.\",\r\n  \"Click, tap or press ENTER to export as %1.\": \"%1(으)로 내보내려면 클릭, 탭 또는 엔터를 누르세요.\",\r\n  'To save the image, right-click this link and choose \"Save picture as...\"': '이미지를 저장하려면, 이 링크를 마우스로 우클릭하여 \"다른 이름으로 저장\"을 선택하세요.',\r\n  'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': '이미지를 저장하려면, 좌측 썸네일을 마우스로 우클릭하여 \"다른 이름으로 저장\"을 선택하세요.',\r\n  \"(Press ESC to close this message)\": \"(이 메시지를 끄려면 ESC를 누르세요.)\",\r\n  \"Image Export Complete\": \"이미지 내보내기 완료\",\r\n  \"Export operation took longer than expected. Something might have gone wrong.\": \"내보내기가 지연되고 있습니다. 문제가 없는지 확인이 필요합니다.\",\r\n  \"Saved from\": \"다음으로부터 저장됨: \",\r\n  \"PNG\": \"\",\r\n  \"JPG\": \"\",\r\n  \"GIF\": \"\",\r\n  \"SVG\": \"\",\r\n  \"PDF\": \"\",\r\n  \"JSON\": \"\",\r\n  \"CSV\": \"\",\r\n  \"XLSX\": \"\",\r\n\r\n  // Scrollbar-related stuff.\r\n  //\r\n  // Scrollbar is a control which can zoom and pan the axes on the chart.\r\n  //\r\n  // Each scrollbar has two grips: left or right (for horizontal scrollbar) or\r\n  // upper and lower (for vertical one).\r\n  //\r\n  // Prompts change in relation to whether Scrollbar is vertical or horizontal.\r\n  //\r\n  // The final section is used to indicate the current range of selection.\r\n  \"Use TAB to select grip buttons or left and right arrows to change selection\": \"선택 범위를 변경하려면 선택 버튼이나 좌우 화살표를 이용하세요.\",\r\n  \"Use left and right arrows to move selection\": \"선택 범위를 움직이려면 좌우 화살표를 이용하세요.\",\r\n  \"Use left and right arrows to move left selection\": \"왼쪽 선택 범위를 움직이려면 좌우 화살표를 이용하세요.\",\r\n  \"Use left and right arrows to move right selection\": \"오른쪽 선택 범위를 움직이려면 좌우 화살표를 이용하세요.\",\r\n  \"Use TAB select grip buttons or up and down arrows to change selection\": \"선택 범위를 변경하려면 선택 버튼이나 상하 화살표를 이용하세요.\",\r\n  \"Use up and down arrows to move selection\": \"선택 범위를 움직이려면 상하 화살표를 이용하세요.\",\r\n  \"Use up and down arrows to move lower selection\": \"하단 선택 범위를 움직이려면 상하 화살표를 이용하세요.\",\r\n  \"Use up and down arrows to move upper selection\": \"상단 선택 범위를 움직이려면 상하 화살표를 이용하세요.\",\r\n  \"From %1 to %2\": \"%1 부터 %2 까지\",\r\n  \"From %1\": \"%1 부터\",\r\n  \"To %1\": \"%1 까지\",\r\n\r\n  // Data loader-related.\r\n  \"No parser available for file: %1\": \"파일 파싱 불가능: %1\",\r\n  \"Error parsing file: %1\": \"파일 파싱 오류: %1\",\r\n  \"Unable to load file: %1\": \"파일 로드 불가능: %1\",\r\n  \"Invalid date\": \"날짜 올바르지 않음\",\r\n};\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/ko_KR.ts"], "sourceRoot": ""}