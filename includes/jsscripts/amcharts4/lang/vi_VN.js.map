{"version": 3, "sources": ["webpack:///./lang/vi_VN.js", "webpack:///../../../src/lang/vi_VN.ts"], "names": ["window", "am4lang_vi_VN", "_decimalSeparator", "_thousandSeparator", "_big_number_suffix_3", "_big_number_suffix_6", "_big_number_suffix_9", "_big_number_suffix_12", "_big_number_suffix_15", "_big_number_suffix_18", "_big_number_suffix_21", "_big_number_suffix_24", "_small_number_suffix_3", "_small_number_suffix_6", "_small_number_suffix_9", "_small_number_suffix_12", "_small_number_suffix_15", "_small_number_suffix_18", "_small_number_suffix_21", "_small_number_suffix_24", "_byte_suffix_B", "_byte_suffix_KB", "_byte_suffix_MB", "_byte_suffix_GB", "_byte_suffix_TB", "_byte_suffix_PB", "_date_millisecond", "_date_second", "_date_minute", "_date_hour", "_date_day", "_date_week", "_date_month", "_date_year", "_duration_millisecond", "_duration_millisecond_second", "_duration_millisecond_minute", "_duration_millisecond_hour", "_duration_millisecond_day", "_duration_millisecond_week", "_duration_millisecond_month", "_duration_millisecond_year", "_duration_second", "_duration_second_minute", "_duration_second_hour", "_duration_second_day", "_duration_second_week", "_duration_second_month", "_duration_second_year", "_duration_minute", "_duration_minute_hour", "_duration_minute_day", "_duration_minute_week", "_duration_minute_month", "_duration_minute_year", "_duration_hour", "_duration_hour_day", "_duration_hour_week", "_duration_hour_month", "_duration_hour_year", "_duration_day", "_duration_day_week", "_duration_day_month", "_duration_day_year", "_duration_week", "_duration_week_month", "_duration_week_year", "_duration_month", "_duration_month_year", "_duration_year", "_era_ad", "_era_bc", "A", "P", "AM", "PM", "A.M.", "P.M.", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", "Jan", "Feb", "Mar", "Apr", "May(short)", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "_dateOrd", "day", "res", "Zoom Out", "Play", "Stop", "Legend", "Click, tap or press ENTER to toggle", "Loading", "Home", "Chart", "Serial chart", "X/Y chart", "Pie chart", "Gauge chart", "Radar chart", "Sankey diagram", "Flow diagram", "Chord diagram", "TreeMap chart", "Sliced chart", "Series", "Candlestick Series", "OHLC Series", "Column Series", "Line Series", "Pie Slice Series", "Funnel Series", "Pyramid Series", "X/Y Series", "Map", "Press ENTER to zoom in", "Press ENTER to zoom out", "Use arrow keys to zoom in and out", "Use plus and minus keys on your keyboard to zoom in and out", "Export", "Image", "Data", "Print", "Click, tap or press ENTER to open", "Click, tap or press ENTER to print.", "Click, tap or press ENTER to export as %1.", "To save the image, right-click this link and choose \"Save picture as...\"", "To save the image, right-click thumbnail on the left and choose \"Save picture as...\"", "(Press ESC to close this message)", "Image Export Complete", "Export operation took longer than expected. Something might have gone wrong.", "Saved from", "PNG", "JPG", "GIF", "SVG", "PDF", "JSON", "CSV", "XLSX", "Use TAB to select grip buttons or left and right arrows to change selection", "Use left and right arrows to move selection", "Use left and right arrows to move left selection", "Use left and right arrows to move right selection", "Use TAB select grip buttons or up and down arrows to change selection", "Use up and down arrows to move selection", "Use up and down arrows to move lower selection", "Use up and down arrows to move upper selection", "From %1 to %2", "From %1", "To %1", "No parser available for file: %1", "Error parsing file: %1", "Unable to load file: %1", "Invalid date"], "mappings": ";;;;;;;;;;;;;;;;;;;wHACAA,OAAAC,eC4DEC,kBAAqB,IACrBC,mBAAsB,IAUtBC,qBAAwB,IACxBC,qBAAwB,IACxBC,qBAAwB,IACxBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IACzBC,sBAAyB,IAEzBC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,uBAA0B,IAC1BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAC3BC,wBAA2B,IAE3BC,eAAkB,IAClBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KACnBC,gBAAmB,KAWnBC,kBAAqB,YACrBC,aAAgB,WAChBC,aAAgB,QAChBC,WAAc,QACdC,UAAa,SACbC,WAAc,KACdC,YAAe,MACfC,WAAc,OAuBdC,sBAAyB,MACzBC,6BAAgC,SAChCC,6BAAgC,YAChCC,2BAA8B,eAC9BC,0BAA6B,iBAC7BC,2BAA8B,iBAC9BC,4BAA+B,uBAC/BC,2BAA8B,6BAE9BC,iBAAoB,KACpBC,wBAA2B,QAC3BC,sBAAyB,WACzBC,qBAAwB,gBACxBC,sBAAyB,gBACzBC,uBAA0B,sBAC1BC,sBAAyB,4BAEzBC,iBAAoB,KACpBC,sBAAyB,QACzBC,qBAAwB,aACxBC,sBAAyB,aACzBC,uBAA0B,mBAC1BC,sBAAyB,yBAEzBC,eAAkB,QAClBC,mBAAsB,aACtBC,oBAAuB,aACvBC,qBAAwB,mBACxBC,oBAAuB,yBAEvBC,cAAiB,OACjBC,mBAAsB,OACtBC,oBAAuB,aACvBC,mBAAsB,mBAEtBC,eAAkB,OAClBC,qBAAwB,OACxBC,oBAAuB,OAEvBC,gBAAmB,OACnBC,qBAAwB,aAExBC,eAAkB,OAGlBC,QAAW,SACXC,QAAW,WAUXC,EAAK,IACLC,EAAK,IACLC,GAAM,KACNC,GAAM,KACNC,OAAQ,KACRC,OAAQ,KAoBRC,QAAW,UACXC,SAAY,UACZC,MAAS,UACTC,MAAS,UACTC,IAAO,UACPC,KAAQ,UACRC,KAAQ,UACRC,OAAU,UACVC,UAAa,UACbC,QAAW,WACXC,SAAY,WACZC,SAAY,WACZC,IAAO,QACPC,IAAO,QACPC,IAAO,QACPC,IAAO,QACPC,aAAc,QACdC,IAAO,QACPC,IAAO,QACPC,IAAO,QACPC,IAAO,QACPC,IAAO,SACPC,IAAO,SACPC,IAAO,SAGPC,OAAU,WACVC,OAAU,UACVC,QAAW,SACXC,UAAa,SACbC,SAAY,UACZC,OAAU,UACVC,SAAY,UACZC,IAAO,KACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,IAAO,OAWPC,SAAY,SAASC,GACnB,IAAIC,EAAM,KACV,GAAKD,EAAM,IAAQA,EAAM,GACvB,OAAQA,EAAM,IACZ,KAAK,EACHC,EAAM,KACN,MACF,KAAK,EACHA,EAAM,KACN,MACF,KAAK,EACHA,EAAM,KAIZ,OAAOA,GAKTC,WAAY,YAGZC,KAAQ,OACRC,KAAQ,OAGRC,OAAU,WAGVC,sCAAuC,GAGvCC,QAAW,WAIXC,KAAQ,YAKRC,MAAS,GACTC,eAAgB,GAChBC,YAAa,GACbC,YAAa,GACbC,cAAe,GACfC,cAAe,GACfC,iBAAkB,GAClBC,eAAgB,GAChBC,gBAAiB,GACjBC,gBAAiB,GACjBC,eAAgB,GAKhBC,OAAU,GACVC,qBAAsB,GACtBC,cAAe,GACfC,gBAAiB,GACjBC,cAAe,GACfC,mBAAoB,GACpBC,gBAAiB,GACjBC,iBAAkB,GAClBC,aAAc,GAGdC,IAAO,GACPC,yBAA0B,GAC1BC,0BAA2B,GAC3BC,oCAAqC,GACrCC,8DAA+D,GAY/DC,OAAU,KACVC,MAAS,WACTC,KAAQ,UACRC,MAAS,KACTC,oCAAqC,GACrCC,sCAAuC,GACvCC,6CAA8C,GAC9CC,2EAA4E,GAC5EC,uFAAwF,GACxFC,oCAAqC,GACrCC,wBAAyB,GACzBC,+EAAgF,GAChFC,aAAc,GACdC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,IAAO,GACPC,KAAQ,GACRC,IAAO,GACPC,KAAQ,GAYRC,8EAA+E,GAC/EC,8CAA+C,GAC/CC,mDAAoD,GACpDC,oDAAqD,GACrDC,wEAAyE,GACzEC,2CAA4C,GAC5CC,iDAAkD,GAClDC,iDAAkD,GAClDC,gBAAiB,eACjBC,UAAW,QACXC,QAAS,SAGTC,mCAAoC,GACpCC,yBAA0B,GAC1BC,0BAA2B,GAC3BC,eAAgB", "file": "./lang/vi_VN.js", "sourcesContent": ["import m from \"../../es2015/lang/vi_VN\";\nwindow.am4lang_vi_VN = m;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lang/vi_VN.js\n// module id = null\n// module chunks = ", "/**\r\n * amCharts 4 locale\r\n *\r\n * Locale: vi_VN\r\n * Language: Vietnamese\r\n * Author: <PERSON><PERSON><PERSON>\r\n *\r\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v4/tutorials/creating-translations/) to make corrections or add new translations.\r\n *\r\n * ---\r\n * Edit but leave the header section above this line. You can remove any\r\n * subsequent comment sections.\r\n * ---\r\n *\r\n * Use this file as a template to create translations. Leave the key part in\r\n * English intact. Fill the value with a translation.\r\n *\r\n * Empty string means no translation, so default \"International English\"\r\n * will be used.\r\n *\r\n * If you need the translation to literally be an empty string, use `null`\r\n * instead.\r\n *\r\n * IMPORTANT:\r\n * When translating make good effort to keep the translation length\r\n * at least the same chartcount as the English, especially for short prompts.\r\n *\r\n * Having significantly longer prompts may distort the actual charts.\r\n *\r\n * NOTE:\r\n * Some prompts - like months or weekdays - come in two versions: full and\r\n * shortened.\r\n *\r\n * If there's no official shortened version of these in your language, and it\r\n * would not be possible to invent such short versions that don't seem weird\r\n * to native speakers of that language, fill those with the same as full\r\n * version.\r\n *\r\n * PLACEHOLDERS:\r\n * Some prompts have placeholders like \"%1\". Those will be replaced by actual\r\n * values during translation and should be retained in the translated prompts.\r\n *\r\n * Placeholder positions may be changed to better suit structure of the\r\n * sentence.\r\n *\r\n * For example \"From %1 to %2\", when actually used will replace \"%1\" with an\r\n * actual value representing range start, and \"%2\" will be replaced by end\r\n * value.\r\n *\r\n * E.g. in a Scrollbar for Value axis \"From %1 to %2\" will become\r\n * \"From 100 to 200\". You may translate \"From\" and \"to\", as well as re-arrange\r\n * the order of the prompt itself, but make sure the \"%1\" and \"%2\" remain, in\r\n * places where they will make sense.\r\n *\r\n * Save the file as language_LOCALE, i.e. `en_GB.ts`, `fr_FR.ts`, etc.\r\n */\r\nexport default {\r\n  // Number formatting options.\r\n  //\r\n  // Please check with the local standards which separator is accepted to be\r\n  // used for separating decimals, and which for thousands.\r\n  \"_decimalSeparator\": \",\",\r\n  \"_thousandSeparator\": \".\",\r\n\r\n  // Suffixes for numbers\r\n  // When formatting numbers, big or small numers might be reformatted to\r\n  // shorter version, by applying a suffix.\r\n  //\r\n  // For example, 1000000 might become \"1m\".\r\n  // Or 1024 might become \"1KB\" if we're formatting byte numbers.\r\n  //\r\n  // This section defines such suffixes for all such cases.\r\n  \"_big_number_suffix_3\": \"k\",\r\n  \"_big_number_suffix_6\": \"M\",\r\n  \"_big_number_suffix_9\": \"G\",\r\n  \"_big_number_suffix_12\": \"T\",\r\n  \"_big_number_suffix_15\": \"P\",\r\n  \"_big_number_suffix_18\": \"E\",\r\n  \"_big_number_suffix_21\": \"Z\",\r\n  \"_big_number_suffix_24\": \"Y\",\r\n\r\n  \"_small_number_suffix_3\": \"m\",\r\n  \"_small_number_suffix_6\": \"μ\",\r\n  \"_small_number_suffix_9\": \"n\",\r\n  \"_small_number_suffix_12\": \"p\",\r\n  \"_small_number_suffix_15\": \"f\",\r\n  \"_small_number_suffix_18\": \"a\",\r\n  \"_small_number_suffix_21\": \"z\",\r\n  \"_small_number_suffix_24\": \"y\",\r\n\r\n  \"_byte_suffix_B\": \"B\",\r\n  \"_byte_suffix_KB\": \"KB\",\r\n  \"_byte_suffix_MB\": \"MB\",\r\n  \"_byte_suffix_GB\": \"GB\",\r\n  \"_byte_suffix_TB\": \"TB\",\r\n  \"_byte_suffix_PB\": \"PB\",\r\n\r\n  // Default date formats for various periods.\r\n  //\r\n  // This should reflect official or de facto formatting universally accepted\r\n  // in the country translation is being made for\r\n  // Available format codes here:\r\n  // https://www.amcharts.com/docs/v4/concepts/formatters/formatting-date-time/#Format_codes\r\n  //\r\n  // This will be used when formatting date/time for particular granularity,\r\n  // e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\r\n  \"_date_millisecond\": \"mm:ss SSS\",\r\n  \"_date_second\": \"HH:mm:ss\",\r\n  \"_date_minute\": \"HH:mm\",\r\n  \"_date_hour\": \"HH:mm\",\r\n  \"_date_day\": \"MMM dd\",\r\n  \"_date_week\": \"ww\",\r\n  \"_date_month\": \"MMM\",\r\n  \"_date_year\": \"yyyy\",\r\n\r\n  // Default duration formats for various base units.\r\n  //\r\n  // This will be used by DurationFormatter to format numeric values into\r\n  // duration.\r\n  //\r\n  // Notice how each duration unit comes in several versions. This is to ensure\r\n  // that each base unit is shown correctly.\r\n  //\r\n  // For example, if we have baseUnit set to \"second\", meaning our duration is\r\n  // in seconds.\r\n  //\r\n  // If we pass in `50` to formatter, it will know that we have just 50 seconds\r\n  // (less than a minute) so it will use format in `\"_duration_second\"` (\"ss\"),\r\n  // and the formatted result will be in like `\"50\"`.\r\n  //\r\n  // If we pass in `70`, which is more than a minute, the formatter will switch\r\n  // to `\"_duration_second_minute\"` (\"mm:ss\"), resulting in \"01:10\" formatted\r\n  // text.\r\n  //\r\n  // Available codes here:\r\n  // https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\r\n  \"_duration_millisecond\": \"SSS\",\r\n  \"_duration_millisecond_second\": \"ss.SSS\",\r\n  \"_duration_millisecond_minute\": \"mm:ss SSS\",\r\n  \"_duration_millisecond_hour\": \"hh:mm:ss SSS\",\r\n  \"_duration_millisecond_day\": \"d'd' mm:ss SSS\",\r\n  \"_duration_millisecond_week\": \"d'd' mm:ss SSS\",\r\n  \"_duration_millisecond_month\": \"M'm' dd'd' mm:ss SSS\",\r\n  \"_duration_millisecond_year\": \"y'y' MM'm' dd'd' mm:ss SSS\",\r\n\r\n  \"_duration_second\": \"ss\",\r\n  \"_duration_second_minute\": \"mm:ss\",\r\n  \"_duration_second_hour\": \"hh:mm:ss\",\r\n  \"_duration_second_day\": \"d'd' hh:mm:ss\",\r\n  \"_duration_second_week\": \"d'd' hh:mm:ss\",\r\n  \"_duration_second_month\": \"M'm' dd'd' hh:mm:ss\",\r\n  \"_duration_second_year\": \"y'y' MM'm' dd'd' hh:mm:ss\",\r\n\r\n  \"_duration_minute\": \"mm\",\r\n  \"_duration_minute_hour\": \"hh:mm\",\r\n  \"_duration_minute_day\": \"d'd' hh:mm\",\r\n  \"_duration_minute_week\": \"d'd' hh:mm\",\r\n  \"_duration_minute_month\": \"M'm' dd'd' hh:mm\",\r\n  \"_duration_minute_year\": \"y'y' MM'm' dd'd' hh:mm\",\r\n\r\n  \"_duration_hour\": \"hh'h'\",\r\n  \"_duration_hour_day\": \"d'd' hh'h'\",\r\n  \"_duration_hour_week\": \"d'd' hh'h'\",\r\n  \"_duration_hour_month\": \"M'm' dd'd' hh'h'\",\r\n  \"_duration_hour_year\": \"y'y' MM'm' dd'd' hh'h'\",\r\n\r\n  \"_duration_day\": \"d'd'\",\r\n  \"_duration_day_week\": \"d'd'\",\r\n  \"_duration_day_month\": \"M'm' dd'd'\",\r\n  \"_duration_day_year\": \"y'y' MM'm' dd'd'\",\r\n\r\n  \"_duration_week\": \"w'w'\",\r\n  \"_duration_week_month\": \"w'w'\",\r\n  \"_duration_week_year\": \"w'w'\",\r\n\r\n  \"_duration_month\": \"M'm'\",\r\n  \"_duration_month_year\": \"y'y' MM'm'\",\r\n\r\n  \"_duration_year\": \"y'y'\",\r\n\r\n  // Era translations\r\n  \"_era_ad\": \"sau CN\",\r\n  \"_era_bc\": \"Trước CN\",\r\n\r\n  // Day part, used in 12-hour formats, e.g. 5 P.M.\r\n  // Please note that these come in 3 variants:\r\n  // * one letter (e.g. \"A\")\r\n  // * two letters (e.g. \"AM\")\r\n  // * two letters with dots (e.g. \"A.M.\")\r\n  //\r\n  // All three need to to be translated even if they are all the same. Some\r\n  // users might use one, some the other.\r\n  \"A\": \"s\",\r\n  \"P\": \"c\",\r\n  \"AM\": \"SA\",\r\n  \"PM\": \"CH\",\r\n  \"A.M.\": \"SA\",\r\n  \"P.M.\": \"CH\",\r\n\r\n  // Date-related stuff.\r\n  //\r\n  // When translating months, if there's a difference, use the form which is\r\n  // best for a full date, e.g. as you would use it in \"2018 January 1\".\r\n  //\r\n  // Note that May is listed twice. This is because in English May is the same\r\n  // in both long and short forms, while in other languages it may not be the\r\n  // case. Translate \"May\" to full word, while \"May(short)\" to shortened\r\n  // version.\r\n  //\r\n  // Should month names and weekdays be capitalized or not?\r\n  //\r\n  // Rule of thumb is this: if the names should always be capitalized,\r\n  // regardless of name position within date (\"January\", \"21st January 2018\",\r\n  // etc.) use capitalized names. Otherwise enter all lowercase.\r\n  //\r\n  // The date formatter will automatically capitalize names if they are the\r\n  // first (or only) word in resulting date.\r\n  \"January\": \"tháng 1\",\r\n  \"February\": \"tháng 2\",\r\n  \"March\": \"tháng 3\",\r\n  \"April\": \"tháng 4\",\r\n  \"May\": \"tháng 5\",\r\n  \"June\": \"tháng 6\",\r\n  \"July\": \"tháng 7\",\r\n  \"August\": \"tháng 8\",\r\n  \"September\": \"tháng 9\",\r\n  \"October\": \"tháng 10\",\r\n  \"November\": \"tháng 11\",\r\n  \"December\": \"tháng 12\",\r\n  \"Jan\": \"thg 1\",\r\n  \"Feb\": \"thg 2\",\r\n  \"Mar\": \"thg 3\",\r\n  \"Apr\": \"thg 4\",\r\n  \"May(short)\": \"thg 5\",\r\n  \"Jun\": \"thg 6\",\r\n  \"Jul\": \"thg 7\",\r\n  \"Aug\": \"thg 8\",\r\n  \"Sep\": \"thg 9\",\r\n  \"Oct\": \"thg 10\",\r\n  \"Nov\": \"thg 11\",\r\n  \"Dec\": \"thg 12\",\r\n\r\n  // Weekdays.\r\n  \"Sunday\": \"Chủ Nhật\",\r\n  \"Monday\": \"Thứ Hai\",\r\n  \"Tuesday\": \"Thứ Ba\",\r\n  \"Wednesday\": \"Thứ Tư\",\r\n  \"Thursday\": \"Thứ Năm\",\r\n  \"Friday\": \"Thứ Sáu\",\r\n  \"Saturday\": \"Thứ Bảy\",\r\n  \"Sun\": \"CN\",\r\n  \"Mon\": \"Th 2\",\r\n  \"Tue\": \"Th 3\",\r\n  \"Wed\": \"Th 4\",\r\n  \"Thu\": \"Th 5\",\r\n  \"Fri\": \"Th 6\",\r\n  \"Sat\": \"Th 7\",\r\n\r\n  // Date ordinal function.\r\n  //\r\n  // This is used when adding number ordinal when formatting days in dates.\r\n  //\r\n  // E.g. \"January 1st\", \"February 2nd\".\r\n  //\r\n  // The function accepts day number, and returns a string to be added to the\r\n  // day, like in default English translation, if we pass in 2, we will receive\r\n  // \"nd\" back.\r\n  \"_dateOrd\": function(day: number): string {\r\n    let res = \"th\";\r\n    if ((day < 11) || (day > 13)) {\r\n      switch (day % 10) {\r\n        case 1:\r\n          res = \"st\";\r\n          break;\r\n        case 2:\r\n          res = \"nd\";\r\n          break;\r\n        case 3:\r\n          res = \"rd\"\r\n          break;\r\n      }\r\n    }\r\n    return res;\r\n  },\r\n\r\n  // Various chart controls.\r\n  // Shown as a tooltip on zoom out button.\r\n  \"Zoom Out\": \"Thu phóng\",\r\n\r\n  // Timeline buttons\r\n  \"Play\": \"Phát\",\r\n  \"Stop\": \"Dừng\",\r\n\r\n  // Chart's Legend screen reader title.\r\n  \"Legend\": \"Chú giải\",\r\n\r\n  // Legend's item screen reader indicator.\r\n  \"Click, tap or press ENTER to toggle\": \"\",\r\n\r\n  // Shown when the chart is busy loading something.\r\n  \"Loading\": \"Đang tải\",\r\n\r\n  // Shown as the first button in the breadcrumb navigation, e.g.:\r\n  // Home > First level > ...\r\n  \"Home\": \"Trang chủ\",\r\n\r\n  // Chart types.\r\n  // Those are used as default screen reader titles for the main chart element\r\n  // unless developer has set some more descriptive title.\r\n  \"Chart\": \"\",\r\n  \"Serial chart\": \"\",\r\n  \"X/Y chart\": \"\",\r\n  \"Pie chart\": \"\",\r\n  \"Gauge chart\": \"\",\r\n  \"Radar chart\": \"\",\r\n  \"Sankey diagram\": \"\",\r\n  \"Flow diagram\": \"\",\r\n  \"Chord diagram\": \"\",\r\n  \"TreeMap chart\": \"\",\r\n  \"Sliced chart\": \"\",\r\n\r\n  // Series types.\r\n  // Used to name series by type for screen readers if they do not have their\r\n  // name set.\r\n  \"Series\": \"\",\r\n  \"Candlestick Series\": \"\",\r\n  \"OHLC Series\": \"\",\r\n  \"Column Series\": \"\",\r\n  \"Line Series\": \"\",\r\n  \"Pie Slice Series\": \"\",\r\n  \"Funnel Series\": \"\",\r\n  \"Pyramid Series\": \"\",\r\n  \"X/Y Series\": \"\",\r\n\r\n  // Map-related stuff.\r\n  \"Map\": \"\",\r\n  \"Press ENTER to zoom in\": \"\",\r\n  \"Press ENTER to zoom out\": \"\",\r\n  \"Use arrow keys to zoom in and out\": \"\",\r\n  \"Use plus and minus keys on your keyboard to zoom in and out\": \"\",\r\n\r\n  // Export-related stuff.\r\n  // These prompts are used in Export menu labels.\r\n  //\r\n  // \"Export\" is the top-level menu item.\r\n  //\r\n  // \"Image\", \"Data\", \"Print\" as second-level indicating type of export\r\n  // operation.\r\n  //\r\n  // Leave actual format untranslated, unless you absolutely know that they\r\n  // would convey more meaning in some other way.\r\n  \"Export\": \"In\",\r\n  \"Image\": \"Hình ảnh\",\r\n  \"Data\": \"Dữ liệu\",\r\n  \"Print\": \"In\",\r\n  \"Click, tap or press ENTER to open\": \"\",\r\n  \"Click, tap or press ENTER to print.\": \"\",\r\n  \"Click, tap or press ENTER to export as %1.\": \"\",\r\n  'To save the image, right-click this link and choose \"Save picture as...\"': \"\",\r\n  'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': \"\",\r\n  \"(Press ESC to close this message)\": \"\",\r\n  \"Image Export Complete\": \"\",\r\n  \"Export operation took longer than expected. Something might have gone wrong.\": \"\",\r\n  \"Saved from\": \"\",\r\n  \"PNG\": \"\",\r\n  \"JPG\": \"\",\r\n  \"GIF\": \"\",\r\n  \"SVG\": \"\",\r\n  \"PDF\": \"\",\r\n  \"JSON\": \"\",\r\n  \"CSV\": \"\",\r\n  \"XLSX\": \"\",\r\n\r\n  // Scrollbar-related stuff.\r\n  //\r\n  // Scrollbar is a control which can zoom and pan the axes on the chart.\r\n  //\r\n  // Each scrollbar has two grips: left or right (for horizontal scrollbar) or\r\n  // upper and lower (for vertical one).\r\n  //\r\n  // Prompts change in relation to whether Scrollbar is vertical or horizontal.\r\n  //\r\n  // The final section is used to indicate the current range of selection.\r\n  \"Use TAB to select grip buttons or left and right arrows to change selection\": \"\",\r\n  \"Use left and right arrows to move selection\": \"\",\r\n  \"Use left and right arrows to move left selection\": \"\",\r\n  \"Use left and right arrows to move right selection\": \"\",\r\n  \"Use TAB select grip buttons or up and down arrows to change selection\": \"\",\r\n  \"Use up and down arrows to move selection\": \"\",\r\n  \"Use up and down arrows to move lower selection\": \"\",\r\n  \"Use up and down arrows to move upper selection\": \"\",\r\n  \"From %1 to %2\": \"Từ %1 đến %2\",\r\n  \"From %1\": \"Từ %1\",\r\n  \"To %1\": \"Đến %1\",\r\n\r\n  // Data loader-related.\r\n  \"No parser available for file: %1\": \"\",\r\n  \"Error parsing file: %1\": \"\",\r\n  \"Unable to load file: %1\": \"\",\r\n  \"Invalid date\": \"\",\r\n};\r\n\n\n\n// WEBPACK FOOTER //\n// ../../../src/lang/vi_VN.ts"], "sourceRoot": ""}