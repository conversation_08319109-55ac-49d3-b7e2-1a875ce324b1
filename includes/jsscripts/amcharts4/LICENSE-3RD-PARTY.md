# 3rd-party libraries used in amCharts 4

## Used in Core package

|Package|Copyright (c)|License|
|-------|-------------|-------|
|canvg|<PERSON>|[License](https://github.com/canvg/canvg/blob/master/LICENSE)|
|d3-geo|<PERSON>|[License](https://github.com/d3/d3-geo/blob/master/LICENSE)|
|d3-geo-projection|<PERSON>|[License](https://github.com/d3/d3-geo-projection/blob/master/LICENSE)|
|pdfmake|bpampuch|[License](https://github.com/bpampuch/pdfmake/blob/master/LICENSE)|
|Fabric|Printio (<PERSON><PERSON><PERSON>, Maxim Chernyak)|[License](https://github.com/fabricjs/fabric.js/blob/master/LICENSE)|
|polylabel|Mapbox|[License](https://github.com/mapbox/polylabel/blob/master/LICENSE)|
|SheetJS js-xlsx|SheetJS LLC|[License](https://github.com/SheetJS/js-xlsx/blob/master/LICENSE)|
|tslib|Microsoft|[License](https://github.com/microsoft/tslib/blob/master/LICENSE.txt)|


## Used in optional plugins

|Package|Copyright (c)|License|
|-------|-------------|-------|
|d3-force|Mike Bostock|[License](https://github.com/d3/d3-force/blob/master/LICENSE)|
|regression-js|Tom Alexander|[License](https://github.com/Tom-Alexander/regression-js/blob/master/LICENSE)|
|venn.js|Ben Frederickson|[License](https://github.com/benfred/venn.js/blob/master/LICENSE)
