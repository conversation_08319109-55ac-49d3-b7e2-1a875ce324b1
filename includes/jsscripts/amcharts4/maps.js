/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["cc1e"],{QJ7E:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={};n.d(i,"geoArea",function(){return ct}),n.d(i,"geoBounds",function(){return ee}),n.d(i,"geoCentroid",function(){return fe}),n.d(i,"geoCircle",function(){return xe}),n.d(i,"geoClipAntimeridian",function(){return ke}),n.d(i,"geoClipCircle",function(){return Be}),n.d(i,"geoClipExtent",function(){return Je}),n.d(i,"geoClipRectangle",function(){return qe}),n.d(i,"geoContains",function(){return gn}),n.d(i,"geoDistance",function(){return an}),n.d(i,"geoGraticule",function(){return yn}),n.d(i,"geoGraticule10",function(){return bn}),n.d(i,"geoInterpolate",function(){return xn}),n.d(i,"geoLength",function(){return nn}),n.d(i,"geoPath",function(){return _i}),n.d(i,"geoAlbers",function(){return ki}),n.d(i,"geoAlbersUsa",function(){return Bi}),n.d(i,"geoAzimuthalEqualArea",function(){return qi}),n.d(i,"geoAzimuthalEqualAreaRaw",function(){return Zi}),n.d(i,"geoAzimuthalEquidistant",function(){return Yi}),n.d(i,"geoAzimuthalEquidistantRaw",function(){return Xi}),n.d(i,"geoConicConformal",function(){return tr}),n.d(i,"geoConicConformalRaw",function(){return $i}),n.d(i,"geoConicEqualArea",function(){return Ri}),n.d(i,"geoConicEqualAreaRaw",function(){return Fi}),n.d(i,"geoConicEquidistant",function(){return rr}),n.d(i,"geoConicEquidistantRaw",function(){return ir}),n.d(i,"geoEqualEarth",function(){return hr}),n.d(i,"geoEqualEarthRaw",function(){return lr}),n.d(i,"geoEquirectangular",function(){return nr}),n.d(i,"geoEquirectangularRaw",function(){return er}),n.d(i,"geoGnomonic",function(){return fr}),n.d(i,"geoGnomonicRaw",function(){return pr}),n.d(i,"geoIdentity",function(){return gr}),n.d(i,"geoProjection",function(){return zi}),n.d(i,"geoProjectionMutator",function(){return Vi}),n.d(i,"geoMercator",function(){return Ji}),n.d(i,"geoMercatorRaw",function(){return Ui}),n.d(i,"geoNaturalEarth1",function(){return mr}),n.d(i,"geoNaturalEarth1Raw",function(){return vr}),n.d(i,"geoOrthographic",function(){return br}),n.d(i,"geoOrthographicRaw",function(){return yr}),n.d(i,"geoStereographic",function(){return Sr}),n.d(i,"geoStereographicRaw",function(){return Pr}),n.d(i,"geoTransverseMercator",function(){return Mr}),n.d(i,"geoTransverseMercatorRaw",function(){return _r}),n.d(i,"geoRotation",function(){return Se}),n.d(i,"geoStream",function(){return et}),n.d(i,"geoTransform",function(){return Mi});var r={};n.d(r,"normalizePoint",function(){return no}),n.d(r,"normalizeMultiline",function(){return io}),n.d(r,"wrapAngleTo180",function(){return ro}),n.d(r,"geoToPoint",function(){return oo});var o={};n.d(o,"Mercator",function(){return Go}),n.d(o,"Miller",function(){return Nu}),n.d(o,"Eckert6",function(){return zu}),n.d(o,"Orthographic",function(){return Vu}),n.d(o,"Stereographic",function(){return Au}),n.d(o,"Albers",function(){return Fu}),n.d(o,"AlbersUsa",function(){return Ru}),n.d(o,"NaturalEarth1",function(){return ku}),n.d(o,"AzimuthalEqualArea",function(){return Bu}),n.d(o,"EqualEarth",function(){return Hu}),n.d(o,"Projection",function(){return Ur});var a={};n.d(a,"LegendDataItem",function(){return u.b}),n.d(a,"Legend",function(){return u.a}),n.d(a,"LegendSettings",function(){return u.c}),n.d(a,"HeatLegend",function(){return s.a}),n.d(a,"MapChartDataItem",function(){return Po}),n.d(a,"MapChart",function(){return So}),n.d(a,"MapSeriesDataItem",function(){return xr}),n.d(a,"MapSeries",function(){return jr}),n.d(a,"MapObject",function(){return wr}),n.d(a,"MapPolygon",function(){return Hr}),n.d(a,"MapImage",function(){return Lr}),n.d(a,"MapLine",function(){return po}),n.d(a,"MapLineObject",function(){return ao}),n.d(a,"MapSpline",function(){return Mo}),n.d(a,"MapArc",function(){return jo}),n.d(a,"Graticule",function(){return vo}),n.d(a,"MapPolygonSeriesDataItem",function(){return Xr}),n.d(a,"MapPolygonSeries",function(){return Yr}),n.d(a,"MapLineSeriesDataItem",function(){return fo}),n.d(a,"MapLineSeries",function(){return go}),n.d(a,"MapSplineSeriesDataItem",function(){return Oo}),n.d(a,"MapSplineSeries",function(){return wo}),n.d(a,"MapImageSeriesDataItem",function(){return uo}),n.d(a,"MapImageSeries",function(){return so}),n.d(a,"MapArcSeriesDataItem",function(){return Lo}),n.d(a,"MapArcSeries",function(){return Co}),n.d(a,"GraticuleSeriesDataItem",function(){return mo}),n.d(a,"GraticuleSeries",function(){return yo}),n.d(a,"multiPolygonToGeo",function(){return Gr}),n.d(a,"multiLineToGeo",function(){return Dr}),n.d(a,"multiPointToGeo",function(){return Nr}),n.d(a,"pointToGeo",function(){return Vr}),n.d(a,"multiGeoPolygonToMultipolygon",function(){return Rr}),n.d(a,"getBackground",function(){return Br}),n.d(a,"multiGeoLineToMultiLine",function(){return Fr}),n.d(a,"multiGeoToPoint",function(){return zr}),n.d(a,"getCircle",function(){return kr}),n.d(a,"ZoomControl",function(){return To}),n.d(a,"SmallMap",function(){return $r}),n.d(a,"Projection",function(){return Ur}),n.d(a,"projections",function(){return o}),n.d(a,"geo",function(){return r}),n.d(a,"d3geo",function(){return i});var u=n("uWmK"),s=n("2OXf"),c=n("m4/l"),l=n("2I/e"),h=n("hD5A"),p=n("MIZb"),f=n("aM7D"),d=n("aCit"),g=n("Mtpk"),v=n("Gg2j"),m=function(){return new y};function y(){this.reset()}y.prototype={constructor:y,reset:function(){this.s=this.t=0},add:function(t){P(b,t,this.t),P(this,b.s,this.s),this.s?this.t+=b.t:this.s=b.t},valueOf:function(){return this.s}};var b=new y;function P(t,e,n){var i=t.s=e+n,r=i-e,o=i-r;t.t=e-o+(n-r)}var S=1e-6,_=1e-12,M=Math.PI,x=M/2,j=M/4,O=2*M,w=180/M,L=M/180,C=Math.abs,E=Math.atan,I=Math.atan2,T=Math.cos,G=Math.ceil,D=Math.exp,N=(Math.floor,Math.log),z=Math.pow,V=Math.sin,A=Math.sign||function(t){return t>0?1:t<0?-1:0},F=Math.sqrt,R=Math.tan;function k(t){return t>1?0:t<-1?M:Math.acos(t)}function B(t){return t>1?x:t<-1?-x:Math.asin(t)}function H(t){return(t=V(t/2))*t}function W(){}function Z(t,e){t&&X.hasOwnProperty(t.type)&&X[t.type](t,e)}var q={Feature:function(t,e){Z(t.geometry,e)},FeatureCollection:function(t,e){for(var n=t.features,i=-1,r=n.length;++i<r;)Z(n[i].geometry,e)}},X={Sphere:function(t,e){e.sphere()},Point:function(t,e){t=t.coordinates,e.point(t[0],t[1],t[2])},MultiPoint:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)t=n[i],e.point(t[0],t[1],t[2])},LineString:function(t,e){Y(t.coordinates,e,0)},MultiLineString:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)Y(n[i],e,0)},Polygon:function(t,e){U(t.coordinates,e)},MultiPolygon:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)U(n[i],e)},GeometryCollection:function(t,e){for(var n=t.geometries,i=-1,r=n.length;++i<r;)Z(n[i],e)}};function Y(t,e,n){var i,r=-1,o=t.length-n;for(e.lineStart();++r<o;)i=t[r],e.point(i[0],i[1],i[2]);e.lineEnd()}function U(t,e){var n=-1,i=t.length;for(e.polygonStart();++n<i;)Y(t[n],e,1);e.polygonEnd()}var J,K,Q,$,tt,et=function(t,e){t&&q.hasOwnProperty(t.type)?q[t.type](t,e):Z(t,e)},nt=m(),it=m(),rt={point:W,lineStart:W,lineEnd:W,polygonStart:function(){nt.reset(),rt.lineStart=ot,rt.lineEnd=at},polygonEnd:function(){var t=+nt;it.add(t<0?O+t:t),this.lineStart=this.lineEnd=this.point=W},sphere:function(){it.add(O)}};function ot(){rt.point=ut}function at(){st(J,K)}function ut(t,e){rt.point=st,J=t,K=e,Q=t*=L,$=T(e=(e*=L)/2+j),tt=V(e)}function st(t,e){e=(e*=L)/2+j;var n=(t*=L)-Q,i=n>=0?1:-1,r=i*n,o=T(e),a=V(e),u=tt*a,s=$*o+u*T(r),c=u*i*V(r);nt.add(I(c,s)),Q=t,$=o,tt=a}var ct=function(t){return it.reset(),et(t,rt),2*it};function lt(t){return[I(t[1],t[0]),B(t[2])]}function ht(t){var e=t[0],n=t[1],i=T(n);return[i*T(e),i*V(e),V(n)]}function pt(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function ft(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function dt(t,e){t[0]+=e[0],t[1]+=e[1],t[2]+=e[2]}function gt(t,e){return[t[0]*e,t[1]*e,t[2]*e]}function vt(t){var e=F(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=e,t[1]/=e,t[2]/=e}var mt,yt,bt,Pt,St,_t,Mt,xt,jt,Ot,wt=m(),Lt={point:Ct,lineStart:It,lineEnd:Tt,polygonStart:function(){Lt.point=Gt,Lt.lineStart=Dt,Lt.lineEnd=Nt,wt.reset(),rt.polygonStart()},polygonEnd:function(){rt.polygonEnd(),Lt.point=Ct,Lt.lineStart=It,Lt.lineEnd=Tt,nt<0?(mt=-(bt=180),yt=-(Pt=90)):wt>S?Pt=90:wt<-S&&(yt=-90),Ot[0]=mt,Ot[1]=bt},sphere:function(){mt=-(bt=180),yt=-(Pt=90)}};function Ct(t,e){jt.push(Ot=[mt=t,bt=t]),e<yt&&(yt=e),e>Pt&&(Pt=e)}function Et(t,e){var n=ht([t*L,e*L]);if(xt){var i=ft(xt,n),r=ft([i[1],-i[0],0],i);vt(r),r=lt(r);var o,a=t-St,u=a>0?1:-1,s=r[0]*w*u,c=C(a)>180;c^(u*St<s&&s<u*t)?(o=r[1]*w)>Pt&&(Pt=o):c^(u*St<(s=(s+360)%360-180)&&s<u*t)?(o=-r[1]*w)<yt&&(yt=o):(e<yt&&(yt=e),e>Pt&&(Pt=e)),c?t<St?zt(mt,t)>zt(mt,bt)&&(bt=t):zt(t,bt)>zt(mt,bt)&&(mt=t):bt>=mt?(t<mt&&(mt=t),t>bt&&(bt=t)):t>St?zt(mt,t)>zt(mt,bt)&&(bt=t):zt(t,bt)>zt(mt,bt)&&(mt=t)}else jt.push(Ot=[mt=t,bt=t]);e<yt&&(yt=e),e>Pt&&(Pt=e),xt=n,St=t}function It(){Lt.point=Et}function Tt(){Ot[0]=mt,Ot[1]=bt,Lt.point=Ct,xt=null}function Gt(t,e){if(xt){var n=t-St;wt.add(C(n)>180?n+(n>0?360:-360):n)}else _t=t,Mt=e;rt.point(t,e),Et(t,e)}function Dt(){rt.lineStart()}function Nt(){Gt(_t,Mt),rt.lineEnd(),C(wt)>S&&(mt=-(bt=180)),Ot[0]=mt,Ot[1]=bt,xt=null}function zt(t,e){return(e-=t)<0?e+360:e}function Vt(t,e){return t[0]-e[0]}function At(t,e){return t[0]<=t[1]?t[0]<=e&&e<=t[1]:e<t[0]||t[1]<e}var Ft,Rt,kt,Bt,Ht,Wt,Zt,qt,Xt,Yt,Ut,Jt,Kt,Qt,$t,te,ee=function(t){var e,n,i,r,o,a,u;if(Pt=bt=-(mt=yt=1/0),jt=[],et(t,Lt),n=jt.length){for(jt.sort(Vt),e=1,o=[i=jt[0]];e<n;++e)At(i,(r=jt[e])[0])||At(i,r[1])?(zt(i[0],r[1])>zt(i[0],i[1])&&(i[1]=r[1]),zt(r[0],i[1])>zt(i[0],i[1])&&(i[0]=r[0])):o.push(i=r);for(a=-1/0,e=0,i=o[n=o.length-1];e<=n;i=r,++e)r=o[e],(u=zt(i[1],r[0]))>a&&(a=u,mt=r[0],bt=i[1])}return jt=Ot=null,mt===1/0||yt===1/0?[[NaN,NaN],[NaN,NaN]]:[[mt,yt],[bt,Pt]]},ne={sphere:W,point:ie,lineStart:oe,lineEnd:se,polygonStart:function(){ne.lineStart=ce,ne.lineEnd=le},polygonEnd:function(){ne.lineStart=oe,ne.lineEnd=se}};function ie(t,e){t*=L;var n=T(e*=L);re(n*T(t),n*V(t),V(e))}function re(t,e,n){kt+=(t-kt)/++Ft,Bt+=(e-Bt)/Ft,Ht+=(n-Ht)/Ft}function oe(){ne.point=ae}function ae(t,e){t*=L;var n=T(e*=L);Qt=n*T(t),$t=n*V(t),te=V(e),ne.point=ue,re(Qt,$t,te)}function ue(t,e){t*=L;var n=T(e*=L),i=n*T(t),r=n*V(t),o=V(e),a=I(F((a=$t*o-te*r)*a+(a=te*i-Qt*o)*a+(a=Qt*r-$t*i)*a),Qt*i+$t*r+te*o);Rt+=a,Wt+=a*(Qt+(Qt=i)),Zt+=a*($t+($t=r)),qt+=a*(te+(te=o)),re(Qt,$t,te)}function se(){ne.point=ie}function ce(){ne.point=he}function le(){pe(Jt,Kt),ne.point=ie}function he(t,e){Jt=t,Kt=e,t*=L,e*=L,ne.point=pe;var n=T(e);Qt=n*T(t),$t=n*V(t),te=V(e),re(Qt,$t,te)}function pe(t,e){t*=L;var n=T(e*=L),i=n*T(t),r=n*V(t),o=V(e),a=$t*o-te*r,u=te*i-Qt*o,s=Qt*r-$t*i,c=F(a*a+u*u+s*s),l=B(c),h=c&&-l/c;Xt+=h*a,Yt+=h*u,Ut+=h*s,Rt+=l,Wt+=l*(Qt+(Qt=i)),Zt+=l*($t+($t=r)),qt+=l*(te+(te=o)),re(Qt,$t,te)}var fe=function(t){Ft=Rt=kt=Bt=Ht=Wt=Zt=qt=Xt=Yt=Ut=0,et(t,ne);var e=Xt,n=Yt,i=Ut,r=e*e+n*n+i*i;return r<_&&(e=Wt,n=Zt,i=qt,Rt<S&&(e=kt,n=Bt,i=Ht),(r=e*e+n*n+i*i)<_)?[NaN,NaN]:[I(n,e)*w,B(i/F(r))*w]},de=function(t){return function(){return t}},ge=function(t,e){function n(n,i){return n=t(n,i),e(n[0],n[1])}return t.invert&&e.invert&&(n.invert=function(n,i){return(n=e.invert(n,i))&&t.invert(n[0],n[1])}),n};function ve(t,e){return[C(t)>M?t+Math.round(-t/O)*O:t,e]}function me(t,e,n){return(t%=O)?e||n?ge(be(t),Pe(e,n)):be(t):e||n?Pe(e,n):ve}function ye(t){return function(e,n){return[(e+=t)>M?e-O:e<-M?e+O:e,n]}}function be(t){var e=ye(t);return e.invert=ye(-t),e}function Pe(t,e){var n=T(t),i=V(t),r=T(e),o=V(e);function a(t,e){var a=T(e),u=T(t)*a,s=V(t)*a,c=V(e),l=c*n+u*i;return[I(s*r-l*o,u*n-c*i),B(l*r+s*o)]}return a.invert=function(t,e){var a=T(e),u=T(t)*a,s=V(t)*a,c=V(e),l=c*r-s*o;return[I(s*r+c*o,u*n+l*i),B(l*n-u*i)]},a}ve.invert=ve;var Se=function(t){function e(e){return(e=t(e[0]*L,e[1]*L))[0]*=w,e[1]*=w,e}return t=me(t[0]*L,t[1]*L,t.length>2?t[2]*L:0),e.invert=function(e){return(e=t.invert(e[0]*L,e[1]*L))[0]*=w,e[1]*=w,e},e};function _e(t,e,n,i,r,o){if(n){var a=T(e),u=V(e),s=i*n;null==r?(r=e+i*O,o=e-s/2):(r=Me(a,r),o=Me(a,o),(i>0?r<o:r>o)&&(r+=i*O));for(var c,l=r;i>0?l>o:l<o;l-=s)c=lt([a,-u*T(l),-u*V(l)]),t.point(c[0],c[1])}}function Me(t,e){(e=ht(e))[0]-=t,vt(e);var n=k(-e[1]);return((-e[2]<0?-n:n)+O-S)%O}var xe=function(){var t,e,n=de([0,0]),i=de(90),r=de(6),o={point:function(n,i){t.push(n=e(n,i)),n[0]*=w,n[1]*=w}};function a(){var a=n.apply(this,arguments),u=i.apply(this,arguments)*L,s=r.apply(this,arguments)*L;return t=[],e=me(-a[0]*L,-a[1]*L,0).invert,_e(o,u,s,1),a={type:"Polygon",coordinates:[t]},t=e=null,a}return a.center=function(t){return arguments.length?(n="function"==typeof t?t:de([+t[0],+t[1]]),a):n},a.radius=function(t){return arguments.length?(i="function"==typeof t?t:de(+t),a):i},a.precision=function(t){return arguments.length?(r="function"==typeof t?t:de(+t),a):r},a},je=function(){var t,e=[];return{point:function(e,n){t.push([e,n])},lineStart:function(){e.push(t=[])},lineEnd:W,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}},Oe=function(t,e){return C(t[0]-e[0])<S&&C(t[1]-e[1])<S};function we(t,e,n,i){this.x=t,this.z=e,this.o=n,this.e=i,this.v=!1,this.n=this.p=null}var Le=function(t,e,n,i,r){var o,a,u=[],s=[];if(t.forEach(function(t){if(!((e=t.length-1)<=0)){var e,n,i=t[0],a=t[e];if(Oe(i,a)){for(r.lineStart(),o=0;o<e;++o)r.point((i=t[o])[0],i[1]);r.lineEnd()}else u.push(n=new we(i,t,null,!0)),s.push(n.o=new we(i,null,n,!1)),u.push(n=new we(a,t,null,!1)),s.push(n.o=new we(a,null,n,!0))}}),u.length){for(s.sort(e),Ce(u),Ce(s),o=0,a=s.length;o<a;++o)s[o].e=n=!n;for(var c,l,h=u[0];;){for(var p=h,f=!0;p.v;)if((p=p.n)===h)return;c=p.z,r.lineStart();do{if(p.v=p.o.v=!0,p.e){if(f)for(o=0,a=c.length;o<a;++o)r.point((l=c[o])[0],l[1]);else i(p.x,p.n.x,1,r);p=p.n}else{if(f)for(c=p.p.z,o=c.length-1;o>=0;--o)r.point((l=c[o])[0],l[1]);else i(p.x,p.p.x,-1,r);p=p.p}c=(p=p.o).z,f=!f}while(!p.v);r.lineEnd()}}};function Ce(t){if(e=t.length){for(var e,n,i=0,r=t[0];++i<e;)r.n=n=t[i],n.p=r,r=n;r.n=n=t[0],n.p=r}}var Ee=m();function Ie(t){return C(t[0])<=M?t[0]:A(t[0])*((C(t[0])+M)%O-M)}var Te=function(t,e){var n=Ie(e),i=e[1],r=V(i),o=[V(n),-T(n),0],a=0,u=0;Ee.reset(),1===r?i=x+S:-1===r&&(i=-x-S);for(var s=0,c=t.length;s<c;++s)if(h=(l=t[s]).length)for(var l,h,p=l[h-1],f=Ie(p),d=p[1]/2+j,g=V(d),v=T(d),m=0;m<h;++m,f=b,g=_,v=w,p=y){var y=l[m],b=Ie(y),P=y[1]/2+j,_=V(P),w=T(P),L=b-f,C=L>=0?1:-1,E=C*L,G=E>M,D=g*_;if(Ee.add(I(D*C*V(E),v*w+D*T(E))),a+=G?L+C*O:L,G^f>=n^b>=n){var N=ft(ht(p),ht(y));vt(N);var z=ft(o,N);vt(z);var A=(G^L>=0?-1:1)*B(z[2]);(i>A||i===A&&(N[0]||N[1]))&&(u+=G^L>=0?1:-1)}}return(a<-S||a<S&&Ee<-S)^1&u},Ge=function(t,e){return t<e?-1:t>e?1:t>=e?0:NaN};var De=function(t){return 1===t.length&&(t=function(t){return function(e,n){return Ge(t(e),n)}}(t)),{left:function(e,n,i,r){for(null==i&&(i=0),null==r&&(r=e.length);i<r;){var o=i+r>>>1;t(e[o],n)<0?i=o+1:r=o}return i},right:function(e,n,i,r){for(null==i&&(i=0),null==r&&(r=e.length);i<r;){var o=i+r>>>1;t(e[o],n)>0?r=o:i=o+1}return i}}}(Ge);De.right,De.left;var Ne=Array.prototype,ze=(Ne.slice,Ne.map,function(t,e,n){t=+t,e=+e,n=(r=arguments.length)<2?(e=t,t=0,1):r<3?1:+n;for(var i=-1,r=0|Math.max(0,Math.ceil((e-t)/n)),o=new Array(r);++i<r;)o[i]=t+i*n;return o});Math.sqrt(50),Math.sqrt(10),Math.sqrt(2);var Ve=function(t){for(var e,n,i,r=t.length,o=-1,a=0;++o<r;)a+=t[o].length;for(n=new Array(a);--r>=0;)for(e=(i=t[r]).length;--e>=0;)n[--a]=i[e];return n};var Ae=function(t,e,n,i){return function(r){var o,a,u,s=e(r),c=je(),l=e(c),h=!1,p={point:f,lineStart:g,lineEnd:v,polygonStart:function(){p.point=m,p.lineStart=y,p.lineEnd=b,a=[],o=[]},polygonEnd:function(){p.point=f,p.lineStart=g,p.lineEnd=v,a=Ve(a);var t=Te(o,i);a.length?(h||(r.polygonStart(),h=!0),Le(a,Re,t,n,r)):t&&(h||(r.polygonStart(),h=!0),r.lineStart(),n(null,null,1,r),r.lineEnd()),h&&(r.polygonEnd(),h=!1),a=o=null},sphere:function(){r.polygonStart(),r.lineStart(),n(null,null,1,r),r.lineEnd(),r.polygonEnd()}};function f(e,n){t(e,n)&&r.point(e,n)}function d(t,e){s.point(t,e)}function g(){p.point=d,s.lineStart()}function v(){p.point=f,s.lineEnd()}function m(t,e){u.push([t,e]),l.point(t,e)}function y(){l.lineStart(),u=[]}function b(){m(u[0][0],u[0][1]),l.lineEnd();var t,e,n,i,s=l.clean(),p=c.result(),f=p.length;if(u.pop(),o.push(u),u=null,f)if(1&s){if((e=(n=p[0]).length-1)>0){for(h||(r.polygonStart(),h=!0),r.lineStart(),t=0;t<e;++t)r.point((i=n[t])[0],i[1]);r.lineEnd()}}else f>1&&2&s&&p.push(p.pop().concat(p.shift())),a.push(p.filter(Fe))}return p}};function Fe(t){return t.length>1}function Re(t,e){return((t=t.x)[0]<0?t[1]-x-S:x-t[1])-((e=e.x)[0]<0?e[1]-x-S:x-e[1])}var ke=Ae(function(){return!0},function(t){var e,n=NaN,i=NaN,r=NaN;return{lineStart:function(){t.lineStart(),e=1},point:function(o,a){var u=o>0?M:-M,s=C(o-n);C(s-M)<S?(t.point(n,i=(i+a)/2>0?x:-x),t.point(r,i),t.lineEnd(),t.lineStart(),t.point(u,i),t.point(o,i),e=0):r!==u&&s>=M&&(C(n-r)<S&&(n-=r*S),C(o-u)<S&&(o-=u*S),i=function(t,e,n,i){var r,o,a=V(t-n);return C(a)>S?E((V(e)*(o=T(i))*V(n)-V(i)*(r=T(e))*V(t))/(r*o*a)):(e+i)/2}(n,i,o,a),t.point(r,i),t.lineEnd(),t.lineStart(),t.point(u,i),e=0),t.point(n=o,i=a),r=u},lineEnd:function(){t.lineEnd(),n=i=NaN},clean:function(){return 2-e}}},function(t,e,n,i){var r;if(null==t)r=n*x,i.point(-M,r),i.point(0,r),i.point(M,r),i.point(M,0),i.point(M,-r),i.point(0,-r),i.point(-M,-r),i.point(-M,0),i.point(-M,r);else if(C(t[0]-e[0])>S){var o=t[0]<e[0]?M:-M;r=n*o/2,i.point(-o,r),i.point(0,r),i.point(o,r)}else i.point(e[0],e[1])},[-M,-x]);var Be=function(t){var e=T(t),n=6*L,i=e>0,r=C(e)>S;function o(t,n){return T(t)*T(n)>e}function a(t,n,i){var r=[1,0,0],o=ft(ht(t),ht(n)),a=pt(o,o),u=o[0],s=a-u*u;if(!s)return!i&&t;var c=e*a/s,l=-e*u/s,h=ft(r,o),p=gt(r,c);dt(p,gt(o,l));var f=h,d=pt(p,f),g=pt(f,f),v=d*d-g*(pt(p,p)-1);if(!(v<0)){var m=F(v),y=gt(f,(-d-m)/g);if(dt(y,p),y=lt(y),!i)return y;var b,P=t[0],_=n[0],x=t[1],j=n[1];_<P&&(b=P,P=_,_=b);var O=_-P,w=C(O-M)<S;if(!w&&j<x&&(b=x,x=j,j=b),w||O<S?w?x+j>0^y[1]<(C(y[0]-P)<S?x:j):x<=y[1]&&y[1]<=j:O>M^(P<=y[0]&&y[0]<=_)){var L=gt(f,(-d+m)/g);return dt(L,p),[y,lt(L)]}}}function u(e,n){var r=i?t:M-t,o=0;return e<-r?o|=1:e>r&&(o|=2),n<-r?o|=4:n>r&&(o|=8),o}return Ae(o,function(t){var e,n,s,c,l;return{lineStart:function(){c=s=!1,l=1},point:function(h,p){var f,d=[h,p],g=o(h,p),v=i?g?0:u(h,p):g?u(h+(h<0?M:-M),p):0;if(!e&&(c=s=g)&&t.lineStart(),g!==s&&(!(f=a(e,d))||Oe(e,f)||Oe(d,f))&&(d[0]+=S,d[1]+=S,g=o(d[0],d[1])),g!==s)l=0,g?(t.lineStart(),f=a(d,e),t.point(f[0],f[1])):(f=a(e,d),t.point(f[0],f[1]),t.lineEnd()),e=f;else if(r&&e&&i^g){var m;v&n||!(m=a(d,e,!0))||(l=0,i?(t.lineStart(),t.point(m[0][0],m[0][1]),t.point(m[1][0],m[1][1]),t.lineEnd()):(t.point(m[1][0],m[1][1]),t.lineEnd(),t.lineStart(),t.point(m[0][0],m[0][1])))}!g||e&&Oe(e,d)||t.point(d[0],d[1]),e=d,s=g,n=v},lineEnd:function(){s&&t.lineEnd(),e=null},clean:function(){return l|(c&&s)<<1}}},function(e,i,r,o){_e(o,t,n,r,e,i)},i?[0,-t]:[-M,t-M])},He=function(t,e,n,i,r,o){var a,u=t[0],s=t[1],c=0,l=1,h=e[0]-u,p=e[1]-s;if(a=n-u,h||!(a>0)){if(a/=h,h<0){if(a<c)return;a<l&&(l=a)}else if(h>0){if(a>l)return;a>c&&(c=a)}if(a=r-u,h||!(a<0)){if(a/=h,h<0){if(a>l)return;a>c&&(c=a)}else if(h>0){if(a<c)return;a<l&&(l=a)}if(a=i-s,p||!(a>0)){if(a/=p,p<0){if(a<c)return;a<l&&(l=a)}else if(p>0){if(a>l)return;a>c&&(c=a)}if(a=o-s,p||!(a<0)){if(a/=p,p<0){if(a>l)return;a>c&&(c=a)}else if(p>0){if(a<c)return;a<l&&(l=a)}return c>0&&(t[0]=u+c*h,t[1]=s+c*p),l<1&&(e[0]=u+l*h,e[1]=s+l*p),!0}}}}},We=1e9,Ze=-We;function qe(t,e,n,i){function r(r,o){return t<=r&&r<=n&&e<=o&&o<=i}function o(r,o,u,c){var l=0,h=0;if(null==r||(l=a(r,u))!==(h=a(o,u))||s(r,o)<0^u>0)do{c.point(0===l||3===l?t:n,l>1?i:e)}while((l=(l+u+4)%4)!==h);else c.point(o[0],o[1])}function a(i,r){return C(i[0]-t)<S?r>0?0:3:C(i[0]-n)<S?r>0?2:1:C(i[1]-e)<S?r>0?1:0:r>0?3:2}function u(t,e){return s(t.x,e.x)}function s(t,e){var n=a(t,1),i=a(e,1);return n!==i?n-i:0===n?e[1]-t[1]:1===n?t[0]-e[0]:2===n?t[1]-e[1]:e[0]-t[0]}return function(a){var s,c,l,h,p,f,d,g,v,m,y,b=a,P=je(),S={point:_,lineStart:function(){S.point=M,c&&c.push(l=[]);m=!0,v=!1,d=g=NaN},lineEnd:function(){s&&(M(h,p),f&&v&&P.rejoin(),s.push(P.result()));S.point=_,v&&b.lineEnd()},polygonStart:function(){b=P,s=[],c=[],y=!0},polygonEnd:function(){var e=function(){for(var e=0,n=0,r=c.length;n<r;++n)for(var o,a,u=c[n],s=1,l=u.length,h=u[0],p=h[0],f=h[1];s<l;++s)o=p,a=f,h=u[s],p=h[0],f=h[1],a<=i?f>i&&(p-o)*(i-a)>(f-a)*(t-o)&&++e:f<=i&&(p-o)*(i-a)<(f-a)*(t-o)&&--e;return e}(),n=y&&e,r=(s=Ve(s)).length;(n||r)&&(a.polygonStart(),n&&(a.lineStart(),o(null,null,1,a),a.lineEnd()),r&&Le(s,u,e,o,a),a.polygonEnd());b=a,s=c=l=null}};function _(t,e){r(t,e)&&b.point(t,e)}function M(o,a){var u=r(o,a);if(c&&l.push([o,a]),m)h=o,p=a,f=u,m=!1,u&&(b.lineStart(),b.point(o,a));else if(u&&v)b.point(o,a);else{var s=[d=Math.max(Ze,Math.min(We,d)),g=Math.max(Ze,Math.min(We,g))],P=[o=Math.max(Ze,Math.min(We,o)),a=Math.max(Ze,Math.min(We,a))];He(s,P,t,e,n,i)?(v||(b.lineStart(),b.point(s[0],s[1])),b.point(P[0],P[1]),u||b.lineEnd(),y=!1):u&&(b.lineStart(),b.point(o,a),y=!1)}d=o,g=a,v=u}return S}}var Xe,Ye,Ue,Je=function(){var t,e,n,i=0,r=0,o=960,a=500;return n={stream:function(n){return t&&e===n?t:t=qe(i,r,o,a)(e=n)},extent:function(u){return arguments.length?(i=+u[0][0],r=+u[0][1],o=+u[1][0],a=+u[1][1],t=e=null,n):[[i,r],[o,a]]}}},Ke=m(),Qe={sphere:W,point:W,lineStart:function(){Qe.point=tn,Qe.lineEnd=$e},lineEnd:W,polygonStart:W,polygonEnd:W};function $e(){Qe.point=Qe.lineEnd=W}function tn(t,e){Xe=t*=L,Ye=V(e*=L),Ue=T(e),Qe.point=en}function en(t,e){t*=L;var n=V(e*=L),i=T(e),r=C(t-Xe),o=T(r),a=i*V(r),u=Ue*n-Ye*i*o,s=Ye*n+Ue*i*o;Ke.add(I(F(a*a+u*u),s)),Xe=t,Ye=n,Ue=i}var nn=function(t){return Ke.reset(),et(t,Qe),+Ke},rn=[null,null],on={type:"LineString",coordinates:rn},an=function(t,e){return rn[0]=t,rn[1]=e,nn(on)},un={Feature:function(t,e){return cn(t.geometry,e)},FeatureCollection:function(t,e){for(var n=t.features,i=-1,r=n.length;++i<r;)if(cn(n[i].geometry,e))return!0;return!1}},sn={Sphere:function(){return!0},Point:function(t,e){return ln(t.coordinates,e)},MultiPoint:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)if(ln(n[i],e))return!0;return!1},LineString:function(t,e){return hn(t.coordinates,e)},MultiLineString:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)if(hn(n[i],e))return!0;return!1},Polygon:function(t,e){return pn(t.coordinates,e)},MultiPolygon:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)if(pn(n[i],e))return!0;return!1},GeometryCollection:function(t,e){for(var n=t.geometries,i=-1,r=n.length;++i<r;)if(cn(n[i],e))return!0;return!1}};function cn(t,e){return!(!t||!sn.hasOwnProperty(t.type))&&sn[t.type](t,e)}function ln(t,e){return 0===an(t,e)}function hn(t,e){for(var n,i,r,o=0,a=t.length;o<a;o++){if(0===(i=an(t[o],e)))return!0;if(o>0&&(r=an(t[o],t[o-1]))>0&&n<=r&&i<=r&&(n+i-r)*(1-Math.pow((n-i)/r,2))<_*r)return!0;n=i}return!1}function pn(t,e){return!!Te(t.map(fn),dn(e))}function fn(t){return(t=t.map(dn)).pop(),t}function dn(t){return[t[0]*L,t[1]*L]}var gn=function(t,e){return(t&&un.hasOwnProperty(t.type)?un[t.type]:cn)(t,e)};function vn(t,e,n){var i=ze(t,e-S,n).concat(e);return function(t){return i.map(function(e){return[t,e]})}}function mn(t,e,n){var i=ze(t,e-S,n).concat(e);return function(t){return i.map(function(e){return[e,t]})}}function yn(){var t,e,n,i,r,o,a,u,s,c,l,h,p=10,f=p,d=90,g=360,v=2.5;function m(){return{type:"MultiLineString",coordinates:y()}}function y(){return ze(G(i/d)*d,n,d).map(l).concat(ze(G(u/g)*g,a,g).map(h)).concat(ze(G(e/p)*p,t,p).filter(function(t){return C(t%d)>S}).map(s)).concat(ze(G(o/f)*f,r,f).filter(function(t){return C(t%g)>S}).map(c))}return m.lines=function(){return y().map(function(t){return{type:"LineString",coordinates:t}})},m.outline=function(){return{type:"Polygon",coordinates:[l(i).concat(h(a).slice(1),l(n).reverse().slice(1),h(u).reverse().slice(1))]}},m.extent=function(t){return arguments.length?m.extentMajor(t).extentMinor(t):m.extentMinor()},m.extentMajor=function(t){return arguments.length?(i=+t[0][0],n=+t[1][0],u=+t[0][1],a=+t[1][1],i>n&&(t=i,i=n,n=t),u>a&&(t=u,u=a,a=t),m.precision(v)):[[i,u],[n,a]]},m.extentMinor=function(n){return arguments.length?(e=+n[0][0],t=+n[1][0],o=+n[0][1],r=+n[1][1],e>t&&(n=e,e=t,t=n),o>r&&(n=o,o=r,r=n),m.precision(v)):[[e,o],[t,r]]},m.step=function(t){return arguments.length?m.stepMajor(t).stepMinor(t):m.stepMinor()},m.stepMajor=function(t){return arguments.length?(d=+t[0],g=+t[1],m):[d,g]},m.stepMinor=function(t){return arguments.length?(p=+t[0],f=+t[1],m):[p,f]},m.precision=function(p){return arguments.length?(v=+p,s=vn(o,r,90),c=mn(e,t,v),l=vn(u,a,90),h=mn(i,n,v),m):v},m.extentMajor([[-180,-90+S],[180,90-S]]).extentMinor([[-180,-80-S],[180,80+S]])}function bn(){return yn()()}var Pn,Sn,_n,Mn,xn=function(t,e){var n=t[0]*L,i=t[1]*L,r=e[0]*L,o=e[1]*L,a=T(i),u=V(i),s=T(o),c=V(o),l=a*T(n),h=a*V(n),p=s*T(r),f=s*V(r),d=2*B(F(H(o-i)+a*s*H(r-n))),g=V(d),v=d?function(t){var e=V(t*=d)/g,n=V(d-t)/g,i=n*l+e*p,r=n*h+e*f,o=n*u+e*c;return[I(r,i)*w,I(o,F(i*i+r*r))*w]}:function(){return[n*w,i*w]};return v.distance=d,v},jn=function(t){return t},On=m(),wn=m(),Ln={point:W,lineStart:W,lineEnd:W,polygonStart:function(){Ln.lineStart=Cn,Ln.lineEnd=Tn},polygonEnd:function(){Ln.lineStart=Ln.lineEnd=Ln.point=W,On.add(C(wn)),wn.reset()},result:function(){var t=On/2;return On.reset(),t}};function Cn(){Ln.point=En}function En(t,e){Ln.point=In,Pn=_n=t,Sn=Mn=e}function In(t,e){wn.add(Mn*t-_n*e),_n=t,Mn=e}function Tn(){In(Pn,Sn)}var Gn=Ln,Dn=1/0,Nn=Dn,zn=-Dn,Vn=zn;var An,Fn,Rn,kn,Bn={point:function(t,e){t<Dn&&(Dn=t);t>zn&&(zn=t);e<Nn&&(Nn=e);e>Vn&&(Vn=e)},lineStart:W,lineEnd:W,polygonStart:W,polygonEnd:W,result:function(){var t=[[Dn,Nn],[zn,Vn]];return zn=Vn=-(Nn=Dn=1/0),t}},Hn=0,Wn=0,Zn=0,qn=0,Xn=0,Yn=0,Un=0,Jn=0,Kn=0,Qn={point:$n,lineStart:ti,lineEnd:ii,polygonStart:function(){Qn.lineStart=ri,Qn.lineEnd=oi},polygonEnd:function(){Qn.point=$n,Qn.lineStart=ti,Qn.lineEnd=ii},result:function(){var t=Kn?[Un/Kn,Jn/Kn]:Yn?[qn/Yn,Xn/Yn]:Zn?[Hn/Zn,Wn/Zn]:[NaN,NaN];return Hn=Wn=Zn=qn=Xn=Yn=Un=Jn=Kn=0,t}};function $n(t,e){Hn+=t,Wn+=e,++Zn}function ti(){Qn.point=ei}function ei(t,e){Qn.point=ni,$n(Rn=t,kn=e)}function ni(t,e){var n=t-Rn,i=e-kn,r=F(n*n+i*i);qn+=r*(Rn+t)/2,Xn+=r*(kn+e)/2,Yn+=r,$n(Rn=t,kn=e)}function ii(){Qn.point=$n}function ri(){Qn.point=ai}function oi(){ui(An,Fn)}function ai(t,e){Qn.point=ui,$n(An=Rn=t,Fn=kn=e)}function ui(t,e){var n=t-Rn,i=e-kn,r=F(n*n+i*i);qn+=r*(Rn+t)/2,Xn+=r*(kn+e)/2,Yn+=r,Un+=(r=kn*t-Rn*e)*(Rn+t),Jn+=r*(kn+e),Kn+=3*r,$n(Rn=t,kn=e)}var si=Qn;function ci(t){this._context=t}ci.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,e){switch(this._point){case 0:this._context.moveTo(t,e),this._point=1;break;case 1:this._context.lineTo(t,e);break;default:this._context.moveTo(t+this._radius,e),this._context.arc(t,e,this._radius,0,O)}},result:W};var li,hi,pi,fi,di,gi=m(),vi={point:W,lineStart:function(){vi.point=mi},lineEnd:function(){li&&yi(hi,pi),vi.point=W},polygonStart:function(){li=!0},polygonEnd:function(){li=null},result:function(){var t=+gi;return gi.reset(),t}};function mi(t,e){vi.point=yi,hi=fi=t,pi=di=e}function yi(t,e){fi-=t,di-=e,gi.add(F(fi*fi+di*di)),fi=t,di=e}var bi=vi;function Pi(){this._string=[]}function Si(t){return"m0,"+t+"a"+t+","+t+" 0 1,1 0,"+-2*t+"a"+t+","+t+" 0 1,1 0,"+2*t+"z"}Pi.prototype={_radius:4.5,_circle:Si(4.5),pointRadius:function(t){return(t=+t)!==this._radius&&(this._radius=t,this._circle=null),this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._string.push("Z"),this._point=NaN},point:function(t,e){switch(this._point){case 0:this._string.push("M",t,",",e),this._point=1;break;case 1:this._string.push("L",t,",",e);break;default:null==this._circle&&(this._circle=Si(this._radius)),this._string.push("M",t,",",e,this._circle)}},result:function(){if(this._string.length){var t=this._string.join("");return this._string=[],t}return null}};var _i=function(t,e){var n,i,r=4.5;function o(t){return t&&("function"==typeof r&&i.pointRadius(+r.apply(this,arguments)),et(t,n(i))),i.result()}return o.area=function(t){return et(t,n(Gn)),Gn.result()},o.measure=function(t){return et(t,n(bi)),bi.result()},o.bounds=function(t){return et(t,n(Bn)),Bn.result()},o.centroid=function(t){return et(t,n(si)),si.result()},o.projection=function(e){return arguments.length?(n=null==e?(t=null,jn):(t=e).stream,o):t},o.context=function(t){return arguments.length?(i=null==t?(e=null,new Pi):new ci(e=t),"function"!=typeof r&&i.pointRadius(r),o):e},o.pointRadius=function(t){return arguments.length?(r="function"==typeof t?t:(i.pointRadius(+t),+t),o):r},o.projection(t).context(e)},Mi=function(t){return{stream:xi(t)}};function xi(t){return function(e){var n=new ji;for(var i in t)n[i]=t[i];return n.stream=e,n}}function ji(){}function Oi(t,e,n){var i=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=i&&t.clipExtent(null),et(n,t.stream(Bn)),e(Bn.result()),null!=i&&t.clipExtent(i),t}function wi(t,e,n){return Oi(t,function(n){var i=e[1][0]-e[0][0],r=e[1][1]-e[0][1],o=Math.min(i/(n[1][0]-n[0][0]),r/(n[1][1]-n[0][1])),a=+e[0][0]+(i-o*(n[1][0]+n[0][0]))/2,u=+e[0][1]+(r-o*(n[1][1]+n[0][1]))/2;t.scale(150*o).translate([a,u])},n)}function Li(t,e,n){return wi(t,[[0,0],e],n)}function Ci(t,e,n){return Oi(t,function(n){var i=+e,r=i/(n[1][0]-n[0][0]),o=(i-r*(n[1][0]+n[0][0]))/2,a=-r*n[0][1];t.scale(150*r).translate([o,a])},n)}function Ei(t,e,n){return Oi(t,function(n){var i=+e,r=i/(n[1][1]-n[0][1]),o=-r*n[0][0],a=(i-r*(n[1][1]+n[0][1]))/2;t.scale(150*r).translate([o,a])},n)}ji.prototype={constructor:ji,point:function(t,e){this.stream.point(t,e)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var Ii=16,Ti=T(30*L),Gi=function(t,e){return+e?function(t,e){function n(i,r,o,a,u,s,c,l,h,p,f,d,g,v){var m=c-i,y=l-r,b=m*m+y*y;if(b>4*e&&g--){var P=a+p,_=u+f,M=s+d,x=F(P*P+_*_+M*M),j=B(M/=x),O=C(C(M)-1)<S||C(o-h)<S?(o+h)/2:I(_,P),w=t(O,j),L=w[0],E=w[1],T=L-i,G=E-r,D=y*T-m*G;(D*D/b>e||C((m*T+y*G)/b-.5)>.3||a*p+u*f+s*d<Ti)&&(n(i,r,o,a,u,s,L,E,O,P/=x,_/=x,M,g,v),v.point(L,E),n(L,E,O,P,_,M,c,l,h,p,f,d,g,v))}}return function(e){var i,r,o,a,u,s,c,l,h,p,f,d,g={point:v,lineStart:m,lineEnd:b,polygonStart:function(){e.polygonStart(),g.lineStart=P},polygonEnd:function(){e.polygonEnd(),g.lineStart=m}};function v(n,i){n=t(n,i),e.point(n[0],n[1])}function m(){l=NaN,g.point=y,e.lineStart()}function y(i,r){var o=ht([i,r]),a=t(i,r);n(l,h,c,p,f,d,l=a[0],h=a[1],c=i,p=o[0],f=o[1],d=o[2],Ii,e),e.point(l,h)}function b(){g.point=v,e.lineEnd()}function P(){m(),g.point=S,g.lineEnd=_}function S(t,e){y(i=t,e),r=l,o=h,a=p,u=f,s=d,g.point=y}function _(){n(l,h,c,p,f,d,r,o,i,a,u,s,Ii,e),g.lineEnd=b,b()}return g}}(t,e):function(t){return xi({point:function(e,n){e=t(e,n),this.stream.point(e[0],e[1])}})}(t)};var Di=xi({point:function(t,e){this.stream.point(t*L,e*L)}});function Ni(t,e,n,i){var r=T(i),o=V(i),a=r*t,u=o*t,s=r/t,c=o/t,l=(o*n-r*e)/t,h=(o*e+r*n)/t;function p(t,i){return[a*t-u*i+e,n-u*t-a*i]}return p.invert=function(t,e){return[s*t-c*e+l,h-c*t-s*e]},p}function zi(t){return Vi(function(){return t})()}function Vi(t){var e,n,i,r,o,a,u,s,c,l,h=150,p=480,f=250,d=0,g=0,v=0,m=0,y=0,b=0,P=null,S=ke,_=null,M=jn,x=.5;function j(t){return s(t[0]*L,t[1]*L)}function O(t){return(t=s.invert(t[0],t[1]))&&[t[0]*w,t[1]*w]}function C(){var t=Ni(h,0,0,b).apply(null,e(d,g)),i=(b?Ni:function(t,e,n){function i(i,r){return[e+t*i,n-t*r]}return i.invert=function(i,r){return[(i-e)/t,(n-r)/t]},i})(h,p-t[0],f-t[1],b);return n=me(v,m,y),u=ge(e,i),s=ge(n,u),a=Gi(u,x),E()}function E(){return c=l=null,j}return j.stream=function(t){return c&&l===t?c:c=Di(function(t){return xi({point:function(e,n){var i=t(e,n);return this.stream.point(i[0],i[1])}})}(n)(S(a(M(l=t)))))},j.preclip=function(t){return arguments.length?(S=t,P=void 0,E()):S},j.postclip=function(t){return arguments.length?(M=t,_=i=r=o=null,E()):M},j.clipAngle=function(t){return arguments.length?(S=+t?Be(P=t*L):(P=null,ke),E()):P*w},j.clipExtent=function(t){return arguments.length?(M=null==t?(_=i=r=o=null,jn):qe(_=+t[0][0],i=+t[0][1],r=+t[1][0],o=+t[1][1]),E()):null==_?null:[[_,i],[r,o]]},j.scale=function(t){return arguments.length?(h=+t,C()):h},j.translate=function(t){return arguments.length?(p=+t[0],f=+t[1],C()):[p,f]},j.center=function(t){return arguments.length?(d=t[0]%360*L,g=t[1]%360*L,C()):[d*w,g*w]},j.rotate=function(t){return arguments.length?(v=t[0]%360*L,m=t[1]%360*L,y=t.length>2?t[2]%360*L:0,C()):[v*w,m*w,y*w]},j.angle=function(t){return arguments.length?(b=t%360*L,C()):b*w},j.precision=function(t){return arguments.length?(a=Gi(u,x=t*t),E()):F(x)},j.fitExtent=function(t,e){return wi(j,t,e)},j.fitSize=function(t,e){return Li(j,t,e)},j.fitWidth=function(t,e){return Ci(j,t,e)},j.fitHeight=function(t,e){return Ei(j,t,e)},function(){return e=t.apply(this,arguments),j.invert=e.invert&&O,C()}}function Ai(t){var e=0,n=M/3,i=Vi(t),r=i(e,n);return r.parallels=function(t){return arguments.length?i(e=t[0]*L,n=t[1]*L):[e*w,n*w]},r}function Fi(t,e){var n=V(t),i=(n+V(e))/2;if(C(i)<S)return function(t){var e=T(t);function n(t,n){return[t*e,V(n)/e]}return n.invert=function(t,n){return[t/e,B(n*e)]},n}(t);var r=1+n*(2*i-n),o=F(r)/i;function a(t,e){var n=F(r-2*i*V(e))/i;return[n*V(t*=i),o-n*T(t)]}return a.invert=function(t,e){var n=o-e;return[I(t,C(n))/i*A(n),B((r-(t*t+n*n)*i*i)/(2*i))]},a}var Ri=function(){return Ai(Fi).scale(155.424).center([0,33.6442])},ki=function(){return Ri().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])};var Bi=function(){var t,e,n,i,r,o,a=ki(),u=Ri().rotate([154,0]).center([-2,58.5]).parallels([55,65]),s=Ri().rotate([157,0]).center([-3,19.9]).parallels([8,18]),c={point:function(t,e){o=[t,e]}};function l(t){var e=t[0],a=t[1];return o=null,n.point(e,a),o||(i.point(e,a),o)||(r.point(e,a),o)}function h(){return t=e=null,l}return l.invert=function(t){var e=a.scale(),n=a.translate(),i=(t[0]-n[0])/e,r=(t[1]-n[1])/e;return(r>=.12&&r<.234&&i>=-.425&&i<-.214?u:r>=.166&&r<.234&&i>=-.214&&i<-.115?s:a).invert(t)},l.stream=function(n){return t&&e===n?t:t=function(t){var e=t.length;return{point:function(n,i){for(var r=-1;++r<e;)t[r].point(n,i)},sphere:function(){for(var n=-1;++n<e;)t[n].sphere()},lineStart:function(){for(var n=-1;++n<e;)t[n].lineStart()},lineEnd:function(){for(var n=-1;++n<e;)t[n].lineEnd()},polygonStart:function(){for(var n=-1;++n<e;)t[n].polygonStart()},polygonEnd:function(){for(var n=-1;++n<e;)t[n].polygonEnd()}}}([a.stream(e=n),u.stream(n),s.stream(n)])},l.precision=function(t){return arguments.length?(a.precision(t),u.precision(t),s.precision(t),h()):a.precision()},l.scale=function(t){return arguments.length?(a.scale(t),u.scale(.35*t),s.scale(t),l.translate(a.translate())):a.scale()},l.translate=function(t){if(!arguments.length)return a.translate();var e=a.scale(),o=+t[0],l=+t[1];return n=a.translate(t).clipExtent([[o-.455*e,l-.238*e],[o+.455*e,l+.238*e]]).stream(c),i=u.translate([o-.307*e,l+.201*e]).clipExtent([[o-.425*e+S,l+.12*e+S],[o-.214*e-S,l+.234*e-S]]).stream(c),r=s.translate([o-.205*e,l+.212*e]).clipExtent([[o-.214*e+S,l+.166*e+S],[o-.115*e-S,l+.234*e-S]]).stream(c),h()},l.fitExtent=function(t,e){return wi(l,t,e)},l.fitSize=function(t,e){return Li(l,t,e)},l.fitWidth=function(t,e){return Ci(l,t,e)},l.fitHeight=function(t,e){return Ei(l,t,e)},l.scale(1070)};function Hi(t){return function(e,n){var i=T(e),r=T(n),o=t(i*r);return[o*r*V(e),o*V(n)]}}function Wi(t){return function(e,n){var i=F(e*e+n*n),r=t(i),o=V(r),a=T(r);return[I(e*o,i*a),B(i&&n*o/i)]}}var Zi=Hi(function(t){return F(2/(1+t))});Zi.invert=Wi(function(t){return 2*B(t/2)});var qi=function(){return zi(Zi).scale(124.75).clipAngle(179.999)},Xi=Hi(function(t){return(t=k(t))&&t/V(t)});Xi.invert=Wi(function(t){return t});var Yi=function(){return zi(Xi).scale(79.4188).clipAngle(179.999)};function Ui(t,e){return[t,N(R((x+e)/2))]}Ui.invert=function(t,e){return[t,2*E(D(e))-x]};var Ji=function(){return Ki(Ui).scale(961/O)};function Ki(t){var e,n,i,r=zi(t),o=r.center,a=r.scale,u=r.translate,s=r.clipExtent,c=null;function l(){var o=M*a(),u=r(Se(r.rotate()).invert([0,0]));return s(null==c?[[u[0]-o,u[1]-o],[u[0]+o,u[1]+o]]:t===Ui?[[Math.max(u[0]-o,c),e],[Math.min(u[0]+o,n),i]]:[[c,Math.max(u[1]-o,e)],[n,Math.min(u[1]+o,i)]])}return r.scale=function(t){return arguments.length?(a(t),l()):a()},r.translate=function(t){return arguments.length?(u(t),l()):u()},r.center=function(t){return arguments.length?(o(t),l()):o()},r.clipExtent=function(t){return arguments.length?(null==t?c=e=n=i=null:(c=+t[0][0],e=+t[0][1],n=+t[1][0],i=+t[1][1]),l()):null==c?null:[[c,e],[n,i]]},l()}function Qi(t){return R((x+t)/2)}function $i(t,e){var n=T(t),i=t===e?V(t):N(n/T(e))/N(Qi(e)/Qi(t)),r=n*z(Qi(t),i)/i;if(!i)return Ui;function o(t,e){r>0?e<-x+S&&(e=-x+S):e>x-S&&(e=x-S);var n=r/z(Qi(e),i);return[n*V(i*t),r-n*T(i*t)]}return o.invert=function(t,e){var n=r-e,o=A(i)*F(t*t+n*n);return[I(t,C(n))/i*A(n),2*E(z(r/o,1/i))-x]},o}var tr=function(){return Ai($i).scale(109.5).parallels([30,30])};function er(t,e){return[t,e]}er.invert=er;var nr=function(){return zi(er).scale(152.63)};function ir(t,e){var n=T(t),i=t===e?V(t):(n-T(e))/(e-t),r=n/i+t;if(C(i)<S)return er;function o(t,e){var n=r-e,o=i*t;return[n*V(o),r-n*T(o)]}return o.invert=function(t,e){var n=r-e;return[I(t,C(n))/i*A(n),r-A(i)*F(t*t+n*n)]},o}var rr=function(){return Ai(ir).scale(131.154).center([0,13.9389])},or=1.340264,ar=-.081106,ur=893e-6,sr=.003796,cr=F(3)/2;function lr(t,e){var n=B(cr*V(e)),i=n*n,r=i*i*i;return[t*T(n)/(cr*(or+3*ar*i+r*(7*ur+9*sr*i))),n*(or+ar*i+r*(ur+sr*i))]}lr.invert=function(t,e){for(var n,i=e,r=i*i,o=r*r*r,a=0;a<12&&(o=(r=(i-=n=(i*(or+ar*r+o*(ur+sr*r))-e)/(or+3*ar*r+o*(7*ur+9*sr*r)))*i)*r*r,!(C(n)<_));++a);return[cr*t*(or+3*ar*r+o*(7*ur+9*sr*r))/T(i),B(V(i)/cr)]};var hr=function(){return zi(lr).scale(177.158)};function pr(t,e){var n=T(e),i=T(t)*n;return[n*V(t)/i,V(e)/i]}pr.invert=Wi(E);var fr=function(){return zi(pr).scale(144.049).clipAngle(60)};function dr(t,e,n,i){return 1===t&&1===e&&0===n&&0===i?jn:xi({point:function(r,o){this.stream.point(r*t+n,o*e+i)}})}var gr=function(){var t,e,n,i,r,o,a=1,u=0,s=0,c=1,l=1,h=jn,p=null,f=jn;function d(){return i=r=null,o}return o={stream:function(t){return i&&r===t?i:i=h(f(r=t))},postclip:function(i){return arguments.length?(f=i,p=t=e=n=null,d()):f},clipExtent:function(i){return arguments.length?(f=null==i?(p=t=e=n=null,jn):qe(p=+i[0][0],t=+i[0][1],e=+i[1][0],n=+i[1][1]),d()):null==p?null:[[p,t],[e,n]]},scale:function(t){return arguments.length?(h=dr((a=+t)*c,a*l,u,s),d()):a},translate:function(t){return arguments.length?(h=dr(a*c,a*l,u=+t[0],s=+t[1]),d()):[u,s]},reflectX:function(t){return arguments.length?(h=dr(a*(c=t?-1:1),a*l,u,s),d()):c<0},reflectY:function(t){return arguments.length?(h=dr(a*c,a*(l=t?-1:1),u,s),d()):l<0},fitExtent:function(t,e){return wi(o,t,e)},fitSize:function(t,e){return Li(o,t,e)},fitWidth:function(t,e){return Ci(o,t,e)},fitHeight:function(t,e){return Ei(o,t,e)}}};function vr(t,e){var n=e*e,i=n*n;return[t*(.8707-.131979*n+i*(i*(.003971*n-.001529*i)-.013791)),e*(1.007226+n*(.015085+i*(.028874*n-.044475-.005916*i)))]}vr.invert=function(t,e){var n,i=e,r=25;do{var o=i*i,a=o*o;i-=n=(i*(1.007226+o*(.015085+a*(.028874*o-.044475-.005916*a)))-e)/(1.007226+o*(.045255+a*(.259866*o-.311325-.005916*11*a)))}while(C(n)>S&&--r>0);return[t/(.8707+(o=i*i)*(o*(o*o*o*(.003971-.001529*o)-.013791)-.131979)),i]};var mr=function(){return zi(vr).scale(175.295)};function yr(t,e){return[T(e)*V(t),V(e)]}yr.invert=Wi(B);var br=function(){return zi(yr).scale(249.5).clipAngle(90+S)};function Pr(t,e){var n=T(e),i=1+T(t)*n;return[n*V(t)/i,V(e)/i]}Pr.invert=Wi(function(t){return 2*E(t)});var Sr=function(){return zi(Pr).scale(250).clipAngle(142)};function _r(t,e){return[N(R((x+e)/2)),-t]}_r.invert=function(t,e){return[-e,2*E(D(t))-x]};var Mr=function(){var t=Ki(_r),e=t.center,n=t.rotate;return t.center=function(t){return arguments.length?e([-t[1],t[0]]):[(t=e())[1],-t[0]]},t.rotate=function(t){return arguments.length?n([t[0],t[1],t.length>2?t[2]+90:90]):[(t=n())[0],t[1],t[2]-90]},n([0,0,90]).scale(159.155)},xr=function(t){function e(){var e=t.call(this)||this;return e.className="MapSeriesDataItem",e.values.value={},e.applyTheme(),e}return Object(c.c)(e,t),Object.defineProperty(e.prototype,"value",{get:function(){return this.values.value.value},set:function(t){this.setValue("value",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomLevel",{get:function(){return this.properties.zoomLevel},set:function(t){this.setProperty("zoomLevel",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomGeoPoint",{get:function(){return this.properties.zoomGeoPoint},set:function(t){this.setProperty("zoomGeoPoint",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"east",{get:function(){return this._east},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"west",{get:function(){return this._west},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"south",{get:function(){return this._south},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"north",{get:function(){return this._north},enumerable:!0,configurable:!0}),e.prototype.updateExtremes=function(){var t=this.getFeature().geometry;if(t){var e=ee(t),n=e[0][0],i=e[0][1],r=e[1][1],o=e[1][0],a=!1;r!=this.north&&(this._north=v.round(r,6),a=!0),i!=this.south&&(this._south=v.round(i,6),a=!0),o!=this.east&&(this._east=v.round(o,6),a=!0),n!=this.west&&(this._west=v.round(n,6),a=!0),this._east<this._west&&(this._east=180,this._west=-180),a&&this.component.invalidateDataItems()}},e.prototype.getFeature=function(){return{}},e}(f.b),jr=function(t){function e(){var e=t.call(this)||this;return e.className="MapSeries",e.isMeasured=!1,e.nonScalingStroke=!0,e.dataFields.value="value",e.ignoreBounds=!1,e.tooltip&&(e.tooltip.showInViewport=!0),e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new xr},e.prototype.checkInclude=function(t,e,n){if(t){if(0==t.length)return!1;if(-1==t.indexOf(n))return!1}return!(e&&e.length>0&&-1!=e.indexOf(n))},Object.defineProperty(e.prototype,"useGeodata",{get:function(){return this.getPropertyValue("useGeodata")},set:function(t){this.setPropertyValue("useGeodata",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"include",{get:function(){return this.getPropertyValue("include")},set:function(t){this.setPropertyValue("include",t)&&this.processIncExc()},enumerable:!0,configurable:!0}),e.prototype.processIncExc=function(){this.invalidateData()},Object.defineProperty(e.prototype,"ignoreBounds",{get:function(){return this.getPropertyValue("ignoreBounds")},set:function(t){this.setPropertyValue("ignoreBounds",t)&&this.chart&&this.chart.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"exclude",{get:function(){return this.getPropertyValue("exclude")},set:function(t){this.setPropertyValue("exclude",t)&&this.processIncExc()},enumerable:!0,configurable:!0}),e.prototype.handleObjectAdded=function(t){var e=t.newValue;e.parent=this,e.series=this,e.strokeWidth=e.strokeWidth},Object.defineProperty(e.prototype,"geodata",{get:function(){return this._geodata},set:function(t){if(t!=this._geodata){this._geodata=t;for(var e=this.data.length-1;e>=0;e--)1==this.data[e].madeFromGeoData&&this.data.splice(e,1);this.disposeData(),this.invalidateData()}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"geodataSource",{get:function(){return this._dataSources.geodata||this.getDataSource("geodata"),this._dataSources.geodata},set:function(t){var e=this;this._dataSources.geodata&&this.removeDispose(this._dataSources.geodata),this._dataSources.geodata=t,this._dataSources.geodata.component=this,this.events.on("inited",function(){e.loadData("geodata")},void 0,!1),this.setDataSourceEvents(t,"geodata")},enumerable:!0,configurable:!0}),e.prototype.getFeatures=function(){},e.prototype.validateDataItems=function(){t.prototype.validateDataItems.call(this),this.updateExtremes()},e.prototype.updateExtremes=function(){var t,e,n,i;this.dataItems.each(function(r){(r.north>t||!g.isNumber(t))&&(t=r.north),(r.south<e||!g.isNumber(e))&&(e=r.south),(r.west<i||!g.isNumber(i))&&(i=r.west),(r.east>n||!g.isNumber(n))&&(n=r.east)}),this._mapObjects&&this._mapObjects.each(function(r){(r.north>t||!g.isNumber(t))&&(t=r.north),(r.south<e||!g.isNumber(e))&&(e=r.south),(r.west<i||!g.isNumber(i))&&(i=r.west),(r.east>n||!g.isNumber(n))&&(n=r.east)}),this.north==t&&this.east==n&&this.south==e&&this.west==i||(this._north=t,this._east=n,this._west=i,this._south=e,this.dispatch("geoBoundsChanged"),this.ignoreBounds||this.chart.updateExtremes())},Object.defineProperty(e.prototype,"north",{get:function(){return g.isNumber(this._northDefined)?this._northDefined:this._north},set:function(t){this._northDefined=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"south",{get:function(){return g.isNumber(this._southDefined)?this._southDefined:this._south},set:function(t){this._southDefined=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"west",{get:function(){return g.isNumber(this._westDefined)?this._westDefined:this._west},set:function(t){this._westDefined=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"east",{get:function(){return g.isNumber(this._eastDefined)?this._eastDefined:this._east},set:function(t){this._eastDefined=t},enumerable:!0,configurable:!0}),e.prototype.processConfig=function(e){if(g.hasValue(e.geodata)&&g.isString(e.geodata)){var n=e.geodata;if(g.hasValue(window["am4geodata_"+e.geodata]))e.geodata=window["am4geodata_"+e.geodata];else try{e.geodata=JSON.parse(e.geodata)}catch(t){throw Error("MapChart error: Geodata `"+n+"` is not loaded or is incorrect.")}}t.prototype.processConfig.call(this,e)},e.prototype.asIs=function(e){return"geodata"==e||t.prototype.asIs.call(this,e)},e.prototype.updateTooltipBounds=function(){this.tooltip&&this.topParent&&this.tooltip.setBounds({x:10,y:10,width:this.topParent.maxWidth-20,height:this.topParent.maxHeight-20})},e}(f.a);d.c.registeredClasses.MapSeries=jr,d.c.registeredClasses.MapSeriesDataItem=xr;var Or=n("C6dT"),wr=function(t){function e(){var e=t.call(this)||this;return e.className="MapObject",e.isMeasured=!1,e.layout="none",e.clickable=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.validate=function(){this.series&&(this.readerTitle=this.series.itemReaderText),t.prototype.validate.call(this)},e.prototype.updateExtremes=function(){var t=this.getFeature();if(t){var e=t.geometry;if(e){var n=ee(e),i=n[0][0],r=n[0][1],o=n[1][1],a=n[1][0],u=!1;o!=this.north&&(this._north=v.round(o,8),u=!0),r!=this.south&&(this._south=v.round(r),u=!0),a!=this.east&&(this._east=v.round(a),u=!0),i!=this.west&&(this._west=v.round(i),u=!0),u&&(this.dispatch("geoBoundsChanged"),this.series&&this.series.invalidateDataItems())}}},e.prototype.getFeature=function(){return{}},Object.defineProperty(e.prototype,"east",{get:function(){return g.isNumber(this._east)?this._east:this.dataItem?this.dataItem.east:void 0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"west",{get:function(){return g.isNumber(this._west)?this._west:this.dataItem?this.dataItem.west:void 0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"south",{get:function(){return g.isNumber(this._south)?this._south:this.dataItem?this.dataItem.south:void 0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"north",{get:function(){return g.isNumber(this._north)?this._north:this.dataItem?this.dataItem.north:void 0},enumerable:!0,configurable:!0}),e.prototype.showTooltip=function(e){var n=t.prototype.showTooltip.call(this,e);return n&&"always"==this.showTooltipOn&&!this.series.chart.events.has("mappositionchanged",this.handleTooltipMove,this)&&this.addDisposer(this.series.chart.events.on("mappositionchanged",this.handleTooltipMove,this)),n},e.prototype.handleTooltipMove=function(t){this.tooltip.isHidden||this.showTooltip()},e}(Or.a);d.c.registeredClasses.MapObject=wr;var Lr=function(t){function e(){var e=t.call(this)||this;return e.className="MapImage",e.applyTheme(),e}return Object(c.c)(e,t),Object.defineProperty(e.prototype,"latitude",{get:function(){var t=this.getPropertyValue("latitude");return!g.isNumber(t)&&this.dataItem&&this.dataItem.geoPoint&&(t=this.dataItem.geoPoint.latitude),t},set:function(t){this.setPropertyValue("latitude",t,!1,!0),this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"longitude",{get:function(){var t=this.getPropertyValue("longitude");return!g.isNumber(t)&&this.dataItem&&this.dataItem.geoPoint&&(t=this.dataItem.geoPoint.longitude),t},set:function(t){this.setPropertyValue("longitude",t,!1,!0),this.updateExtremes()},enumerable:!0,configurable:!0}),e.prototype.validatePosition=function(){if(g.isNumber(this.latitude)&&g.isNumber(this.longitude)){var e=this.series.chart.projection.d3Projection([this.longitude,this.latitude]),n=this.series.chart.projection.d3Path({type:"Point",coordinates:[this.longitude,this.latitude]});this.__disabled=!n,this.moveTo({x:e[0],y:e[1]})}t.prototype.validatePosition.call(this)},e.prototype.getFeature=function(){return{type:"Feature",geometry:{type:"Point",coordinates:[this.longitude,this.latitude]}}},e}(wr);d.c.registeredClasses.MapImage=Lr;var Cr=n("R6wv"),Er=n("U8r1"),Ir=n.n(Er),Tr=n("hJ5i");function Gr(t){return Tr.map(t,function(t){var e=t[0],n=t[1],i=[];return e&&i.push(Nr(e)),n&&i.push(Nr(n)),i})}function Dr(t){return Tr.map(t,function(t){return Nr(t)})}function Nr(t){return Tr.map(t,function(t){return Vr(t)})}function zr(t){return Tr.map(t,Ar)}function Vr(t){return{longitude:t[0],latitude:t[1]}}function Ar(t){return[t.longitude,t.latitude]}function Fr(t){return Tr.map(t,function(t){return Tr.map(t,Ar)})}function Rr(t){return Tr.map(t,function(t){var e=t[0],n=t[1],i=[];return e&&i.push(zr(e)),n&&i.push(zr(n)),i})}function kr(t,e,n){return[xe().center([t,e]).radius(n)().coordinates]}function Br(t,e,n,i){var r=[];-180==i&&(i=-179.9999),-90==n&&(n=-89.9999),90==t&&(t=89.9999),180==e&&(e=179.9999);for(var o=Math.min(90,(e-i)/Math.ceil((e-i)/90)),a=(t-n)/Math.ceil((t-n)/90),u=i;u<e;u+=o){var s=[];r.push([s]),u+o>e&&(o=e-u);for(var c=u;c<=u+o;c+=5)s.push([c,t]);for(var l=t;l>=n;l-=a)s.push([u+o,l]);for(c=u+o;c>=u;c-=5)s.push([c,n]);for(l=n;l<=t;l+=a)s.push([u,l])}return r}var Hr=function(t){function e(){var e=t.call(this)||this;e.className="MapPolygon",e.polygon=e.createChild(Cr.a),e.polygon.shouldClone=!1,e.polygon.applyOnClones=!0,e.setPropertyValue("precision",.5);var n=new p.a;return e.fill=n.getFor("secondaryButton"),e.stroke=n.getFor("secondaryButtonStroke"),e.strokeOpacity=1,e.tooltipPosition="pointer",e.nonScalingStroke=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.getFeature=function(){if(this.multiPolygon&&this.multiPolygon.length>0)return{type:"Feature",geometry:{type:"MultiPolygon",coordinates:this.multiPolygon}}},Object.defineProperty(e.prototype,"multiGeoPolygon",{get:function(){var t=this.getPropertyValue("multiGeoPolygon");return!t&&this.dataItem&&(t=this.dataItem.multiGeoPolygon),t},set:function(t){this.setPropertyValue("multiGeoPolygon",t,!0),this.multiPolygon=Rr(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiPolygon",{get:function(){var t=this.getPropertyValue("multiPolygon");return!t&&this.dataItem&&(t=this.dataItem.multiPolygon),t},set:function(t){this.setPropertyValue("multiPolygon",t)&&(this.updateExtremes(),this.invalidate())},enumerable:!0,configurable:!0}),e.prototype.validate=function(){if(this.series){var e=this.series.chart.projection,n=e.d3Path;if(this.multiPolygon){if(this.series){var i={type:"MultiPolygon",coordinates:this.multiPolygon};e.d3Projection.precision(this.precision),this.polygon.path=n(i)}if(this.series.calculateVisualCenter){var r=0,o=this.multiPolygon[0];if(this.multiPolygon.length>1)for(var a=0;a<this.multiPolygon.length;a++){var u=this.multiPolygon[a],s=ct({type:"Polygon",coordinates:u});s>r&&(o=u,r=s)}var c=Ir()(o);this._visualLongitude=c[0],this._visualLatitude=c[1]}else this._visualLongitude=this.longitude,this._visualLatitude=this.latitude}}t.prototype.validate.call(this)},e.prototype.measureElement=function(){},Object.defineProperty(e.prototype,"latitude",{get:function(){return this.north+(this.south-this.north)/2},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"longitude",{get:function(){return this.east+(this.west-this.east)/2},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visualLatitude",{get:function(){var t=this.getPropertyValue("visualLatitude");return g.isNumber(t)?t:this._adapterO?this._adapterO.apply("visualLatitude",this._visualLatitude):this._visualLatitude},set:function(t){this.setPropertyValue("visualLatitude",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visualLongitude",{get:function(){var t=this.getPropertyValue("visualLongitude");return g.isNumber(t)?t:this._adapterO?this._adapterO.apply("visualLongitude",this._visualLongitude):this._visualLongitude},set:function(t){this.setPropertyValue("visualLongitude",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelWidth",{get:function(){return this.polygon.pixelWidth},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelHeight",{get:function(){return this.polygon.pixelHeight},enumerable:!0,configurable:!0}),e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.polygon.copyFrom(e.polygon)},e.prototype.updateExtremes=function(){t.prototype.updateExtremes.call(this)},Object.defineProperty(e.prototype,"boxArea",{get:function(){return(this.north-this.south)*(this.east-this.west)},enumerable:!0,configurable:!0}),e.prototype.getTooltipX=function(){return this.series.chart.projection.convert({longitude:this.visualLongitude,latitude:this.visualLatitude}).x},e.prototype.getTooltipY=function(){return this.series.chart.projection.convert({longitude:this.visualLongitude,latitude:this.visualLatitude}).y},Object.defineProperty(e.prototype,"precision",{get:function(){return this.getPropertyValue("precision")},set:function(t){this.setPropertyValue("precision",t,!0)},enumerable:!0,configurable:!0}),e}(wr);d.c.registeredClasses.MapPolygon=Hr;var Wr=n("vMqJ"),Zr=n("v9UT"),qr=n("Wglt"),Xr=function(t){function e(){var e=t.call(this)||this;return e.className="MapPolygonSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.getFeature=function(){if(this.multiPolygon&&this.multiPolygon.length>0)return{type:"Feature",geometry:{type:"MultiPolygon",coordinates:this.multiPolygon}}},Object.defineProperty(e.prototype,"mapPolygon",{get:function(){var t=this;if(!this._mapPolygon){var e=this.component.mapPolygons.create();this._mapPolygon=e,this.addSprite(e),this._disposers.push(new h.b(function(){t.component&&t.component.mapPolygons.removeValue(e)})),this.mapObject=e}return this._mapPolygon},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"polygon",{get:function(){return this._polygon},set:function(t){this._polygon=t,this.multiPolygon=[t]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiPolygon",{get:function(){return this._multiPolygon},set:function(t){this._multiPolygon=t,this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"geoPolygon",{get:function(){return this._geoPolygon},set:function(t){this._geoPolygon=t,this.multiGeoPolygon=[t]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiGeoPolygon",{get:function(){return this._multiGeoPolygon},set:function(t){this._multiGeoPolygon=t,this.multiPolygon=Rr(t)},enumerable:!0,configurable:!0}),e}(xr),Yr=function(t){function e(){var e=t.call(this)||this;return e.calculateVisualCenter=!1,e.className="MapPolygonSeries",e.dataFields.multiPolygon="multiPolygon",e.dataFields.polygon="polygon",e.dataFields.geoPolygon="geoPolygon",e.dataFields.multiGeoPolygon="multiGeoPolygon",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new Xr},e.prototype.processIncExc=function(){this.mapPolygons.clear(),t.prototype.processIncExc.call(this)},e.prototype.validateData=function(){if(this.useGeodata||this.geodata){var e=this._dataSources.geodata?void 0:this.chart.geodata;if(this.geodata&&(e=this.geodata),e){var n=void 0;if("FeatureCollection"==e.type?n=e.features:"Feature"==e.type?n=[e]:-1!=["Point","LineString","Polygon","MultiPoint","MultiLineString","MultiPolygon"].indexOf(e.type)?n=[{geometry:e}]:console.log("nothing found in geoJSON"),n)for(var i=function(t,e){var i=n[t],o=i.geometry;if(o){var a=o.type,u=i.id;if(r.chart.geodataNames&&r.chart.geodataNames[u]&&(i.properties.name=r.chart.geodataNames[u]),"Polygon"==a||"MultiPolygon"==a){if(!r.checkInclude(r.include,r.exclude,u))return"continue";var s=o.coordinates;s&&"Polygon"==a&&(s=[s]);var c=Tr.find(r.data,function(t,e){return t.id==u});c?c.multiPolygon||(c.multiPolygon=s):(c={multiPolygon:s,id:u,madeFromGeoData:!0},r.data.push(c)),Zr.softCopyProperties(i.properties,c)}}},r=this,o=0,a=n.length;o<a;o++)i(o)}}t.prototype.validateData.call(this)},e.prototype.validate=function(){t.prototype.validate.call(this),this.dataItems.each(function(t){Zr.used(t.mapPolygon)}),this.mapPolygons.each(function(t){t.validate(),t.zIndex||t.propertyFields.zIndex||(t.zIndex=1e6-t.boxArea)})},Object.defineProperty(e.prototype,"mapPolygons",{get:function(){if(!this._mapPolygons){var t=new Hr,e=new Wr.e(t);this._disposers.push(new Wr.c(e)),this._disposers.push(e.template),e.template.focusable=!0,e.events.on("inserted",this.handleObjectAdded,this,!1),this._mapPolygons=e,this._mapObjects=e}return this._mapPolygons},enumerable:!0,configurable:!0}),e.prototype.getPolygonById=function(t){return qr.find(this.mapPolygons.iterator(),function(e){return e.dataItem.dataContext.id==t})},e.prototype.copyFrom=function(e){this.mapPolygons.template.copyFrom(e.mapPolygons.template),t.prototype.copyFrom.call(this,e)},e.prototype.getFeatures=function(){var t=this,e=[];return this.dataItems.each(function(t){var n=t.getFeature();n&&e.push(n)}),this.mapPolygons.each(function(n){if(-1==t.dataItems.indexOf(n._dataItem)){var i=n.getFeature();i&&e.push(i)}}),e},e}(jr);d.c.registeredClasses.MapPolygonSeries=Yr,d.c.registeredClasses.MapPolygonSeriesDataItem=Xr;var Ur=function(){function t(){this.d3Projection=nr()}return Object.defineProperty(t.prototype,"d3Projection",{get:function(){return this._d3Projection},set:function(t){this._d3Projection=t,t.precision(.1),this._d3Path=_i().projection(t),this.chart&&this.chart.invalidateProjection()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"d3Path",{get:function(){return this._d3Path},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"scale",{get:function(){return this.d3Projection.scale()/100},enumerable:!0,configurable:!0}),t.prototype.convert=function(t){var e=this.d3Projection([t.longitude,t.latitude]);if(e)return{x:e[0],y:e[1]}},t.prototype.invert=function(t){var e=this.d3Projection.invert([t.x,t.y]);if(e)return{longitude:e[0],latitude:e[1]}},t.prototype.project=function(t,e){return this.convert({longitude:t*v.DEGREES,latitude:e*v.DEGREES})},t.prototype.unproject=function(t,e){return this.invert({x:t,y:e})},t.prototype.rotate=function(t,e,n,i){var r=e*v.RADIANS,o=n*v.RADIANS;i*=v.RADIANS;var a=t.longitude*v.RADIANS+r,u=t.latitude*v.RADIANS,s=Math.cos(o),c=Math.sin(o),l=Math.cos(i),h=Math.sin(i),p=Math.cos(u),f=Math.cos(a)*p,d=Math.sin(a)*p,g=Math.sin(u),m=g*s+f*c;return{longitude:v.DEGREES*Math.atan2(d*l-m*h,f*s-g*c),latitude:v.DEGREES*Math.asin(m*l+d*h)}},t.prototype.unrotate=function(t,e,n,i){var r=e*v.RADIANS,o=n*v.RADIANS;i*=v.RADIANS;var a=t.longitude*v.RADIANS-r,u=t.latitude*v.RADIANS,s=Math.cos(o),c=Math.sin(o),l=Math.cos(i),h=Math.sin(i),p=Math.cos(u),f=Math.cos(a)*p,d=Math.sin(a)*p,g=Math.sin(u),m=g*l-d*h;return{longitude:v.DEGREES*Math.atan2(d*l+g*h,f*s+m*c),latitude:v.DEGREES*Math.asin(m*s-f*c)}},t.prototype.intermediatePoint=function(t,e,n){var i=xn([t.longitude,t.latitude],[e.longitude,e.latitude])(n);return{longitude:i[0],latitude:i[1]}},t.prototype.multiDistance=function(t){for(var e=0,n=0;n<t.length;n++){var i=t[n];if(i.length>1)for(var r=1;r<i.length;r++){var o=i[r-1],a=i[r];e+=this.distance(o,a)}}return e},t.prototype.distance=function(t,e){return an([t.longitude,t.latitude],[e.longitude,e.latitude])},t.prototype.positionToPoint=function(t,e){if(t){var n=this.positionToGeoPoint(t,e),i=this.positionToGeoPoint(t,e-.01),r=this.positionToGeoPoint(t,e+.01);if(i&&r){var o=this.convert(n),a=this.convert(i),u=this.convert(r);return{x:o.x,y:o.y,angle:v.getAngle(a,u)}}}return{x:0,y:0,angle:0}},t.prototype.positionToGeoPoint=function(t,e){if(t){for(var n=this.multiDistance(t),i=0,r=0,o=0,a=void 0,u=void 0,s=0;s<t.length;s++){var c=t[s];if(c.length>1){for(var l=1;l<c.length;l++)if(a=c[l-1],u=c[l],r=i/n,o=(i+=this.distance(a,u))/n,r<=e&&o>e){s=t.length;break}}else 1==c.length&&(a=c[0],u=c[0],r=0,o=1)}if(a&&u){var h=(e-r)/(o-r);return this.intermediatePoint(a,u,h)}}return{longitude:0,latitude:0}},t}();d.c.registeredClasses.Projection=Ur;var Jr=n("FzPm"),Kr=n("GtDR"),Qr=n("8ZqG"),$r=function(t){function e(){var e=t.call(this)||this;e._chart=new h.d,e.className="SmallMap",e.align="left",e.valign="bottom",e.percentHeight=20,e.percentWidth=20,e.margin(5,5,5,5);var n=new p.a;e.background.fillOpacity=.9,e.background.fill=n.getFor("background"),e.events.on("hit",e.moveToPosition,e,!1),e.events.on("maxsizechanged",e.updateMapSize,e,!1),e.seriesContainer=e.createChild(Or.a),e.seriesContainer.shouldClone=!1;var i=e.createChild(Kr.a);return i.shouldClone=!1,i.stroke=n.getFor("alternativeBackground"),i.strokeWidth=1,i.strokeOpacity=.5,i.fill=Object(Qr.c)(),i.verticalCenter="middle",i.horizontalCenter="middle",i.isMeasured=!1,i.visible=!1,e.rectangle=i,e._disposers.push(e._chart),e.applyTheme(),e}return Object(c.c)(e,t),Object.defineProperty(e.prototype,"series",{get:function(){return this._series||(this._series=new Wr.b,this._series.events.on("inserted",this.handleSeriesAdded,this,!1),this._series.events.on("removed",this.handleSeriesRemoved,this,!1)),this._series},enumerable:!0,configurable:!0}),e.prototype.handleSeriesAdded=function(t){var e=t.newValue;if(this.chart.series.contains(e)){var n=e.clone();this._series.removeValue(e),this._series.push(n),e=n,this.chart.dataUsers.push(n)}e.chart=this.chart,e.parent=this.seriesContainer,e.interactionsEnabled=!1,e.events.on("inited",this.updateMapSize,this,!1),e.hidden=!1},e.prototype.handleSeriesRemoved=function(t){this.invalidate()},e.prototype.moveToPosition=function(t){var e=Zr.spritePointToSprite(t.spritePoint,this,this.seriesContainer),n=this.chart.seriesPointToGeo(e);this.chart.zoomToGeoPoint(n,this.chart.zoomLevel,!0)},Object.defineProperty(e.prototype,"chart",{get:function(){return this._chart.get()},set:function(t){this.chart!=t&&this._chart.set(t,new h.c([t.events.on("mappositionchanged",this.updateRectangle,this,!1),t.events.on("scaleratiochanged",this.updateMapSize,this,!1)]))},enumerable:!0,configurable:!0}),e.prototype.updateRectangle=function(){var t=this.chart,e=t.zoomLevel,n=this.rectangle;n.width=this.pixelWidth/e,n.height=this.pixelHeight/e;var i=Math.min(this.percentWidth,this.percentHeight)/100,r=t.seriesContainer;n.x=Math.ceil(-r.pixelX*i/e)+this.seriesContainer.pixelX,n.y=Math.ceil(-r.pixelY*i/e)+this.seriesContainer.pixelY,n.validate()},e.prototype.updateMapSize=function(){if(this.chart){var t=this.chart.scaleRatio*Math.min(this.percentWidth,this.percentHeight)/100;this.seriesContainer.scale=t;var e={width:0,height:0,x:0,y:0};try{e=this.seriesContainer.group.node.getBBox()}catch(t){}e.width>0&&(this.rectangle.visible=!0),this.seriesContainer.x=this.pixelWidth/2-e.x*t-e.width/2*t,this.seriesContainer.y=this.pixelHeight/2-e.y*t-e.height/2*t,this.updateRectangle(),this.afterDraw()}},e.prototype.afterDraw=function(){t.prototype.afterDraw.call(this),this.rectangle.maskRectangle={x:-1,y:-1,width:Math.ceil(this.pixelWidth+2),height:Math.ceil(this.pixelHeight+2)}},e.prototype.processConfig=function(e){if(e&&g.hasValue(e.series)&&g.isArray(e.series))for(var n=0,i=e.series.length;n<i;n++){var r=e.series[n];g.hasValue(r)&&g.isString(r)&&this.map.hasKey(r)&&(e.series[n]=this.map.getKey(r))}t.prototype.processConfig.call(this,e)},e}(Or.a);d.c.registeredClasses.SmallMap=$r;var to=n("WYhe"),eo=n("0FpR");function no(t){var e=ro(t.longitude),n=Math.asin(Math.sin(t.latitude*v.RADIANS))*v.DEGREES,i=ro(t.latitude);return Math.abs(i)>90&&(e=ro(e+180)),t.longitude=e,t.latitude=n,t}function io(t){return Tr.each(t,function(t){Tr.each(t,function(t){no(t)})}),t}function ro(t){return(t%=360)>180&&(t-=360),t<-180&&(t+=360),t}function oo(t){return{x:t.longitude,y:t.latitude}}var ao=function(t){function e(){var e=t.call(this)||this;return e.adjustRotation=!0,e.className="MapLineObject",e.isMeasured=!1,e.layout="none",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.validatePosition=function(){var e=this.mapLine;if(e){var n=e.positionToPoint(this.position);if(this.x=n.x,this.y=n.y,this.adjustRotation&&(this.rotation=n.angle),this.mapLine.dataItem){var i=this.mapLine.dataItem.component;this.scale=1/i.scale}if(e.shortestDistance){var r=this.mapLine.series.chart.projection,o=r.positionToGeoPoint(e.multiGeoLine,this.position),a=r.d3Path({type:"Point",coordinates:[o.longitude,o.latitude]});this.__disabled=!a}}t.prototype.validatePosition.call(this)},Object.defineProperty(e.prototype,"position",{get:function(){return this.getPropertyValue("position")},set:function(t){this.setPropertyValue("position",t,!1,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"adjustRotation",{get:function(){return this.getPropertyValue("adjustRotation")},set:function(t){this.setPropertyValue("adjustRotation",t,!1,!0)},enumerable:!0,configurable:!0}),e}(Or.a);d.c.registeredClasses.MapLineObject=ao;var uo=function(t){function e(){var e=t.call(this)||this;return e.className="MapImageSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.getFeature=function(){return{type:"Feature",geometry:{type:"Point",coordinates:this.point}}},Object.defineProperty(e.prototype,"mapImage",{get:function(){var t=this;if(!this._mapImage){var e=this.component.mapImages.create();this.addSprite(e),this._mapImage=e,this._disposers.push(e),this._disposers.push(new h.b(function(){t.component&&t.component.mapImages.removeValue(e)})),this.mapObject=e}return this._mapImage},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"point",{get:function(){return this._point},set:function(t){this._point=t,this._geoPoint=Vr(t),this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiPoint",{get:function(){return[this._point]},set:function(t){this._point=t[0],this._geoPoint=Vr(this._point),this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"geoPoint",{get:function(){return this._geoPoint},set:function(t){this._geoPoint=t,this.point=[t.longitude,t.latitude]},enumerable:!0,configurable:!0}),e}(xr),so=function(t){function e(){var e=t.call(this)||this;return e.className="MapImageSeries",e.dataFields.multiPoint="multiPoint",e.dataFields.point="point",e.dataFields.geoPoint="geoPoint",e.dataFields.multiGeoPoint="multiGeoPoint",e.ignoreBounds=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new uo},e.prototype.validateData=function(){var e=this;if(this.data.length>0&&0==this._parseDataFrom&&this.mapImages.clear(),this.useGeodata&&(this.useGeodata||this.geodata)){var n=this.chart.geodata,i=void 0;if("FeatureCollection"==n.type?i=n.features:"Feature"==n.type?i=[n]:-1!=["Point","LineString","Polygon","MultiPoint","MultiLineString","MultiPolygon"].indexOf(n.type)?i=[{geometry:n}]:console.log("nothing found in geoJSON"),i)for(var r=function(t,e){var n=i[t],r=n.geometry;if(r){var a=r.type,u=n.id;if("Point"==a||"MultiPoint"==a){if(!o.checkInclude(o.include,o.exclude,u))return"continue";var s=r.coordinates;"Point"==a&&(s=[s]);var c=Tr.find(o.data,function(t,e){return t.id==u});c?c.multiPoint||(c.multiPoint=s):(c={multiPoint:s,id:u,madeFromGeoData:!0},o.data.push(c)),Zr.softCopyProperties(n.properties,c)}}},o=this,a=0,u=i.length;a<u;a++)r(a)}t.prototype.validateData.call(this),qr.each(this.dataItems.iterator(),function(t){var n=t.mapImage;n.isDisposed()||(e.mapImages.moveValue(n),g.isNumber(n.latitude)&&g.isNumber(n.latitude)&&(t.geoPoint={latitude:n.latitude,longitude:n.longitude}))})},Object.defineProperty(e.prototype,"mapImages",{get:function(){if(!this._mapImages){var t=new Lr,e=new Wr.e(t);this._disposers.push(new Wr.c(e)),this._disposers.push(e.template),e.template.focusable=!0,e.events.on("inserted",this.handleObjectAdded,this,!1),this._mapImages=e,this._mapObjects=e}return this._mapImages},enumerable:!0,configurable:!0}),e.prototype.validateDataElement=function(e){t.prototype.validateDataElement.call(this,e),e.mapImage.invalidate()},e.prototype.validate=function(){t.prototype.validate.call(this),qr.each(this.mapImages.iterator(),function(t){t.validatePosition()})},e.prototype.copyFrom=function(e){this.mapImages.template.copyFrom(e.mapImages.template),t.prototype.copyFrom.call(this,e)},e.prototype.getFeatures=function(){var t=this,e=[];return this.dataItems.each(function(t){var n=t.getFeature();n&&e.push(n)}),this.mapImages.each(function(n){if(-1==t.dataItems.indexOf(n._dataItem)){var i=n.getFeature();i&&e.push(i)}}),e},e.prototype.getImageById=function(t){return qr.find(this.mapImages.iterator(),function(e){var n=e.dataItem.dataContext;if(e.id==t||n&&n.id==t)return!0})},e}(jr);d.c.registeredClasses.MapImageSeries=so,d.c.registeredClasses.MapImageSeriesDataItem=uo;var co=n("Rnbi"),lo=n("jfaP"),ho=n("tjMS"),po=function(t){function e(){var e=t.call(this)||this;e._imageListeners={},e.className="MapLine",e.createLine(),e.line.stroke=Object(Qr.c)(),e.line.parent=e,e.strokeOpacity=1,e.setPropertyValue("precision",.1);var n=new p.a;return e.stroke=n.getFor("grid"),e.shortestDistance=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createLine=function(){this.line=new lo.a},e.prototype.positionToPoint=function(t){return this.shortestDistance?this.series.chart.projection.positionToPoint(this.multiGeoLine,t):this.line?this.line.positionToPoint(t):{x:0,y:0,angle:0}},Object.defineProperty(e.prototype,"multiGeoLine",{get:function(){var t=this.getPropertyValue("multiGeoLine");return!t&&this.dataItem&&this.dataItem.multiGeoLine&&(t=this.dataItem.multiGeoLine),t},set:function(t){if(t&&t.length>0){this.setPropertyValue("multiGeoLine",io(t),!0);var e=Fr(t);this.setPropertyValue("multiLine",e),this.updateExtremes()}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiLine",{get:function(){var t=this.getPropertyValue("multiLine");return!t&&this.dataItem&&this.dataItem.multiLine&&(t=this.dataItem.multiLine),t},set:function(t){this.setPropertyValue("multiLine",t),this.multiGeoLine=Dr(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"imagesToConnect",{get:function(){return this.getPropertyValue("imagesToConnect")},set:function(t){var e=this;if(this.setPropertyValue("imagesToConnect",t,!0),this.handleImagesToConnect(),this.series){var n=this.series.chart;n&&n.series.each(function(t){t instanceof so&&(t.isReady()||e._disposers.push(t.events.on("ready",e.handleImagesToConnect,e,!1)))})}},enumerable:!0,configurable:!0}),e.prototype.handleImagesToConnect=function(){var t,e,n=this;if(this.imagesToConnect){var i=[],r=[i],o=function(t){if(g.isString(t)){var e=a.series.chart;e&&e.series.each(function(e){if(e instanceof so){var n=e.getImageById(t);n&&(t=n)}})}if(t instanceof Lr&&(i.push({longitude:t.longitude,latitude:t.latitude}),!a._imageListeners[t.uid])){var r=t.events.on("propertychanged",function(t){"longitude"!=t.property&&"latitude"!=t.property||(n.handleImagesToConnect(),n.invalidate())},a,!1);a._imageListeners[t.uid]=r,a._disposers.push(r)}},a=this;try{for(var u=Object(c.g)(this.imagesToConnect),s=u.next();!s.done;s=u.next()){o(s.value)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(e=u.return)&&e.call(u)}finally{if(t)throw t.error}}this.multiGeoLine=r}},e.prototype.validate=function(){var e=this.series.chart;if(this.multiLine){if(this.shortestDistance)e.projection.d3Projection.precision(this.precision),this.line.path=e.projection.d3Path(this.getFeature());else{for(var n=[],i=0,r=this.multiLine.length;i<r;i++){for(var o=this.multiLine[i],a=[],u=0,s=o.length;u<s;u++){var c=o[u],l=this.series.chart.projection.convert({longitude:c[0],latitude:c[1]});a.push(l)}n.push(a)}this.line.segments=n}this._arrow&&this._arrow.validatePosition(),qr.each(this.lineObjects.iterator(),function(t){t.validatePosition()}),this.handleGlobalScale()}else this.imagesToConnect&&this.handleImagesToConnect();t.prototype.validate.call(this)},e.prototype.getFeature=function(){if(this.multiLine&&this.multiLine.length>0&&this.multiLine[0]&&this.multiLine[0].length>0)return{type:"Feature",geometry:{type:"MultiLineString",coordinates:this.multiLine}}},e.prototype.measureElement=function(){},Object.defineProperty(e.prototype,"shortestDistance",{get:function(){return this.getPropertyValue("shortestDistance")},set:function(t){this.setPropertyValue("shortestDistance",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"lineObjects",{get:function(){return this._lineObjects||(this._lineObjects=new Wr.e(new ao),this._lineObjects.events.on("inserted",this.handleLineObjectAdded,this,!1),this._disposers.push(new Wr.c(this._lineObjects)),this._disposers.push(this._lineObjects.template)),this._lineObjects},enumerable:!0,configurable:!0}),e.prototype.handleLineObjectAdded=function(t){var e=t.newValue;e.mapLine=this,e.shouldClone=!1,e.parent=this},Object.defineProperty(e.prototype,"arrow",{get:function(){if(!this._arrow){var t=this.createChild(ao);t.shouldClone=!1,t.width=8,t.height=10,t.mapLine=this,t.position=.5;var e=t.createChild(co.a);e.fillOpacity=1,e.width=Object(ho.c)(100),e.height=Object(ho.c)(100),e.rotation=90,e.horizontalCenter="middle",e.verticalCenter="middle",this._arrow=t}return this._arrow},set:function(t){this._arrow=t,t.mapLine=this,t.parent=this},enumerable:!0,configurable:!0}),e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.line.copyFrom(e.line),this.lineObjects.copyFrom(e.lineObjects),e._arrow&&(this.arrow=e.arrow.clone())},Object.defineProperty(e.prototype,"latitude",{get:function(){return this.north+(this.south-this.north)/2},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"longitude",{get:function(){return this.east+(this.west-this.east)/2},enumerable:!0,configurable:!0}),e.prototype.getTooltipX=function(){var t=this.getPropertyValue("tooltipX");return t instanceof ho.a||(t=Object(ho.c)(50)),t instanceof ho.a?this.positionToPoint(t.value).x:0},e.prototype.getTooltipY=function(){var t=this.getPropertyValue("tooltipY");return t instanceof ho.a||(t=Object(ho.c)(50)),t instanceof ho.a?this.positionToPoint(t.value).y:0},Object.defineProperty(e.prototype,"precision",{get:function(){return this.getPropertyValue("precision")},set:function(t){this.setPropertyValue("precision",t,!0)},enumerable:!0,configurable:!0}),e}(wr);d.c.registeredClasses.MapLine=po;var fo=function(t){function e(){var e=t.call(this)||this;return e.className="MapLineSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.getFeature=function(){if(this.multiLine&&this.multiLine.length>0)return{type:"Feature",geometry:{type:"MultiLineString",coordinates:this.multiLine}}},Object.defineProperty(e.prototype,"mapLine",{get:function(){var t=this;if(!this._mapLine){var e=this.component.mapLines.create();this._mapLine=e,this.addSprite(e),this._disposers.push(e),this._disposers.push(new h.b(function(){t.component&&t.component.mapLines.removeValue(e)})),this.mapObject=e}return this._mapLine},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"line",{get:function(){return this._line},set:function(t){this._line=t,this.multiLine=[t]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiLine",{get:function(){return this._multiLine},set:function(t){this._multiLine=t,this._multiGeoLine=Dr(t),this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"geoLine",{get:function(){return this._geoLine},set:function(t){this._geoLine=t,this.multiLine=Fr([t])},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiGeoLine",{get:function(){return this._multiGeoLine},set:function(t){this._multiGeoLine=t,this.multiLine=Fr(t)},enumerable:!0,configurable:!0}),e}(xr),go=function(t){function e(){var e=t.call(this)||this;return e.className="MapLineSeries",e.dataFields.multiLine="multiLine",e.dataFields.line="line",e.dataFields.geoLine="geoLine",e.dataFields.multiGeoLine="multiGeoLine",e.ignoreBounds=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new fo},e.prototype.validateData=function(){if(this.useGeodata||this.geodata){var e=this.chart.geodata;if(e){var n=void 0;if("FeatureCollection"==e.type?n=e.features:"Feature"==e.type?n=[e]:-1!=["Point","LineString","Polygon","MultiPoint","MultiLineString","MultiPolygon"].indexOf(e.type)?n=[{geometry:e}]:console.log("nothing found in geoJSON"),n)for(var i=function(t,e){var i=n[t],o=i.geometry;if(o){var a=o.type,u=i.id;if("LineString"==a||"MultiLineString"==a){if(!r.checkInclude(r.include,r.exclude,u))return"continue";var s=o.coordinates,c=Tr.find(r.data,function(t,e){return t.id==u});"LineString"==a&&(s=[s]),c?c.multiLine||(c.multiLine=s):(c={multiLine:s,id:u,madeFromGeoData:!0},r.data.push(c)),Zr.softCopyProperties(i.properties,c)}}},r=this,o=0,a=n.length;o<a;o++)i(o)}}t.prototype.validateData.call(this)},Object.defineProperty(e.prototype,"mapLines",{get:function(){if(!this._mapLines){var t=this.createLine(),e=new Wr.e(t);this._disposers.push(new Wr.c(e)),this._disposers.push(e.template),e.events.on("inserted",this.handleObjectAdded,this,!1),this._mapLines=e,this._mapObjects=e}return this._mapLines},enumerable:!0,configurable:!0}),e.prototype.createLine=function(){return new po},e.prototype.validate=function(){this.dataItems.each(function(t){Zr.used(t.mapLine)}),t.prototype.validate.call(this),this.mapLines.each(function(t){t.validate()})},e.prototype.copyFrom=function(e){this.mapLines.template.copyFrom(e.mapLines.template),t.prototype.copyFrom.call(this,e)},e.prototype.getFeatures=function(){var t=this,e=[];return this.dataItems.each(function(t){var n=t.getFeature();n&&e.push(n)}),this.mapLines.each(function(n){if(-1==t.dataItems.indexOf(n._dataItem)){var i=n.getFeature();i&&e.push(i)}}),e},e.prototype.getLineById=function(t){return qr.find(this.mapLines.iterator(),function(e){return e.dataItem.dataContext.id==t})},e}(jr);d.c.registeredClasses.MapLineSeries=go,d.c.registeredClasses.MapLineSeriesDataItem=fo;var vo=function(t){function e(){var e=t.call(this)||this;return e.className="Graticule",e.applyTheme(),e.shortestDistance=!0,e}return Object(c.c)(e,t),e}(po);d.c.registeredClasses.Graticule=vo;var mo=function(t){function e(){var e=t.call(this)||this;return e.className="GraticuleSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e}(fo),yo=function(t){function e(){var e=t.call(this)||this;return e.className="GraticuleSeries",e.longitudeStep=10,e.latitudeStep=10,e.north=90,e.south=-90,e.east=-180,e.west=180,e.fitExtent=!0,e.singleSprite=!0,e.events.disableType("geoBoundsChanged"),e.mapLines.template.line.strokeOpacity=.08,e.ignoreBounds=!1,e.hiddenInLegend=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new mo},e.prototype.validateData=function(){var e=this;t.prototype.validateData.call(this),this.mapLines.clear();var n=yn();if(n){n.stepMinor([this.longitudeStep,this.latitudeStep]),n.stepMajor([360,360]);var i=this.chart;if(this.fitExtent?n.extent([[i.east,i.north],[i.west,i.south]]):n.extent([[this.east,this.north],[this.west,this.south]]),this.singleSprite){this.mapLines.create().multiLine=n().coordinates}else{var r=n.lines();Tr.each(r,function(t){e.mapLines.create().multiLine=[t.coordinates]})}}},e.prototype.createLine=function(){return new vo},Object.defineProperty(e.prototype,"latitudeStep",{get:function(){return this.getPropertyValue("latitudeStep")},set:function(t){this.setPropertyValue("latitudeStep",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"longitudeStep",{get:function(){return this.getPropertyValue("longitudeStep")},set:function(t){this.setPropertyValue("longitudeStep",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"fitExtent",{get:function(){return this.getPropertyValue("fitExtent")},set:function(t){this.setPropertyValue("fitExtent",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"singleSprite",{get:function(){return this.getPropertyValue("singleSprite")},set:function(t){this.setPropertyValue("singleSprite",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),e}(go);d.c.registeredClasses.GraticuleSeries=yo,d.c.registeredClasses.GraticuleSeriesDataItem=mo;var bo=n("zhwk"),Po=function(t){function e(){var e=t.call(this)||this;return e.className="MapChartDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e}(l.b),So=function(t){function e(){var e=t.call(this)||this;e.scaleRatio=1,e.zoomDuration=1e3,e.zoomEasing=eo.cubicOut,e.minZoomLevel=1,e.maxZoomLevel=32,e._prevZoomGeoPoint={latitude:0,longitude:0},e.className="MapChart",e.projection=new Ur,e.setPropertyValue("deltaLatitude",0),e.setPropertyValue("deltaLongitude",0),e.setPropertyValue("deltaGamma",0),e.maxPanOut=.7,e.homeZoomLevel=1,e.zoomStep=2,e.layout="absolute",e.centerMapOnZoomOut=!0,e.padding(0,0,0,0),Zr.used(e.backgroundSeries),e.minWidth=10,e.minHeight=10,e.events.once("inited",e.handleAllInited,e,!1);var n=e.seriesContainer;n.visible=!1,n.inert=!0,n.resizable=!0,n.events.on("transformed",e.handleMapTransform,e,!1),n.events.on("doublehit",e.handleDoubleHit,e,!1),n.events.on("dragged",e.handleDrag,e,!1),n.zIndex=0,n.dragWhileResize=!0,e.events.on("maxsizechanged",function(t){0!=t.previousWidth&&0!=t.previousHeight||(e.updateExtremes(),e.updateCenterGeoPoint())},void 0,!1);var i=e.chartContainer;i.parent=e,i.zIndex=-1,e._disposers.push(e.events.on("maxsizechanged",function(){if(e.inited){e._mapAnimation&&e._mapAnimation.stop();var t=!0;e.series.each(function(e){e.updateTooltipBounds(),e.inited&&!e.dataInvalid||(t=!1)}),t&&e.updateScaleRatio(),e.zoomToGeoPoint(e._zoomGeoPointReal,e.zoomLevel,!0,0)}},void 0,!1));var r=i.background;r.fillOpacity=0,r.events.on("down",function(t){e.seriesContainer.dragStart(t.target.interactions.downPointers.getIndex(0))},e),r.events.on("up",function(t){e.seriesContainer.dragStop()},e),r.events.on("doublehit",e.handleDoubleHit,e),r.focusable=!0,i.events.on("down",e.handleMapDown,e,!1),e.background.fillOpacity=0,e._disposers.push(Object(bo.b)().body.events.on("keyup",function(t){if(e.topParent.hasFocused&&(!e._zoomControl||!e._zoomControl.thumb.isFocused))switch(to.b.getEventKey(t.event)){case"up":e.pan({x:0,y:.1});break;case"down":e.pan({x:0,y:-.1});break;case"left":e.pan({x:.1,y:0});break;case"right":e.pan({x:-.1,y:0})}},e)),e.mouseWheelBehavior="zoom";var o=Object(bo.b)();e._disposers.push(o.body.events.on("down",e.handlePanDown,e)),e._disposers.push(o.body.events.on("up",e.handlePanUp,e));var a=e.seriesContainer.createChild(Jr.a);return a.radius=10,a.inert=!0,a.isMeasured=!1,a.events.on("transformed",e.handlePanMove,e,!1),a.interactionsEnabled=!1,a.opacity=0,a.x=0,a.y=0,e.panSprite=a,e.panBehavior="move",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.handlePanDown=function(t){var e=Zr.documentPointToSvg(t.pointer.point,this.htmlContainer);e.x>0&&e.y>0&&e.x<this.svgContainer.width&&e.y<this.svgContainer.height&&(this._downPointOrig=Zr.documentPointToSprite(t.pointer.point,this.seriesContainer),this.panSprite.moveTo(this._downPointOrig),this.panSprite.dragStart(t.pointer),this._downDeltaLongitude=this.deltaLongitude,this._downDeltaLatitude=this.deltaLatitude)},e.prototype.handlePanUp=function(t){this._downPointOrig&&this.panSprite.dragStop(t.pointer,!0),this._downPointOrig=void 0},e.prototype.handlePanMove=function(){if(!this.seriesContainer.isResized){if(Object(bo.b)().areTransformed([this.panSprite.interactions,this.seriesContainer.interactions]))return;var t=this.projection.d3Projection,e=this.panBehavior;if("move"!=e&&"none"!=e&&this._downPointOrig&&t.rotate){var n=t.rotate(),i=n[0],r=n[1],o=n[2];t.rotate([0,0,0]);var a=this.projection.invert(this._downPointOrig),u={x:this.panSprite.pixelX,y:this.panSprite.pixelY},s=void 0;u&&(s=this.projection.invert(u)),t.rotate([i,r,o]),s&&("rotateLat"!=e&&"rotateLongLat"!=e||(this.deltaLatitude=this._downDeltaLatitude+s.latitude-a.latitude),"rotateLong"!=e&&"rotateLongLat"!=e||(this.deltaLongitude=this._downDeltaLongitude+s.longitude-a.longitude))}}},e.prototype.handleAllInited=function(){var t=this,e=!0;if(this.seriesContainer.visible=!0,this.series.each(function(t){t.inited&&!t.dataInvalid||(e=!1)}),e)this.updateCenterGeoPoint(),this.updateScaleRatio(),this.goHome(0);else{var n=d.c.events.once("exitframe",function(){t.removeDispose(n),t.handleAllInited()},this,!1);this.addDisposer(n)}},e.prototype.updateZoomGeoPoint=function(){var t=Zr.svgPointToSprite({x:this.innerWidth/2+this.pixelPaddingLeft,y:this.innerHeight/2+this.pixelPaddingTop},this.series.getIndex(0)),e=this.projection.invert(t);this._zoomGeoPointReal=e},e.prototype.updateCenterGeoPoint=function(){var t,e,n,i;if(this.backgroundSeries){var r=this.backgroundSeries.getFeatures();if(r.length>0){var o=this.projection.d3Path.bounds(r[0].geometry);t=o[0][0],n=o[0][1],e=o[1][0],i=o[1][1]}}else this.series.each(function(r){var o=r.group.node.getBBox();(t>o.x||!g.isNumber(t))&&(t=o.x),(e<o.x+o.width||!g.isNumber(e))&&(e=o.x+o.width),(n>o.y||!g.isNumber(n))&&(n=o.y),(i<o.y+o.height||!g.isNumber(i))&&(i=o.y+o.height)});this.seriesMaxLeft=t,this.seriesMaxRight=e,this.seriesMaxTop=n,this.seriesMaxBottom=i,this.seriesWidth=e-t,this.seriesHeight=i-n,this.seriesWidth>0&&this.seriesHeight>0?(this.chartContainer.visible=!0,this._centerGeoPoint=this.projection.invert({x:t+(e-t)/2,y:n+(i-n)/2}),this._zoomGeoPointReal&&g.isNumber(this._zoomGeoPointReal.latitude)||(this._zoomGeoPointReal=this._centerGeoPoint)):this.chartContainer.visible=!1},e.prototype.handleDrag=function(){var t=this.zoomLevel*this.scaleRatio,e=this.seriesWidth*t,n=this.seriesHeight*t,i=this.seriesContainer,r=this.seriesMaxLeft*t,o=this.seriesMaxRight*t,a=this.seriesMaxTop*t,u=this.seriesMaxBottom*t,s=i.pixelX,c=i.pixelY,l=this.maxPanOut,h=Math.min(this.maxWidth*(1-l)-e-r,-r);s<h&&(s=h);var p=Math.max(this.maxWidth*l-r,this.maxWidth-o);s>p&&(s=p);var f=Math.min(this.maxHeight*(1-l)-n-a,-a);c<f&&(c=f);var d=Math.max(this.maxHeight*l-a,this.maxHeight-u);c>d&&(c=d),i.moveTo({x:s,y:c},void 0,void 0,!0),this._zoomGeoPointReal=this.zoomGeoPoint},e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),g.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Map")),g.hasValue(this.background.readerTitle)||(this.background.readerTitle=this.language.translate("Use plus and minus keys on your keyboard to zoom in and out"))},e.prototype.handleMapDown=function(){this._mapAnimation&&this._mapAnimation.stop()},e.prototype.handleDoubleHit=function(t){var e=Zr.documentPointToSvg(t.point,this.htmlContainer,this.svgContainer.cssScale),n=this.svgPointToGeo(e);this.zoomIn(n)},e.prototype.handleWheel=function(t){var e=this.seriesContainer.interactions.inertias.getKey("move");e&&e.done();var n=Zr.documentPointToSvg(t.point,this.htmlContainer,this.svgContainer.cssScale),i=this.svgPointToGeo(n);t.shift.y<0?this.zoomIn(i,void 0,this.interactions.mouseOptions.sensitivity):this.zoomOut(i,void 0,this.interactions.mouseOptions.sensitivity)},Object.defineProperty(e.prototype,"mouseWheelBehavior",{get:function(){return this.getPropertyValue("mouseWheelBehavior")},set:function(t){this.setPropertyValue("mouseWheelBehavior",t)&&("none"!=t?(this._mouseWheelDisposer=this.chartContainer.events.on("wheel",this.handleWheel,this,!1),this._disposers.push(this._mouseWheelDisposer)):(this._mouseWheelDisposer&&this._mouseWheelDisposer.dispose(),this.chartContainer.wheelable=!1))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"panBehavior",{get:function(){return this.getPropertyValue("panBehavior")},set:function(t){if(this.setPropertyValue("panBehavior",t)){var e=this.seriesContainer;switch(this.panSprite.draggable=!1,e.draggable=!1,t){case"move":e.draggable=!0;break;default:this.panSprite.draggable=!0}}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"centerMapOnZoomOut",{get:function(){return this.getPropertyValue("centerMapOnZoomOut")},set:function(t){this.setPropertyValue("centerMapOnZoomOut",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"projection",{get:function(){return this.getPropertyValue("projection")},set:function(t){var e=this;this.setPropertyValue("projection",t)&&(this.invalidateProjection(),t.chart=this,this._backgroundSeries&&this._backgroundSeries.invalidate(),this.inited&&this.updateExtremes(),this.series.each(function(t){t.events.once("validated",function(){e.updateCenterGeoPoint(),e.updateScaleRatio(),e.goHome(0)})}))},enumerable:!0,configurable:!0}),e.prototype.validateDataItems=function(){t.prototype.validateDataItems.call(this),this.updateExtremes()},e.prototype.updateExtremes=function(){var t,e,n,i;this.series.each(function(r){r.ignoreBounds||r instanceof yo&&r.fitExtent||((r.north>e||!g.isNumber(e))&&(e=r.north),(r.south<i||!g.isNumber(i))&&(i=r.south),(r.west<n||!g.isNumber(n))&&(n=r.west),(r.east>t||!g.isNumber(t))&&(t=r.east))});var r=[],o=!1;this.series.each(function(t){t instanceof yo&&!t.fitExtent&&(r=t.getFeatures(),o=!0)}),o||this.series.each(function(t){t.ignoreBounds||t instanceof yo&&t.fitExtent||(r=r.concat(t.getFeatures()))});var a=v.max(50,this.innerWidth),u=v.max(50,this.innerHeight),s=this.projection.d3Projection;if(r.length>0&&s&&(this.east!=t||this.west!=n||this.north!=e||this.south!=i)){if(this.east=t,this.west=n,this.north=e,this.south=i,s.rotate){var c=s.rotate(),l=c[0],h=c[1],p=c[2];this.deltaLongitude=l,this.deltaLatitude=h,this.deltaGamma=p}var f={type:"FeatureCollection",features:r},d=s.scale();if(s.fitSize([a,u],f),s.scale()!=d&&this.invalidateDataUsers(),this.series.each(function(t){t instanceof yo&&t.invalidateData()}),this._backgroundSeries){var m=this._backgroundSeries.mapPolygons.getIndex(0);m&&(m.multiPolygon=Br(this.north,this.east,this.south,this.west))}this._fitWidth=a,this._fitHeight=u}this._zoomGeoPointReal&&g.isNumber(this._zoomGeoPointReal.latitude)||this.goHome(0)},e.prototype.updateScaleRatio=function(){var t;this.updateCenterGeoPoint();var e=this.innerWidth/this.seriesWidth,n=this.innerHeight/this.seriesHeight;t=v.min(e,n),(g.isNaN(t)||t==1/0)&&(t=1),t!=this.scaleRatio&&(this.scaleRatio=t,qr.each(this.series.iterator(),function(e){e.scale=t,e.updateTooltipBounds()}),this.backgroundSeries.scale=t,this.dispatch("scaleratiochanged"))},e.prototype.svgPointToGeo=function(t){var e=this.series.getIndex(0);if(e){var n=Zr.svgPointToSprite(t,e);return this.seriesPointToGeo(n)}},e.prototype.geoPointToSVG=function(t){var e=this.series.getIndex(0);if(e){var n=this.geoPointToSeries(t);return Zr.spritePointToSvg(n,e)}},e.prototype.seriesPointToGeo=function(t){return this.projection.invert(t)},e.prototype.geoPointToSeries=function(t){return this.projection.convert(t)},Object.defineProperty(e.prototype,"geodata",{get:function(){return this._geodata},set:function(t){t!=this._geodata&&(this._geodata=t,this.invalidateData(),this.dataUsers.each(function(t){for(var e=t.data.length-1;e>=0;e--)1==t.data[e].madeFromGeoData&&t.data.splice(e,1);t.disposeData(),t.invalidateData()}))},enumerable:!0,configurable:!0}),e.prototype.zoomToGeoPoint=function(t,e,n,i,r){var o=this;if(t||(t=this.zoomGeoPoint),t&&g.isNumber(t.longitude)&&g.isNumber(t.latitude)){this._zoomGeoPointReal=t,e=v.fitToRange(e,this.minZoomLevel,this.maxZoomLevel);var a=this.projection.convert(t);if(a){var u=this.geoPointToSVG(t),s=Zr.svgPointToSprite(u,this);n&&(s={x:this.innerWidth/2,y:this.innerHeight/2}),g.isNumber(i)||(i=this.zoomDuration);var c=s.x-a.x*e*this.scaleRatio,l=s.y-a.y*e*this.scaleRatio;return!r&&e<this.zoomLevel&&this.centerMapOnZoomOut&&e<1.5&&(c=this.innerWidth/2-(this.seriesMaxLeft+(this.seriesMaxRight-this.seriesMaxLeft)/2)*e*this.scaleRatio,l=this.innerHeight/2-(this.seriesMaxTop+(this.seriesMaxBottom-this.seriesMaxTop)/2)*e*this.scaleRatio),this._mapAnimation=this.seriesContainer.animate([{property:"scale",to:e},{property:"x",from:this.seriesContainer.pixelX,to:c},{property:"y",from:this.seriesContainer.pixelY,to:l}],i,this.zoomEasing),this._disposers.push(this._mapAnimation.events.on("animationended",function(){o._zoomGeoPointReal=o.zoomGeoPoint})),this.seriesContainer.validatePosition(),this._mapAnimation}}},e.prototype.zoomToMapObject=function(t,e,n,i){void 0==n&&(n=!0);var r=this.seriesContainer.interactions.inertias.getKey("move");if(r&&r.done(),t instanceof Lr)return g.isNaN(e)&&(e=5),this.zoomToGeoPoint({latitude:t.latitude,longitude:t.longitude},e,n,i,!0);var o=t.dataItem;if(o&&g.isNumber(o.zoomLevel)&&(e=o.zoomLevel),t instanceof Hr){var a=t.dataItem,u=t.polygon.bbox;0!=u.width&&0!=u.height||(u=t.polygon.group.getBBox()),g.isNumber(e)||(e=Math.min(this.seriesWidth/u.width,this.seriesHeight/u.height));var s=void 0;if(a&&g.hasValue(a.zoomGeoPoint))s=a.zoomGeoPoint;else{var c={x:u.x+u.width/2,y:u.y+u.height/2},l=Zr.spritePointToSprite(c,t.polygon,t.series);s=this.seriesPointToGeo(l)}return this.zoomToGeoPoint(s,e,!0,i,!0)}},e.prototype.zoomToRectangle=function(t,e,n,i,r,o,a){g.isNaN(r)&&(r=1);var u=r*Math.min((this.south-this.north)/(n-t),(this.west-this.east)/(i-e));return this.zoomToGeoPoint({latitude:t+(n-t)/2,longitude:i+(e-i)/2},u,o,a,!0)},e.prototype.zoomIn=function(t,e,n){void 0===n&&(n=1);var i=1+(this.zoomStep-1)*n;return i<1&&(i=1),this.zoomToGeoPoint(t,this.zoomLevel*i,!1,e)},e.prototype.zoomOut=function(t,e,n){void 0===n&&(n=1);var i=1+(this.zoomStep-1)*n;return i<1&&(i=1),this.zoomToGeoPoint(t,this.zoomLevel/i,!1,e)},e.prototype.pan=function(t,e){var n=this.geoPointToSVG(this.zoomGeoPoint);n.x+=this.pixelWidth*t.x,n.y+=this.pixelHeight*t.y,this.zoomToGeoPoint(this.svgPointToGeo(n),this.zoomLevel,!0,e,!0)},Object.defineProperty(e.prototype,"zoomGeoPoint",{get:function(){var t=Zr.spritePointToSvg({x:this.pixelWidth/2,y:this.pixelHeight/2},this);return this.svgPointToGeo(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomLevel",{get:function(){return this.seriesContainer.scale},set:function(t){this.seriesContainer.scale=t},enumerable:!0,configurable:!0}),e.prototype.handleMapTransform=function(){this.zoomLevel!=this._prevZoomLevel&&(this.dispatch("zoomlevelchanged"),this._prevZoomLevel=this.zoomLevel,this.svgContainer.readerAlert(this.language.translate("Zoom level changed to %1",this.language.locale,g.castString(this.zoomLevel)))),!this.zoomGeoPoint||this._prevZoomGeoPoint.latitude==this.zoomGeoPoint.latitude&&this._prevZoomGeoPoint.longitude==this.zoomGeoPoint.longitude||this.dispatch("mappositionchanged")},Object.defineProperty(e.prototype,"smallMap",{get:function(){if(!this._smallMap){var t=new $r;this.smallMap=t}return this._smallMap},set:function(t){this._smallMap&&this.removeDispose(this._smallMap),this._smallMap=t,this._smallMap.chart=this,t.parent=this.chartContainer},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomControl",{get:function(){return this._zoomControl},set:function(t){this._zoomControl&&this.removeDispose(this._zoomControl),this._zoomControl=t,t.chart=this,t.parent=this.chartContainer,t.plusButton.exportable=!1,t.minusButton.exportable=!1},enumerable:!0,configurable:!0}),e.prototype.createSeries=function(){return new jr},Object.defineProperty(e.prototype,"deltaLongitude",{get:function(){return this.getPropertyValue("deltaLongitude")},set:function(t){t=v.round(t,3),this.setPropertyValue("deltaLongitude",ro(t))&&(this.rotateMap(),this.updateZoomGeoPoint())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"deltaLatitude",{get:function(){return this.getPropertyValue("deltaLatitude")},set:function(t){t=v.round(t,3),this.setPropertyValue("deltaLatitude",t)&&(this.rotateMap(),this.updateZoomGeoPoint())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"deltaGamma",{get:function(){return this.getPropertyValue("deltaGamma")},set:function(t){t=v.round(t,3),this.setPropertyValue("deltaGamma",t)&&(this.rotateMap(),this.updateZoomGeoPoint())},enumerable:!0,configurable:!0}),e.prototype.rotateMap=function(){this.projection.d3Projection&&this.projection.d3Projection.rotate&&(this.projection.d3Projection.rotate([this.deltaLongitude,this.deltaLatitude,this.deltaGamma]),this.invalidateProjection())},Object.defineProperty(e.prototype,"maxPanOut",{get:function(){return this.getPropertyValue("maxPanOut")},set:function(t){this.setPropertyValue("maxPanOut",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"homeGeoPoint",{get:function(){return this.getPropertyValue("homeGeoPoint")},set:function(t){this.setPropertyValue("homeGeoPoint",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"homeZoomLevel",{get:function(){return this.getPropertyValue("homeZoomLevel")},set:function(t){this.setPropertyValue("homeZoomLevel",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomStep",{get:function(){return this.getPropertyValue("zoomStep")},set:function(t){this.setPropertyValue("zoomStep",t)},enumerable:!0,configurable:!0}),e.prototype.invalidateProjection=function(){this.east=void 0,this.invalidateDataUsers(),this.updateCenterGeoPoint()},Object.defineProperty(e.prototype,"geodataSource",{get:function(){return this._dataSources.geodata||this.getDataSource("geodata"),this._dataSources.geodata},set:function(t){var e=this;this._dataSources.geodata&&this.removeDispose(this._dataSources.geodata),this._dataSources.geodata=t,this._dataSources.geodata.component=this,this.events.on("inited",function(){e.loadData("geodata")},this,!1),this.setDataSourceEvents(t,"geodata")},enumerable:!0,configurable:!0}),e.prototype.processConfig=function(e){if(g.hasValue(e.geodata)&&g.isString(e.geodata)){var n=e.geodata;if(g.hasValue(window["am4geodata_"+e.geodata]))e.geodata=window["am4geodata_"+e.geodata];else try{e.geodata=JSON.parse(e.geodata)}catch(t){this.raiseCriticalError(Error("MapChart error: Geodata `"+n+"` is not loaded or is incorrect."),!0)}}g.hasValue(e.projection)&&g.isString(e.projection)&&(e.projection=this.createClassInstance(e.projection)),g.hasValue(e.smallMap)&&!g.hasValue(e.smallMap.type)&&(e.smallMap.type="SmallMap"),g.hasValue(e.zoomControl)&&!g.hasValue(e.zoomControl.type)&&(e.zoomControl.type="ZoomControl"),t.prototype.processConfig.call(this,e)},e.prototype.handleSeriesAdded=function(e){t.prototype.handleSeriesAdded.call(this,e);var n=e.newValue;n.scale=this.scaleRatio,n.events.on("validated",this.updateCenterGeoPoint,this,!1)},e.prototype.configOrder=function(e,n){return e==n?0:"smallMap"==e?1:"smallMap"==n?-1:"series"==e?1:"series"==n?-1:t.prototype.configOrder.call(this,e,n)},e.prototype.asIs=function(e){return"projection"==e||"geodata"==e||t.prototype.asIs.call(this,e)},Object.defineProperty(e.prototype,"centerGeoPoint",{get:function(){return this._centerGeoPoint},enumerable:!0,configurable:!0}),e.prototype.goHome=function(t){var e=this.homeGeoPoint;e||(e=this.centerGeoPoint),e&&this.zoomToGeoPoint(e,this.homeZoomLevel,!0,t,!0)},e.prototype.setPaper=function(e){return this.svgContainer&&(this.svgContainer.hideOverflow=!0),t.prototype.setPaper.call(this,e)},Object.defineProperty(e.prototype,"backgroundSeries",{get:function(){var t=this;if(!this._backgroundSeries){var e=new Yr;e.parent=this.seriesContainer,e.chart=this,e.hiddenInLegend=!0,e.addDisposer(new h.b(function(){t._backgroundSeries=void 0})),this._disposers.push(e);var n=(new p.a).getFor("background"),i=e.mapPolygons.template.polygon;i.stroke=n,i.fill=n,i.fillOpacity=0,i.strokeOpacity=0,e.mapPolygons.create(),this._backgroundSeries=e}return this._backgroundSeries},enumerable:!0,configurable:!0}),e.prototype.setLegend=function(e){t.prototype.setLegend.call(this,e),e.parent=this},e.prototype.setTapToActivate=function(e){t.prototype.setTapToActivate.call(this,e),this.seriesContainer.interactions.isTouchProtected=!0,this.panSprite.interactions.isTouchProtected=!0},e.prototype.handleTapToActivate=function(){t.prototype.handleTapToActivate.call(this),this.seriesContainer.interactions.isTouchProtected=!1,this.panSprite.interactions.isTouchProtected=!1},e.prototype.handleTapToActivateDeactivation=function(){t.prototype.handleTapToActivateDeactivation.call(this),this.seriesContainer.interactions.isTouchProtected=!0,this.panSprite.interactions.isTouchProtected=!0},e.prototype.asFunction=function(e){return"zoomEasing"==e||t.prototype.asIs.call(this,e)},e}(l.a);d.c.registeredClasses.MapChart=So;var _o=n("xgTw"),Mo=function(t){function e(){var e=t.call(this)||this;return e.className="MapSpline",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createLine=function(){this.line=new _o.a,this.line.tensionX=.8,this.line.tensionY=.8},Object.defineProperty(e.prototype,"shortestDistance",{get:function(){return!1},set:function(t){},enumerable:!0,configurable:!0}),e}(po);d.c.registeredClasses.MapSpline=Mo;var xo=n("MXvJ"),jo=function(t){function e(){var e=t.call(this)||this;return e.className="MapArc",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createLine=function(){this.line=new xo.a},Object.defineProperty(e.prototype,"shortestDistance",{get:function(){return!1},set:function(t){},enumerable:!0,configurable:!0}),e}(po);d.c.registeredClasses.MapArc=jo;var Oo=function(t){function e(){var e=t.call(this)||this;return e.className="MapSplineSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e}(fo),wo=function(t){function e(){var e=t.call(this)||this;return e.className="MapSplineSeries",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new Oo},e.prototype.createLine=function(){return new Mo},e}(go);d.c.registeredClasses.MapSplineSeries=wo,d.c.registeredClasses.MapSplineSeriesDataItem=Oo;var Lo=function(t){function e(){var e=t.call(this)||this;return e.className="MapArcSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e}(fo),Co=function(t){function e(){var e=t.call(this)||this;return e.className="MapArcSeries",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new Lo},e.prototype.createLine=function(){return new jo},e}(go);d.c.registeredClasses.MapArcSeries=Co,d.c.registeredClasses.MapArcSeriesDataItem=Lo;var Eo=n("aGXA"),Io=n("CnhP"),To=function(t){function e(){var e=t.call(this)||this;e._chart=new h.d,e.className="ZoomControl",e.align="right",e.valign="bottom",e.layout="vertical",e.padding(5,5,5,5);var n=new p.a,i=e.createChild(Eo.a);i.shouldClone=!1,i.label.text="+",e.plusButton=i;var r=e.createChild(Or.a);r.shouldClone=!1,r.background.fill=n.getFor("alternativeBackground"),r.background.fillOpacity=.05,r.background.events.on("hit",e.handleBackgroundClick,e,!1),r.events.on("sizechanged",e.updateThumbSize,e,!1),e.slider=r;var o=r.createChild(Eo.a);o.shouldClone=!1,o.padding(0,0,0,0),o.draggable=!0,o.events.on("drag",e.handleThumbDrag,e,!1),e.thumb=o;var a=e.createChild(Eo.a);return a.shouldClone=!1,a.label.text="-",e.minusButton=a,e.thumb.role="slider",e.thumb.readerLive="polite",e.thumb.readerTitle=e.language.translate("Use arrow keys to zoom in and out"),e.minusButton.readerTitle=e.language.translate("Press ENTER to zoom in"),e.plusButton.readerTitle=e.language.translate("Press ENTER to zoom out"),e.applyTheme(),e.events.on("propertychanged",function(t){"layout"==t.property&&e.fixLayout()},void 0,!1),e._disposers.push(e._chart),e.fixLayout(),e}return Object(c.c)(e,t),e.prototype.fixLayout=function(){var t=this.plusButton,e=this.minusButton,n=this.thumb,i=this.slider;t.x=void 0,t.y=void 0,e.x=void 0,e.y=void 0,n.x=void 0,n.y=void 0,i.x=void 0,i.y=void 0,t.padding(6,10,6,10),e.padding(6,10,6,10),e.label.align="center",e.label.valign="middle",t.label.align="center",t.label.valign="middle","vertical"==this.layout?(this.width=40,this.height=void 0,e.width=Object(ho.c)(100),e.height=void 0,n.width=Object(ho.c)(100),n.height=void 0,t.width=Object(ho.c)(100),t.height=void 0,i.width=Object(ho.c)(100),e.marginTop=1,t.marginBottom=2,i.height=0,e.toFront(),t.toBack(),n.minX=0,n.maxX=0,n.minY=0):"horizontal"==this.layout&&(this.height=40,this.width=void 0,e.height=Object(ho.c)(100),e.width=void 0,t.height=Object(ho.c)(100),t.width=void 0,n.height=Object(ho.c)(100),n.width=void 0,n.minX=0,n.minY=0,n.maxY=0,i.height=Object(ho.c)(100),i.width=0,e.toBack(),t.toFront())},e.prototype.handleBackgroundClick=function(t){var e=t.target,n=t.spritePoint.y,i=this.chart,r=Math.log(i.maxZoomLevel)/Math.LN2,o=Math.log(i.minZoomLevel)/Math.LN2,a=(e.pixelHeight-n)/e.pixelHeight*(o+(r-o)),u=Math.pow(2,a);i.zoomToGeoPoint(i.zoomGeoPoint,u)},Object.defineProperty(e.prototype,"chart",{get:function(){return this._chart.get()},set:function(t){var e=this;this._chart.set(t,new h.c([t.events.on("maxsizechanged",this.updateThumbSize,this,!1),t.events.on("zoomlevelchanged",this.updateThumb,this,!1),this.minusButton.events.on("hit",function(){t.zoomOut(t.zoomGeoPoint)},t,!1),Object(bo.b)().body.events.on("keyup",function(n){e.topParent.hasFocused&&(to.b.isKey(n.event,"enter")?e.minusButton.isFocused?t.zoomOut():e.plusButton.isFocused&&t.zoomIn():to.b.isKey(n.event,"plus")?t.zoomIn():to.b.isKey(n.event,"minus")&&t.zoomOut())},t),this.plusButton.events.on("hit",function(){t.zoomIn(t.zoomGeoPoint)},t,!1)]))},enumerable:!0,configurable:!0}),e.prototype.updateThumbSize=function(){if(this.chart){var t=this.slider,e=this.thumb;"vertical"==this.layout?(e.minHeight=Math.min(this.slider.pixelHeight,20),e.height=t.pixelHeight/this.stepCount,e.maxY=t.pixelHeight-e.pixelHeight,e.pixelHeight<=1?e.visible=!1:e.visible=!0):(e.minWidth=Math.min(this.slider.pixelWidth,20),e.width=t.pixelWidth/this.stepCount,e.maxX=t.pixelWidth-e.pixelWidth,e.pixelWidth<=1?e.visible=!1:e.visible=!0)}},e.prototype.updateThumb=function(){var t=this.slider,e=this.chart,n=this.thumb;if(!n.isDown){var i=(Math.log(e.zoomLevel)-Math.log(this.chart.minZoomLevel))/Math.LN2;"vertical"==this.layout?n.y=t.pixelHeight-(t.pixelHeight-n.pixelHeight)*i/this.stepCount-n.pixelHeight:n.x=t.pixelWidth*i/this.stepCount}},e.prototype.handleThumbDrag=function(){var t,e=this.slider,n=this.chart,i=this.thumb;t=Math.log(this.chart.minZoomLevel)/Math.LN2+(t="vertical"==this.layout?this.stepCount*(e.pixelHeight-i.pixelY-i.pixelHeight)/(e.pixelHeight-i.pixelHeight):this.stepCount*i.pixelX/e.pixelWidth);var r=Math.pow(2,t);n.zoomToGeoPoint(void 0,r,!1,0)},Object.defineProperty(e.prototype,"stepCount",{get:function(){return Math.log(this.chart.maxZoomLevel)/Math.LN2-Math.log(this.chart.minZoomLevel)/Math.LN2},enumerable:!0,configurable:!0}),e.prototype.createBackground=function(){return new Io.a},e}(Or.a);d.c.registeredClasses.ZoomControl=To;var Go=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=Ji(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.Mercator=Go;var Do=Math.abs,No=Math.atan,zo=Math.atan2,Vo=(Math.ceil,Math.cos),Ao=Math.exp,Fo=Math.floor,Ro=Math.log,ko=Math.max,Bo=Math.min,Ho=Math.pow,Wo=(Math.round,Math.sign||function(t){return t>0?1:t<0?-1:0}),Zo=Math.sin,qo=Math.tan,Xo=1e-6,Yo=1e-12,Uo=Math.PI,Jo=Uo/2,Ko=Uo/4,Qo=Math.SQRT1_2,$o=aa(2),ta=aa(Uo),ea=2*Uo,na=180/Uo,ia=Uo/180;function ra(t){return t>1?Jo:t<-1?-Jo:Math.asin(t)}function oa(t){return t>1?0:t<-1?Uo:Math.acos(t)}function aa(t){return t>0?Math.sqrt(t):0}function ua(t){return(Ao(t)-Ao(-t))/2}function sa(t){return(Ao(t)+Ao(-t))/2}function ca(t,e){var n=Vo(e),i=function(t){return t?t/Math.sin(t):1}(oa(n*Vo(t/=2)));return[2*n*Zo(t)*i,Zo(e)*i]}ca.invert=function(t,e){if(!(t*t+4*e*e>Uo*Uo+Xo)){var n=t,i=e,r=25;do{var o,a=Zo(n),u=Zo(n/2),s=Vo(n/2),c=Zo(i),l=Vo(i),h=Zo(2*i),p=c*c,f=l*l,d=u*u,g=1-f*s*s,v=g?oa(l*s)*aa(o=1/g):o=0,m=2*v*l*u-t,y=v*c-e,b=o*(f*d+v*l*s*p),P=o*(.5*a*h-2*v*c*u),S=.25*o*(h*u-v*c*f*a),_=o*(p*s+v*d*l),M=P*S-_*b;if(!M)break;var x=(y*P-m*_)/M,j=(m*S-y*b)/M;n-=x,i-=j}while((Do(x)>Xo||Do(j)>Xo)&&--r>0);return[n,i]}};function la(t,e){var n=qo(e/2),i=aa(1-n*n),r=1+i*Vo(t/=2),o=Zo(t)*i/r,a=n/r,u=o*o,s=a*a;return[4/3*o*(3+u-3*s),4/3*a*(3+3*u-s)]}la.invert=function(t,e){if(e*=3/8,!(t*=3/8)&&Do(e)>1)return null;var n=1+t*t+e*e,i=aa((n-aa(n*n-4*e*e))/2),r=ra(i)/3,o=i?function(t){return Ro(t+aa(t*t-1))}(Do(e/i))/3:function(t){return Ro(t+aa(t*t+1))}(Do(t))/3,a=Vo(r),u=sa(o),s=u*u-a*a;return[2*Wo(t)*zo(ua(o)*a,.25-s),2*Wo(e)*zo(u*Zo(r),.25+s)]};var ha=aa(8),pa=Ro(1+$o);function fa(t,e){var n=Do(e);return n<Ko?[t,Ro(qo(Ko+e/2))]:[t*Vo(n)*(2*$o-1/Zo(n)),Wo(e)*(2*$o*(n-Ko)-Ro(qo(n/2)))]}fa.invert=function(t,e){if((i=Do(e))<pa)return[t,2*No(Ao(e))-Jo];var n,i,r=Ko,o=25;do{var a=Vo(r/2),u=qo(r/2);r-=n=(ha*(r-Ko)-Ro(u)-i)/(ha-a*a/(2*u))}while(Do(n)>Yo&&--o>0);return[t/(Vo(r)*(ha-1/Zo(r))),Wo(e)*r]};function da(t,e){return[t*Vo(e)/Vo(e/=2),2*Zo(e)]}da.invert=function(t,e){var n=2*ra(e/2);return[t*Vo(n/2)/Vo(n),n]};function ga(t,e,n){var i,r,o,a=100;n=void 0===n?0:+n,e=+e;do{(r=t(n))===(o=t(n+Xo))&&(o=r+Xo),n-=i=-1*Xo*(r-e)/(r-o)}while(a-- >0&&Do(i)>Xo);return a<0?NaN:n}function va(t,e){var n,i=t*Zo(e),r=30;do{e-=n=(e+Zo(e)-i)/(1+Vo(e))}while(Do(n)>Xo&&--r>0);return e/2}function ma(t,e,n){function i(i,r){return[t*i*Vo(r=va(n,r)),e*Zo(r)]}return i.invert=function(i,r){return r=ra(r/e),[i/(t*Vo(r)),ra((2*r+Zo(2*r))/n)]},i}var ya=ma($o/Jo,$o,Uo),ba=2.00276,Pa=1.11072;function Sa(t,e){var n=va(Uo,e);return[ba*t/(1/Vo(e)+Pa/Vo(n)),(e+$o*Zo(n))/ba]}Sa.invert=function(t,e){var n,i,r=ba*e,o=e<0?-Ko:Ko,a=25;do{i=r-$o*Zo(o),o-=n=(Zo(2*o)+2*o-Uo*Zo(i))/(2*Vo(2*o)+2+Uo*Vo(i)*$o*Vo(o))}while(Do(n)>Xo&&--a>0);return i=r-$o*Zo(o),[t*(1/Vo(i)+Pa/Vo(o))/ba,i]};function _a(t,e){return[t*Vo(e),e]}_a.invert=function(t,e){return[t/Vo(e),e]};ma(1,4/Uo,Uo);function Ma(t,e){var n=aa(1-Zo(e));return[2/ta*t*n,ta*(1-n)]}Ma.invert=function(t,e){var n=(n=e/ta-1)*n;return[n>0?t*aa(Uo/n)/2:0,ra(1-n)]};var xa=aa(3);function ja(t,e){return[xa*t*(2*Vo(2*e/3)-1)/ta,xa*ta*Zo(e/3)]}ja.invert=function(t,e){var n=3*ra(e/(xa*ta));return[ta*t/(xa*(2*Vo(2*n/3)-1)),n]};function Oa(t){var e=Vo(t);function n(t,n){return[t*e,Zo(n)/e]}return n.invert=function(t,n){return[t/e,ra(n*e)]},n}function wa(t,e){var n=aa(8/(3*Uo));return[n*t*(1-Do(e)/Uo),n*e]}wa.invert=function(t,e){var n=aa(8/(3*Uo)),i=e/n;return[t/(n*(1-Do(i)/Uo)),i]};function La(t,e){var n=aa(4-3*Zo(Do(e)));return[2/aa(6*Uo)*t*n,Wo(e)*aa(2*Uo/3)*(2-n)]}La.invert=function(t,e){var n=2-Do(e)/aa(2*Uo/3);return[t*aa(6*Uo)/(2*n),Wo(e)*ra((4-n*n)/3)]};function Ca(t,e){var n=aa(Uo*(4+Uo));return[2/n*t*(1+aa(1-4*e*e/(Uo*Uo))),4/n*e]}Ca.invert=function(t,e){var n=aa(Uo*(4+Uo))/2;return[t*n/(1+aa(1-e*e*(4+Uo)/(4*Uo))),e*n/2]};function Ea(t,e){var n=(2+Jo)*Zo(e);e/=2;for(var i=0,r=1/0;i<10&&Do(r)>Xo;i++){var o=Vo(e);e-=r=(e+Zo(e)*(o+2)-n)/(2*o*(1+o))}return[2/aa(Uo*(4+Uo))*t*(1+Vo(e)),2*aa(Uo/(4+Uo))*Zo(e)]}Ea.invert=function(t,e){var n=e*aa((4+Uo)/Uo)/2,i=ra(n),r=Vo(i);return[t/(2/aa(Uo*(4+Uo))*(1+r)),ra((i+n*(r+2))/(2+Jo))]};function Ia(t,e){return[t*(1+Vo(e))/aa(2+Uo),2*e/aa(2+Uo)]}Ia.invert=function(t,e){var n=aa(2+Uo),i=e*n/2;return[n*t/(1+Vo(i)),i]};function Ta(t,e){for(var n=(1+Jo)*Zo(e),i=0,r=1/0;i<10&&Do(r)>Xo;i++)e-=r=(e+Zo(e)-n)/(1+Vo(e));return n=aa(2+Uo),[t*(1+Vo(e))/n,2*e/n]}Ta.invert=function(t,e){var n=1+Jo,i=aa(n/2);return[2*t*i/(1+Vo(e*=i)),ra((e+Zo(e))/n)]};var Ga=function(){return zi(Ta).scale(173.044)},Da=3+2*$o;function Na(t,e){var n=Zo(t/=2),i=Vo(t),r=aa(Vo(e)),o=Vo(e/=2),a=Zo(e)/(o+$o*i*r),u=aa(2/(1+a*a)),s=aa(($o*o+(i+n)*r)/($o*o+(i-n)*r));return[Da*(u*(s-1/s)-2*Ro(s)),Da*(u*a*(s+1/s)-2*No(a))]}Na.invert=function(t,e){if(!(n=la.invert(t/1.2,1.065*e)))return null;var n,i=n[0],r=n[1],o=20;t/=Da,e/=Da;do{var a=i/2,u=r/2,s=Zo(a),c=Vo(a),l=Zo(u),h=Vo(u),p=Vo(r),f=aa(p),d=l/(h+$o*c*f),g=d*d,v=aa(2/(1+g)),m=($o*h+(c+s)*f)/($o*h+(c-s)*f),y=aa(m),b=y-1/y,P=y+1/y,S=v*b-2*Ro(y)-t,_=v*d*P-2*No(d)-e,M=l&&Qo*f*s*g/l,x=($o*c*h+f)/(2*(h+$o*c*f)*(h+$o*c*f)*f),j=-.5*d*v*v*v,O=j*M,w=j*x,L=(L=2*h+$o*f*(c-s))*L*y,C=($o*c*h*f+p)/L,E=-$o*s*l/(f*L),I=b*O-2*C/y+v*(C+C/m),T=b*w-2*E/y+v*(E+E/m),G=d*P*O-2*M/(1+g)+v*P*M+v*d*(C-C/m),D=d*P*w-2*x/(1+g)+v*P*x+v*d*(E-E/m),N=T*G-D*I;if(!N)break;var z=(_*T-S*D)/N,V=(S*G-_*I)/N;i-=z,r=ko(-Jo,Bo(Jo,r-V))}while((Do(z)>Xo||Do(V)>Xo)&&--o>0);return Do(Do(r)-Jo)<Xo?[0,r]:o&&[i,r]};var za=Vo(35*ia);function Va(t,e){var n=qo(e/2);return[t*za*aa(1-n*n),(1+za)*n]}Va.invert=function(t,e){var n=e/(1+za);return[t&&t/(za*aa(1-n*n)),2*No(n)]};function Aa(t,e){var n=e/2,i=Vo(n);return[2*t/ta*Vo(e)*i*i,ta*qo(n)]}Aa.invert=function(t,e){var n=No(e/ta),i=Vo(n),r=2*n;return[t*ta/2/(Vo(r)*i*i),r]};var Fa=function(t,e,n,i,r,o,a,u){function s(s,c){if(!c)return[t*s/Uo,0];var l=c*c,h=t+l*(e+l*(n+l*i)),p=c*(r-1+l*(o-u+l*a)),f=(h*h+p*p)/(2*p),d=s*ra(h/f)/Uo;return[f*Zo(d),c*(1+l*u)+f*(1-Vo(d))]}return arguments.length<8&&(u=0),s.invert=function(s,c){var l,h,p=Uo*s/t,f=c,d=50;do{var g=f*f,v=t+g*(e+g*(n+g*i)),m=f*(r-1+g*(o-u+g*a)),y=v*v+m*m,b=2*m,P=y/b,S=P*P,_=ra(v/P)/Uo,M=p*_,x=v*v,j=(2*e+g*(4*n+6*g*i))*f,O=r+g*(3*o+5*g*a),w=(2*(v*j+m*(O-1))*b-y*(2*(O-1)))/(b*b),L=Vo(M),C=Zo(M),E=P*L,I=P*C,T=p/Uo*(1/aa(1-x/S))*(j*P-v*w)/S,G=I-s,D=f*(1+g*u)+P-E-c,N=w*C+E*T,z=E*_,V=1+w-(w*L-I*T),A=I*_,F=N*A-V*z;if(!F)break;p-=l=(D*N-G*V)/F,f-=h=(G*A-D*z)/F}while((Do(l)>Xo||Do(h)>Xo)&&--d>0);return[p,f]},s};Fa(2.8284,-1.6988,.75432,-.18071,1.76003,-.38914,.042555),Fa(2.583819,-.835827,.170354,-.038094,1.543313,-.411435,.082742),Fa(5/6*Uo,-.62636,-.0344,0,1.3493,-.05524,0,.045);function Ra(t,e){var n=t*t,i=e*e;return[t*(1-.162388*i)*(.87-952426e-9*n*n),e*(1+i/12)]}Ra.invert=function(t,e){var n,i=t,r=e,o=50;do{var a=r*r;r-=n=(r*(1+a/12)-e)/(1+a/4)}while(Do(n)>Xo&&--o>0);o=50,t/=1-.162388*a;do{var u=(u=i*i)*u;i-=n=(i*(.87-952426e-9*u)-t)/(.87-.00476213*u)}while(Do(n)>Xo&&--o>0);return[i,r]};Fa(2.6516,-.76534,.19123,-.047094,1.36289,-.13965,.031762);function ka(t,e){var n=Wo(t),i=Wo(e),r=Vo(e),o=Vo(t)*r,a=Zo(t)*r,u=Zo(i*e);t=Do(zo(a,u)),e=ra(o),Do(t-Jo)>Xo&&(t%=Jo);var s=function(t,e){if(e===Jo)return[0,0];var n,i,r=Zo(e),o=r*r,a=o*o,u=1+a,s=1+3*a,c=1-a,l=ra(1/aa(u)),h=c+o*u*l,p=(1-r)/h,f=aa(p),d=p*u,g=aa(d),v=f*c;if(0===t)return[0,-(v+o*g)];var m,y=Vo(e),b=1/y,P=2*r*y,S=(-h*y-(-3*o+l*s)*P*(1-r))/(h*h),_=-b*P,M=-b*(o*u*S+p*s*P),x=-2*b*(c*(.5*S/f)-2*o*f*P),j=4*t/Uo;if(t>.222*Uo||e<Uo/4&&t>.175*Uo){if(n=(v+o*aa(d*(1+a)-v*v))/(1+a),t>Uo/4)return[n,n];var O=n,w=.5*n;n=.5*(w+O),i=50;do{var L=aa(d-n*n),C=n*(x+_*L)+M*ra(n/g)-j;if(!C)break;C<0?w=n:O=n,n=.5*(w+O)}while(Do(O-w)>Xo&&--i>0)}else{n=Xo,i=25;do{var E=n*n,I=aa(d-E),T=x+_*I,G=n*T+M*ra(n/g)-j,D=T+(M-_*E)/I;n-=m=I?G/D:0}while(Do(m)>Xo&&--i>0)}return[n,-v-o*aa(d-n*n)]}(t>Uo/4?Jo-t:t,e);return t>Uo/4&&(u=s[0],s[0]=-s[1],s[1]=-u),s[0]*=n,s[1]*=-i,s}ka.invert=function(t,e){Do(t)>1&&(t=2*Wo(t)-t),Do(e)>1&&(e=2*Wo(e)-e);var n=Wo(t),i=Wo(e),r=-n*t,o=-i*e,a=o/r<1,u=function(t,e){var n=0,i=1,r=.5,o=50;for(;;){var a=r*r,u=aa(r),s=ra(1/aa(1+a)),c=1-a+r*(1+a)*s,l=(1-u)/c,h=aa(l),p=l*(1+a),f=h*(1-a),d=p-t*t,g=aa(d),v=e+f+r*g;if(Do(i-n)<Yo||0==--o||0===v)break;v>0?n=r:i=r,r=.5*(n+i)}if(!o)return null;var m=ra(u),y=Vo(m),b=1/y,P=2*u*y,S=(-c*y-(-3*r+s*(1+3*a))*P*(1-u))/(c*c);return[Uo/4*(t*(-2*b*(.5*S/h*(1-a)-2*r*h*P)+-b*P*g)+-b*(r*(1+a)*S+l*(1+3*a)*P)*ra(t/aa(p))),m]}(a?o:r,a?r:o),s=u[0],c=u[1],l=Vo(c);return a&&(s=-Jo-s),[n*(zo(Zo(s)*l,-Zo(c))+Uo),i*ra(Vo(s)*l)]};function Ba(t,e){var n,i,r,o,a;if(e<Xo)return[(o=Zo(t))-(n=e*(t-o*(i=Vo(t)))/4)*i,i+n*o,1-e*o*o/2,t-n];if(e>=1-Xo)return n=(1-e)/4,r=1/(i=sa(t)),[(o=function(t){return((t=Ao(2*t))-1)/(t+1)}(t))+n*((a=i*ua(t))-t)/(i*i),r-n*o*r*(a-t),r+n*o*r*(a+t),2*No(Ao(t))-Jo+n*(a-t)/i];var u=[1,0,0,0,0,0,0,0,0],s=[aa(e),0,0,0,0,0,0,0,0],c=0;for(i=aa(1-e),a=1;Do(s[c]/u[c])>Xo&&c<8;)n=u[c++],s[c]=(n-i)/2,u[c]=(n+i)/2,i=aa(n*i),a*=2;r=a*u[c]*t;do{r=(ra(o=s[c]*Zo(i=r)/u[c])+r)/2}while(--c);return[Zo(r),o=Vo(r),o/Vo(r-i),r]}function Ha(t,e){if(!e)return t;if(1===e)return Ro(qo(t/2+Ko));for(var n=1,i=aa(1-e),r=aa(e),o=0;Do(r)>Xo;o++){if(t%Uo){var a=No(i*qo(t)/n);a<0&&(a+=Uo),t+=a+~~(t/Uo)*Uo}else t+=t;r=(n+i)/2,i=aa(n*i),r=((n=r)-i)/2}return t/(Ho(2,o)*n)}function Wa(t,e){var n=($o-1)/($o+1),i=aa(1-n*n),r=Ha(Jo,i*i),o=Ro(qo(Uo/4+Do(e)/2)),a=Ao(-1*o)/aa(n),u=function(t,e){var n=t*t,i=e+1,r=1-n-e*e;return[.5*((t>=0?Jo:-Jo)-zo(r,2*t)),-.25*Ro(r*r+4*n)+.5*Ro(i*i+n)]}(a*Vo(-1*t),a*Zo(-1*t)),s=function(t,e,n){var i=Do(t),r=ua(Do(e));if(i){var o=1/Zo(i),a=1/(qo(i)*qo(i)),u=-(a+n*(r*r*o*o)-1+n),s=(-u+aa(u*u-(n-1)*a*4))/2;return[Ha(No(1/aa(s)),n)*Wo(t),Ha(No(aa((s/a-1)/n)),1-n)*Wo(e)]}return[0,Ha(No(r),1-n)*Wo(e)]}(u[0],u[1],i*i);return[-s[1],(e>=0?1:-1)*(.5*r-s[0])]}Wa.invert=function(t,e){var n=($o-1)/($o+1),i=aa(1-n*n),r=function(t,e,n){var i,r,o;return t?(i=Ba(t,n),e?(o=(r=Ba(e,1-n))[1]*r[1]+n*i[0]*i[0]*r[0]*r[0],[[i[0]*r[2]/o,i[1]*i[2]*r[0]*r[1]/o],[i[1]*r[1]/o,-i[0]*i[2]*r[0]*r[2]/o],[i[2]*r[1]*r[2]/o,-n*i[0]*i[1]*r[0]/o]]):[[i[0],0],[i[1],0],[i[2],0]]):[[0,(r=Ba(e,1-n))[0]/r[1]],[1/r[1],0],[r[2]/r[1],0]]}(.5*Ha(Jo,i*i)-e,-t,i*i),o=function(t,e){var n=e[0]*e[0]+e[1]*e[1];return[(t[0]*e[0]+t[1]*e[1])/n,(t[1]*e[0]-t[0]*e[1])/n]}(r[0],r[1]);return[zo(o[1],o[0])/-1,2*No(Ao(-.5*Ro(n*o[0]*o[0]+n*o[1]*o[1])))-Jo]};ra(1-1/3),Oa(0);var Za=.7109889596207567,qa=.0528035274542;function Xa(t,e){return e>-Za?((t=ya(t,e))[1]+=qa,t):_a(t,e)}Xa.invert=function(t,e){return e>-Za?ya.invert(t,e-qa):_a.invert(t,e)};function Ya(t,e){return Do(e)>Za?((t=ya(t,e))[1]-=e>0?qa:-qa,t):_a(t,e)}Ya.invert=function(t,e){return Do(e)>Za?ya.invert(t,e+(e>0?qa:-qa)):_a.invert(t,e)};function Ua(t,e){return[3/ea*t*aa(Uo*Uo/3-e*e),e]}Ua.invert=function(t,e){return[ea/3*t/aa(Uo*Uo/3-e*e),e]};var Ja=Uo/$o;function Ka(t,e){return[t*(1+aa(Vo(e)))/2,e/(Vo(e/2)*Vo(t/6))]}Ka.invert=function(t,e){var n=Do(t),i=Do(e),r=Xo,o=Jo;i<Ja?o*=i/Ja:r+=6*oa(Ja/i);for(var a=0;a<25;a++){var u=Zo(o),s=aa(Vo(o)),c=Zo(o/2),l=Vo(o/2),h=Zo(r/6),p=Vo(r/6),f=.5*r*(1+s)-n,d=o/(l*p)-i,g=s?-.25*r*u/s:0,v=.5*(1+s),m=(1+.5*o*c/l)/(l*p),y=o/l*(h/6)/(p*p),b=g*y-m*v,P=(f*y-d*v)/b,S=(d*g-f*m)/b;if(o-=P,r-=S,Do(P)<Xo&&Do(S)<Xo)break}return[t<0?-r:r,e<0?-o:o]};function Qa(t,e){var n=t*t,i=e*e;return[t*(.975534+i*(-.0143059*n-.119161+-.0547009*i)),e*(1.00384+n*(.0802894+-.02855*i+199025e-9*n)+i*(.0998909+-.0491032*i))]}Qa.invert=function(t,e){var n=Wo(t)*Uo,i=e/2,r=50;do{var o=n*n,a=i*i,u=n*i,s=n*(.975534+a*(-.0143059*o-.119161+-.0547009*a))-t,c=i*(1.00384+o*(.0802894+-.02855*a+199025e-9*o)+a*(.0998909+-.0491032*a))-e,l=.975534-a*(.119161+3*o*.0143059+.0547009*a),h=-u*(.238322+.2188036*a+.0286118*o),p=u*(.1605788+7961e-7*o+-.0571*a),f=1.00384+o*(.0802894+199025e-9*o)+a*(3*(.0998909-.02855*o)-.245516*a),d=h*p-f*l,g=(c*h-s*f)/d,v=(s*p-c*l)/d;n-=g,i-=v}while((Do(g)>Xo||Do(v)>Xo)&&--r>0);return r&&[n,i]};function $a(t,e){return[Zo(t)/Vo(e),qo(e)*Vo(t)]}$a.invert=function(t,e){var n=t*t,i=e*e+1,r=n+i,o=t?Qo*aa((r-aa(r*r-4*n))/n):1/aa(i);return[ra(t*o),Wo(e)*oa(o)]};function tu(t,e){return[t,1.25*Ro(qo(Ko+.4*e))]}tu.invert=function(t,e){return[t,2.5*No(Ao(.8*e))-.625*Uo]};var eu=function(){return zi(tu).scale(108.318)};var nu=aa(6),iu=aa(7);function ru(t,e){var n=ra(7*Zo(e)/(3*nu));return[nu*t*(2*Vo(2*n/3)-1)/iu,9*Zo(n/3)/iu]}ru.invert=function(t,e){var n=3*ra(e*iu/9);return[t*iu/(nu*(2*Vo(2*n/3)-1)),ra(3*Zo(n)*nu/7)]};function ou(t,e){for(var n,i=(1+Qo)*Zo(e),r=e,o=0;o<25&&(r-=n=(Zo(r/2)+Zo(r)-i)/(.5*Vo(r/2)+Vo(r)),!(Do(n)<Xo));o++);return[t*(1+2*Vo(r)/Vo(r/2))/(3*$o),2*aa(3)*Zo(r/2)/aa(2+$o)]}ou.invert=function(t,e){var n=e*aa(2+$o)/(2*aa(3)),i=2*ra(n);return[3*$o*t/(1+2*Vo(i)/Vo(i/2)),ra((n+Zo(i))/(1+Qo))]};function au(t,e){for(var n,i=aa(6/(4+Uo)),r=(1+Uo/4)*Zo(e),o=e/2,a=0;a<25&&(o-=n=(o/2+Zo(o)-r)/(.5+Vo(o)),!(Do(n)<Xo));a++);return[i*(.5+Vo(o))*t/1.5,i*o]}au.invert=function(t,e){var n=aa(6/(4+Uo)),i=e/n;return Do(Do(i)-Jo)<Xo&&(i=i<0?-Jo:Jo),[1.5*t/(n*(.5+Vo(i))),ra((i/2+Zo(i))/(1+Uo/4))]};function uu(t,e){var n=e*e,i=n*n,r=n*i;return[t*(.84719-.13063*n+r*r*(.05494*n-.04515-.02326*i+.00331*r)),e*(1.01183+i*i*(.01926*n-.02625-.00396*i))]}uu.invert=function(t,e){var n,i,r,o,a=e,u=25;do{a-=n=(a*(1.01183+(r=(i=a*a)*i)*r*(.01926*i-.02625-.00396*r))-e)/(1.01183+r*r*(.21186*i-.23625+-.05148*r))}while(Do(n)>Yo&&--u>0);return[t/(.84719-.13063*(i=a*a)+(o=i*(r=i*i))*o*(.05494*i-.04515-.02326*r+.00331*o)),a]};function su(t,e){return[t*(1+Vo(e))/2,2*(e-qo(e/2))]}su.invert=function(t,e){for(var n=e/2,i=0,r=1/0;i<10&&Do(r)>Xo;++i){var o=Vo(e/2);e-=r=(e-qo(e/2)-n)/(1-.5/(o*o))}return[2*t/(1+Vo(e)),e]};function cu(t,e){var n=Zo(e),i=Vo(e),r=Wo(t);if(0===t||Do(e)===Jo)return[0,e];if(0===e)return[t,0];if(Do(t)===Jo)return[t*i,Jo*n];var o=Uo/(2*t)-2*t/Uo,a=2*e/Uo,u=(1-a*a)/(n-a),s=o*o,c=u*u,l=1+s/c,h=1+c/s,p=(o*n/u-o/2)/l,f=(c*n/s+u/2)/h,d=f*f-(c*n*n/s+u*n-1)/h;return[Jo*(p+aa(p*p+i*i/l)*r),Jo*(f+aa(d<0?0:d)*Wo(-e*o)*r)]}cu.invert=function(t,e){var n=(t/=Jo)*t,i=n+(e/=Jo)*e,r=Uo*Uo;return[t?(i-1+aa((1-i)*(1-i)+4*n))/(2*t)*Jo:0,ga(function(t){return i*(Uo*Zo(t)-2*t)*Uo+4*t*t*(e-Zo(t))+2*Uo*t-r*e},0)]};var lu=1.0148,hu=.23185,pu=-.14499,fu=.02406,du=lu,gu=5*hu,vu=7*pu,mu=9*fu;function yu(t,e){var n=e*e;return[t,e*(lu+n*n*(hu+n*(pu+fu*n)))]}yu.invert=function(t,e){e>1.790857183?e=1.790857183:e<-1.790857183&&(e=-1.790857183);var n,i=e;do{var r=i*i;i-=n=(i*(lu+r*r*(hu+r*(pu+fu*r)))-e)/(du+r*r*(gu+r*(vu+mu*r)))}while(Do(n)>Xo);return[t,i]};function bu(t,e){if(Do(e)<Xo)return[t,0];var n=qo(e),i=t*Zo(e);return[Zo(i)/n,e+(1-Vo(i))/n]}bu.invert=function(t,e){if(Do(e)<Xo)return[t,0];var n,i=t*t+e*e,r=.5*e,o=10;do{var a=qo(r),u=1/Vo(r),s=i-2*e*r+r*r;r-=n=(a*s+2*(r-e))/(2+s*u*u+2*(r-e)*a)}while(Do(n)>Xo&&--o>0);return a=qo(r),[(Do(e)<Do(r+1/a)?ra(t*a):Wo(t)*(oa(Do(t*a))+Jo))/Zo(r),r]};var Pu=[[0,90],[-90,0],[0,0],[90,0],[180,0],[0,-90]],Su=([[0,2,1],[0,3,2],[5,1,2],[5,2,3],[0,1,4],[0,4,3],[5,4,1],[5,3,4]].map(function(t){return t.map(function(t){return Pu[t]})}),2/aa(3));function _u(t,e){var n=Ma(t,e);return[n[0]*Su,n[1]]}_u.invert=function(t,e){return Ma.invert(t/Su,e)};var Mu=[[.9986,-.062],[1,0],[.9986,.062],[.9954,.124],[.99,.186],[.9822,.248],[.973,.31],[.96,.372],[.9427,.434],[.9216,.4958],[.8962,.5571],[.8679,.6176],[.835,.6769],[.7986,.7346],[.7597,.7903],[.7186,.8435],[.6732,.8936],[.6213,.9394],[.5722,.9761],[.5322,1]];function xu(t,e){var n,i=Bo(18,36*Do(e)/Uo),r=Fo(i),o=i-r,a=(n=Mu[r])[0],u=n[1],s=(n=Mu[++r])[0],c=n[1],l=(n=Mu[Bo(19,++r)])[0],h=n[1];return[t*(s+o*(l-a)/2+o*o*(l-2*s+a)/2),(e>0?Jo:-Jo)*(c+o*(h-u)/2+o*o*(h-2*c+u)/2)]}Mu.forEach(function(t){t[1]*=1.0144}),xu.invert=function(t,e){var n=e/Jo,i=90*n,r=Bo(18,Do(i/5)),o=ko(0,Fo(r));do{var a=Mu[o][1],u=Mu[o+1][1],s=Mu[Bo(19,o+2)][1],c=s-a,l=s-2*u+a,h=2*(Do(n)-u)/c,p=l/c,f=h*(1-p*h*(1-2*p*h));if(f>=0||1===o){i=(e>=0?5:-5)*(f+r);var d,g=50;do{f=(r=Bo(18,Do(i)/5))-(o=Fo(r)),a=Mu[o][1],u=Mu[o+1][1],s=Mu[Bo(19,o+2)][1],i-=(d=(e>=0?Jo:-Jo)*(u+f*(s-a)/2+f*f*(s-2*u+a)/2)-e)*na}while(Do(d)>Yo&&--g>0);break}}while(--o>=0);var v=Mu[o][0],m=Mu[o+1][0],y=Mu[Bo(19,o+2)][0];return[t/(m+f*(y-v)/2+f*f*(y-2*m+v)/2),i*ia]};function ju(t,e){var n=qo(e/2),i=Zo(Ko*n);return[t*(.74482-.34588*i*i),1.70711*n]}ju.invert=function(t,e){var n=e/1.70711,i=Zo(Ko*n);return[t/(.74482-.34588*i*i),2*No(n)]};function Ou(t,e){if(Do(e)<Xo)return[t,0];var n=Do(e/Jo),i=ra(n);if(Do(t)<Xo||Do(Do(e)-Jo)<Xo)return[0,Wo(e)*Uo*qo(i/2)];var r=Vo(i),o=Do(Uo/t-t/Uo)/2,a=o*o,u=r/(n+r-1),s=u*(2/n-1),c=s*s,l=c+a,h=u-c,p=a+u;return[Wo(t)*Uo*(o*h+aa(a*h*h-l*(u*u-c)))/l,Wo(e)*Uo*(s*p-o*aa((a+1)*l-p*p))/l]}Ou.invert=function(t,e){if(Do(e)<Xo)return[t,0];if(Do(t)<Xo)return[0,Jo*Zo(2*No(e/Uo))];var n=(t/=Uo)*t,i=(e/=Uo)*e,r=n+i,o=r*r,a=-Do(e)*(1+r),u=a-2*i+n,s=-2*a+1+2*i+o,c=i/s+(2*u*u*u/(s*s*s)-9*a*u/(s*s))/27,l=(a-u*u/(3*s))/s,h=2*aa(-l/3),p=oa(3*c/(l*h))/3;return[Uo*(r-1+aa(1+2*(n-i)+o))/(2*t),Wo(e)*Uo*(-h*Vo(p+Uo/3)-u/(3*s))]};function wu(t,e){if(Do(e)<Xo)return[t,0];var n=Do(e/Jo),i=ra(n);if(Do(t)<Xo||Do(Do(e)-Jo)<Xo)return[0,Wo(e)*Uo*qo(i/2)];var r=Vo(i),o=Do(Uo/t-t/Uo)/2,a=o*o,u=r*(aa(1+a)-o*r)/(1+a*n*n);return[Wo(t)*Uo*u,Wo(e)*Uo*aa(1-u*(2*o+u))]}wu.invert=function(t,e){if(!t)return[0,Jo*Zo(2*No(e/Uo))];var n=Do(t/Uo),i=(1-n*n-(e/=Uo)*e)/(2*n),r=aa(i*i+1);return[Wo(t)*Uo*(r-i),Wo(e)*Jo*Zo(2*zo(aa((1-2*i*n)*(i+r)-n),aa(r+i+n)))]};function Lu(t,e){if(Do(e)<Xo)return[t,0];var n=e/Jo,i=ra(n);if(Do(t)<Xo||Do(Do(e)-Jo)<Xo)return[0,Uo*qo(i/2)];var r=(Uo/t-t/Uo)/2,o=n/(1+Vo(i));return[Uo*(Wo(t)*aa(r*r+1-o*o)-r),Uo*o]}Lu.invert=function(t,e){if(!e)return[t,0];var n=e/Uo,i=(Uo*Uo*(1-n*n)-t*t)/(2*Uo*t);return[t?Uo*(Wo(t)*aa(i*i+1)-i):0,Jo*Zo(2*No(n))]};function Cu(t,e){if(!e)return[t,0];var n=Do(e);if(!t||n===Jo)return[0,e];var i=n/Jo,r=i*i,o=(8*i-r*(r+2)-5)/(2*r*(i-1)),a=o*o,u=i*o,s=r+a+2*u,c=i+3*o,l=t/Jo,h=l+1/l,p=Wo(Do(t)-Jo)*aa(h*h-4),f=p*p,d=(p*(s+a-1)+2*aa(s*(r+a*f-1)+(1-r)*(r*(c*c+4*a)+12*u*a+4*a*a)))/(4*s+f);return[Wo(t)*Jo*d,Wo(e)*Jo*aa(1+p*Do(d)-d*d)]}Cu.invert=function(t,e){var n;if(!t||!e)return[t,e];e/=Uo;var i=Wo(t)*t/Jo,r=(i*i-1+4*e*e)/Do(i),o=r*r,a=2*e,u=50;do{var s=a*a,c=(8*a-s*(s+2)-5)/(2*s*(a-1)),l=(3*a-s*a-10)/(2*s*a),h=c*c,p=a*c,f=a+c,d=f*f,g=a+3*c,v=-2*f*(4*p*h+(1-4*s+3*s*s)*(1+l)+h*(14*s-6-o+(8*s-8-2*o)*l)+p*(12*s-8+(10*s-10-o)*l)),m=aa(d*(s+h*o-1)+(1-s)*(s*(g*g+4*h)+h*(12*p+4*h)));a-=n=(r*(d+h-1)+2*m-i*(4*d+o))/(r*(2*c*l+2*f*(1+l))+v/m-8*f*(r*(-1+h+d)+2*m)*(1+l)/(o+4*d))}while(n>Xo&&--u>0);return[Wo(t)*(aa(r*r+4)+r)*Uo/4,Jo*a]};var Eu=4*Uo+3*aa(3),Iu=2*aa(2*Uo*aa(3)/Eu);ma(Iu*aa(3)/Uo,Iu,Eu/6);function Tu(t,e){return[t*aa(1-3*e*e/(Uo*Uo)),e]}Tu.invert=function(t,e){return[t/aa(1-3*e*e/(Uo*Uo)),e]};function Gu(t,e){var n=Vo(e),i=Vo(t)*n,r=1-i,o=Vo(t=zo(Zo(t)*n,-Zo(e))),a=Zo(t);return[a*(n=aa(1-i*i))-o*r,-o*n-a*r]}Gu.invert=function(t,e){var n=(t*t+e*e)/-2,i=aa(-n*(2+n)),r=e*n+t*i,o=t*n-e*i,a=aa(o*o+r*r);return[zo(i*r,a*(1+n)),a?-ra(i*o/a):0]};function Du(t,e){var n=ca(t,e);return[(n[0]+t/Jo)/2,(n[1]+e)/2]}Du.invert=function(t,e){var n=t,i=e,r=25;do{var o,a=Vo(i),u=Zo(i),s=Zo(2*i),c=u*u,l=a*a,h=Zo(n),p=Vo(n/2),f=Zo(n/2),d=f*f,g=1-l*p*p,v=g?oa(a*p)*aa(o=1/g):o=0,m=.5*(2*v*a*f+n/Jo)-t,y=.5*(v*u+i)-e,b=.5*o*(l*d+v*a*p*c)+.5/Jo,P=o*(h*s/4-v*u*f),S=.125*o*(s*f-v*u*l*h),_=.5*o*(c*p+v*d*a)+.5,M=P*S-_*b,x=(y*P-m*_)/M,j=(m*S-y*b)/M;n-=x,i-=j}while((Do(x)>Xo||Do(j)>Xo)&&--r>0);return[n,i]};var Nu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=eu(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.Miller=Nu;var zu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=Ga(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.Eckert6=zu;var Vu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=br(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.Orthographic=Vu;var Au=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=Sr(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.Stereographic=Au;var Fu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=ki(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.Albers=Fu;var Ru=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=Bi(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.AlbersUsa=Ru;var ku=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=mr(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.NaturalEarth1=ku;var Bu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=qi(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.AzimuthalEqualArea=Bu;var Hu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=hr(),e}return Object(c.c)(e,t),e}(Ur);d.c.registeredClasses.EqualEarth=Hu,window.am4maps=a},QaCB:function(t,e,n){"use strict";function i(t,e){if(!(this instanceof i))return new i(t,e);if(this.data=t||[],this.length=this.data.length,this.compare=e||r,this.length>0)for(var n=(this.length>>1)-1;n>=0;n--)this._down(n)}function r(t,e){return t<e?-1:t>e?1:0}t.exports=i,t.exports.default=i,i.prototype={push:function(t){this.data.push(t),this.length++,this._up(this.length-1)},pop:function(){if(0!==this.length){var t=this.data[0];return this.length--,this.length>0&&(this.data[0]=this.data[this.length],this._down(0)),this.data.pop(),t}},peek:function(){return this.data[0]},_up:function(t){for(var e=this.data,n=this.compare,i=e[t];t>0;){var r=t-1>>1,o=e[r];if(n(i,o)>=0)break;e[t]=o,t=r}e[t]=i},_down:function(t){for(var e=this.data,n=this.compare,i=this.length>>1,r=e[t];t<i;){var o=1+(t<<1),a=o+1,u=e[o];if(a<this.length&&n(e[a],u)<0&&(o=a,u=e[a]),n(u,r)>=0)break;e[t]=u,t=o}e[t]=r}}},U8r1:function(t,e,n){"use strict";var i=n("QaCB");function r(t,e,n){var r,u,s,c;e=e||1;for(var l=0;l<t[0].length;l++){var h=t[0][l];(!l||h[0]<r)&&(r=h[0]),(!l||h[1]<u)&&(u=h[1]),(!l||h[0]>s)&&(s=h[0]),(!l||h[1]>c)&&(c=h[1])}var p=s-r,f=c-u,d=Math.min(p,f),g=d/2,v=new i(null,o);if(0===d)return[r,u];for(var m=r;m<s;m+=d)for(var y=u;y<c;y+=d)v.push(new a(m+g,y+g,g,t));var b=function(t){for(var e=0,n=0,i=0,r=t[0],o=0,u=r.length,s=u-1;o<u;s=o++){var c=r[o],l=r[s],h=c[0]*l[1]-l[0]*c[1];n+=(c[0]+l[0])*h,i+=(c[1]+l[1])*h,e+=3*h}return 0===e?new a(r[0][0],r[0][1],0,t):new a(n/e,i/e,0,t)}(t),P=new a(r+p/2,u+f/2,0,t);P.d>b.d&&(b=P);for(var S=v.length;v.length;){var _=v.pop();_.d>b.d&&(b=_,n&&console.log("found best %d after %d probes",Math.round(1e4*_.d)/1e4,S)),_.max-b.d<=e||(g=_.h/2,v.push(new a(_.x-g,_.y-g,g,t)),v.push(new a(_.x+g,_.y-g,g,t)),v.push(new a(_.x-g,_.y+g,g,t)),v.push(new a(_.x+g,_.y+g,g,t)),S+=4)}return n&&(console.log("num probes: "+S),console.log("best distance: "+b.d)),[b.x,b.y]}function o(t,e){return e.max-t.max}function a(t,e,n,i){this.x=t,this.y=e,this.h=n,this.d=function(t,e,n){for(var i=!1,r=1/0,o=0;o<n.length;o++)for(var a=n[o],s=0,c=a.length,l=c-1;s<c;l=s++){var h=a[s],p=a[l];h[1]>e!=p[1]>e&&t<(p[0]-h[0])*(e-h[1])/(p[1]-h[1])+h[0]&&(i=!i),r=Math.min(r,u(t,e,h,p))}return(i?1:-1)*Math.sqrt(r)}(t,e,i),this.max=this.d+this.h*Math.SQRT2}function u(t,e,n,i){var r=n[0],o=n[1],a=i[0]-r,u=i[1]-o;if(0!==a||0!==u){var s=((t-r)*a+(e-o)*u)/(a*a+u*u);s>1?(r=i[0],o=i[1]):s>0&&(r+=a*s,o+=u*s)}return(a=t-r)*a+(u=e-o)*u}t.exports=r,t.exports.default=r}},["QJ7E"]);
//# sourceMappingURL=maps.js.map