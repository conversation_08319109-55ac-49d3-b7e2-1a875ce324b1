{"name": "simplelightbox", "version": "2.10.1", "homepage": "https://simplelightbox.com/", "authors": ["<PERSON> <<EMAIL>> (https://www.andrerinas.de)"], "description": "Touch-friendly image lightbox for mobile and desktop with jQuery", "main": ["dist/simple-lightbox.css", "dist/simple-lightbox.min.css", "dist/simple-lightbox.js", "dist/simple-lightbox.min.js"], "keywords": ["image", "gallery", "lightbox", "swipe", "touch", "j<PERSON>y", "pinch", "popup"], "repository": {"type": "git", "url": "git://github.com/andreknieriem/simplelightbox.git"}, "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "demo"]}