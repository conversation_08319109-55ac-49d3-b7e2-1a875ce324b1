{"name": "simplelightbox", "version": "2.10.1", "description": "Touch-friendly modern image lightbox for mobile and desktop with optional jQuery support", "main": "dist/simple-lightbox.js", "style": "dist/simple-lightbox.css", "module": "dist/simple-lightbox.modules.js", "repository": {"type": "git", "url": "https://github.com/andreknieriem/simplelightbox.git"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.14.5", "babelify": "^10.0.0", "browserify": "^17.0.0", "core-js": "^3.14.0", "gulp": "^4.0.2", "gulp-append-prepend": "^1.0.9", "gulp-babel": "^8.0.0", "gulp-buffer": "^0.0.2", "gulp-rename": "^2.0.0", "gulp-sass": "^5.0.0", "gulp-tap": "^2.0.0", "gulp-uglify": "^3.0.2", "node-sass": "^6.0.1", "through2": "^4.0.2"}, "keywords": ["lightbox", "modal", "gallery", "jquery-plugin", "touchfriendly", "pinch", "responsive", "popup", "dialog"], "author": {"name": "<PERSON>", "url": "https://www.andrerinas.de", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/andreknieriem/simplelightbox/issues"}, "homepage": "https://simplelightbox.com/"}