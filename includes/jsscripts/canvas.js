if (typeof CanvasRenderingContext2D !== 'undefined') {
  CanvasRenderingContext2D.prototype.dashedRect = function (fromX, fromY, width, height, pattern) {
	  this.dashedLineTo(fromX, fromY, fromX+width, fromY, pattern);
	  this.dashedLineTo(fromX, fromY, fromX, fromY+height, pattern);
	  this.dashedLineTo(fromX+width, fromY+height, fromX+width, fromY, pattern);
	  this.dashedLineTo(fromX+width, fromY+height, fromX, fromY+height, pattern);
  };

  CanvasRenderingContext2D.prototype.doubleArrowTo = function (fromx, fromy, tox, toy) {
    var headlen = 7;   // length of head in pixels
    var angle = Math.atan2(toy-fromy,tox-fromx);
    var angle2 = angle+Math.PI;

    this.moveTo(fromx, fromy);
    this.lineTo(tox, toy);
    this.lineTo(tox-headlen*Math.cos(angle-Math.PI/6),toy-headlen*Math.sin(angle-Math.PI/6));
    this.moveTo(tox, toy);
    this.lineTo(tox-headlen*Math.cos(angle+Math.PI/6),toy-headlen*Math.sin(angle+Math.PI/6));
    
    this.moveTo(fromx, fromy);
    this.lineTo(fromx-headlen*Math.cos(angle2-Math.PI/6),fromy-headlen*Math.sin(angle2-Math.PI/6));
    this.moveTo(fromx, fromy);
    this.lineTo(fromx-headlen*Math.cos(angle2+Math.PI/6),fromy-headlen*Math.sin(angle2+Math.PI/6));
  };

  CanvasRenderingContext2D.prototype.arrowTo = function (fromx, fromy, tox, toy) {
  	var headlen = 7;   // length of head in pixels
    var angle = Math.atan2(toy-fromy,tox-fromx);
    this.moveTo(fromx, fromy);
    this.lineTo(tox, toy);
    this.lineTo(tox-headlen*Math.cos(angle-Math.PI/6),toy-headlen*Math.sin(angle-Math.PI/6));
    this.moveTo(tox, toy);
    this.lineTo(tox-headlen*Math.cos(angle+Math.PI/6),toy-headlen*Math.sin(angle+Math.PI/6));
  };
  
  
  CanvasRenderingContext2D.prototype.dashedLineTo = function (fromX, fromY, toX, toY, pattern) {
  	// Our growth rate for our line can be one of the following:
    // (+,+), (+,-), (-,+), (-,-)
    // Because of this, our algorithm needs to understand if the x-coord and
    // y-coord should be getting smaller or larger and properly cap the
    // values based on (x,y).
    
    var lt = function (a, b) { return a <= b; };
    var gt = function (a, b) { return a >= b; };
    var capmin = function (a, b) { return Math.min(a, b); };
    var capmax = function (a, b) { return Math.max(a, b); };
  
    var checkX = { thereYet: gt, cap: capmin };
    var checkY = { thereYet: gt, cap: capmin };

    if (fromY - toY > 0) {
      checkY.thereYet = lt;
      checkY.cap = capmax;
    }
    if (fromX - toX > 0) {
      checkX.thereYet = lt;
      checkX.cap = capmax;
    }

    this.moveTo(fromX, fromY);
    var offsetX = fromX;
    var offsetY = fromY;
    var idx = 0, dash = true;
    var ang = Math.atan2(toY - fromY, toX - fromX);
    var cos = Math.cos(ang);
    var sin = Math.sin(ang);

    while (!(checkX.thereYet(offsetX, toX) && checkY.thereYet(offsetY, toY))) {
      var len = pattern[idx];
  
      offsetX = checkX.cap(toX, offsetX + (cos * len));
      offsetY = checkY.cap(toY, offsetY + (sin * len));

      if (dash) {
        this.lineTo(offsetX, offsetY);
      }
      else {
        this.moveTo(offsetX, offsetY);
      }
  
      idx = (idx + 1) % pattern.length;
      dash = !dash;
    }
  };
	  
	CanvasRenderingContext2D.prototype.rotateText = function (x, y, text, degs) {
	  
		// metric will receive the measures of the text
		var metric = this.measureText(text); 
		this.save(); // this will "save" the normal canvas to return to
		// We want to find the center of the text (or whatever point you want)
		// and rotate about it
	  	var tx = x + (metric.width/2);
	  	var ty = y + 5;
	  
	  	// Translate to near the center to rotate about the center
	  	this.translate(tx,ty);
	  	// 		Then rotate...
	  	this.rotate(degs * Math.PI/180);
	  	// Then translate back to draw in the right place!
	  	this.translate(-tx,-ty);
	  
	  	this.fillText(text, x, y);
	  	this.restore(); // This will un-translate and un-rotate the
		// canvas
	};
}	   



