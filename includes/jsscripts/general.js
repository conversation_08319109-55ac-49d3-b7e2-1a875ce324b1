/**
 * This file contains general javascript funtions.
 * If you change it, also create a minified version via https://www.toptal.com/developers/javascript-minifier and save to general.min.js
 */

function confirmDelete(_href, _title, _text, _type, _showCancelButton, _confirmButtonText, dummy, _html, _cancelButtonText) {
  $href = _href;
  $title = _title;
  $text = _text;
  $type = _type != "" && typeof _showCancelButton !== 'undefined' ? _type : 'warning';
  $showCancelButton = typeof _showCancelButton !== 'undefined' ? _showCancelButton : false;
  $confirmButtonText = _confirmButtonText ? _confirmButtonText : "OK";
  $cancelButtonText = _cancelButtonText ? _cancelButtonText : "Annuleren";
  $html = typeof _html !== 'undefined' ? _html : false;

  if (typeof swal !== 'undefined') {
    let vals = {
      title: $title,
      type: $type,
      showCancelButton: true,
      cancelButtonText: $cancelButtonText,
      confirmButtonColor: ($type == 'warning' ? "#DD6B55" : ($type == 'info' ? "#34A7DD" : ($type == 'success' ? "#34A7DD" : "#AEDEF4"))),
      confirmButtonText: $confirmButtonText,
      useRejections: true, //backwards compatible sweetalert2 6 en 7
      heightAuto: false, //added anders word het linkermenu afgekapt.
    };
    if ($html != false) {
      vals.html = $html;
    }
    else {
      vals.text = $text;
    }
    swal(vals).then(
      function (result) {
        // handle Confirm button click
        // result is an optional parameter, needed for modals with input
        window.location.href = $href;
      }, function (dismiss) {
        // dismiss can be 'cancel', 'overlay', 'esc' or 'timer'
        return false;
      }
    );
  }
  else {
    if (confirm($title)) {
      window.location.href = $href;
    }
  }
}

function getSwalConfirmConfig(title, html) {
  return {
    title: title,
    html: html,
    type: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Ja',
    cancelButtonText: 'Nee'
  }
}

/**
 * Creating a simple sweetalert confirm box.
 * Example:
 *  swalConfirm("Verwijderen","Wilt u dit product van de bestelling verwijderen?<br/>" + $(this).attr('data-product-desc'), function(confirmed) {
 *    if (!confirmed) return;
 *    $("#remproduct").val(productId);
 *    $("#saveOrder").trigger("click");
 *  });
 * @param title
 * @param message
 * @param callback
 */
function swalConfirm(title, message, callback) {
  swal({
    title: title,
    html: message,
    type: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Ja',
    cancelButtonText: 'Nee'
  }).then((confirmed) => {
    callback(confirmed && confirmed.value === true);
  });
}

/**
 * Create a simple sweetalert ERROR box
 * @param title
 * @param message
 */
function swalError(title, message) {
  swal({
    title: title,
    html: message,
    type: 'error',
    confirmButtonColor: '#3085d6',
    confirmButtonText: 'Sluiten'
  }).catch(swal.noop);
}

/**
 * Create a simple sweetalert INFO box
 * @param title
 * @param message
 */
function swalInfo(title, message) {
  swal({
    title: title,
    html: message,
    type: 'info',
    confirmButtonColor: '#3085d6',
    confirmButtonText: 'Sluiten'
  }).catch(swal.noop);
}

function doPrint() {
  document.body.focus();
  self.print();
}

function openwindow(url) {
  if (window.search_win) window.search_win.close();
  search_win = window.open(url, 'win_ref', 'width=900,height=600,resizable=1,scrollbars=yes');
}

//this is for reminding the user that the page has changed
var dirty = false;

function setDirty(value) {
  dirty = value;
}

function isDirty() {
  return dirty;
}

function Round(Number, DecimalPlaces) {
  var nr = parseFloat(Number);
  // we add this because of javascript can do some weird rounding because of Floating point numbers (see: http://stackoverflow.com/questions/18143083/js-floating-point-causing-incorrect-rounding)
  // for example, multiplying 3.5 * 23.99 will return 83.96499999999999, while we expect 83,965
  nr = nr + 0.0000000001;
  //avoid rounding negative values problem vs php rounding
  if (nr < 0) {
    return -1 * (Math.round(-1 * nr * Math.pow(10, DecimalPlaces)) / Math.pow(10, DecimalPlaces));
  }
  else {
    return Math.round(nr * Math.pow(10, DecimalPlaces)) / Math.pow(10, DecimalPlaces);
  }
}

function RoundFixed(Number, DecimalPlaces) {
  return Round(Number, DecimalPlaces).toFixed(DecimalPlaces);
}

function is_numeric(mixed_var) {
  return (typeof (mixed_var) === 'number' || typeof (mixed_var) === 'string') && mixed_var !== '' && !isNaN(mixed_var);
}

function decimalNL(str, decimals) {
  if (decimals == null) {
    decimals = 2;
  }
  str = String(str);
  str = str.replace(',', '.');
  str = RoundFixed(str, decimals).toString();
  if (isNaN(str)) return '';
  return str;
}

function decimalPerc(str) {
  str = String(str);
  str = str.replace(',', '.');
  str = RoundFixed(str, 1);
  if (isNaN(str)) return '';
  return str;
}

/** Format as NL price: 9.999,00 **/
function currencyFormat(num, decimals) {
  num = parseFloat(num);
  if (isNaN(decimals)) {
    decimals = 2;
  }
  return (
    num
      .toFixed(decimals) // always two decimal digits
      .replace('.', ',') // replace decimal point character with ,
      .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.')
  ) // use . as a separator
}

/** Format as nummber NL with thousand separator: 9.999 **/
function numberFormat(num, decimals) {
  if (isNaN(decimals)) {
    decimals = 0;
  }
  return currencyFormat(num, decimals)
}

function getFloatEsc(str) {
  str = String(str);
  str = str.replace(',', '.');
  return parseFloat(str);
}

function favorieten(bookmarkurl, bookmarktitel) {
  if (document.all)
    window.external.AddFavorite(bookmarkurl, bookmarktitel);
  else if (window.sidebar)
    window.sidebar.addPanel(bookmarktitel, bookmarkurl, "");
}

function zoekinkvk(kvk, postcode, huisnummer) {

  //KvK nummer
  if (kvk != "") {
    kvk = kvk.substr(0, 8);
    window.open('https://server.db.kvk.nl/TST-BIN/ZS/ZSWWW01@?TYPE=NDNR&NDNR=' + kvk + '&NSDN=%3F');
  }
  else if (postcode != "") {
    //Zoeken a.d.h.v. bezoekadres
    postcode = postcode.replace(' ', '');
    if (huisnummer == "") {
      huisnummer = 1;
    }
    window.open('https://server.db.kvk.nl/TST-BIN/ZS/ZSWWW01@?TAAL=NL++&TYPE=PCHN&AANT=0&AWCD=' + postcode + '&NHVC=' + huisnummer + '&HIST=+');
  }
  else {
    alert('Niet mogelijk');
  }
}

function zoekpostcode(straat, nr, plaats, postcode, land) {
  if (land == 2 || land == 'be') {
    window.open('http://www.bpost.be/site/nl/residential/customerservice/search/postal_codes.html');
  }
  else if (land == 3 || land == 'de') {
    window.open('http://www.postdirekt.de/plzserver/');
  }
  else {
    var searchval = "";
    if (postcode && postcode != "") {
      searchval += postcode;
    }
    else {
      searchval += straat + " " + nr + " + " + plaats;
    }
    var win = window.open('http://www.postcode.nl/zoek/' + searchval);
  }
}

/** @deprecated use lengthCounter() **/
function textCounter(field, cntfield, maxlimit) {
  if (field.value.length > maxlimit) {// if too long...trim it!
    field.value = field.value.substring(0, maxlimit);
  }
  else {
    // otherwise, update 'characters left' counter
    if (cntfield) cntfield.value = maxlimit - field.value.length;
  }
}

function textCounterJquery(selector, cntfield, maxlimit) {
  if (selector.val().length > maxlimit) {// if too long...trim it!
    selector.val(selector.val().substring(0, maxlimit));
  }
  else {
    // otherwise, update 'characters left' counter
    cntfield.val(maxlimit - selector.val().length);
  }
}

/**
 * Calculates nr of characters in input/textarea
 * @param elementname: class to use. Alle input/textarea's with this class wil be updated. The counters name is elementname-length
 */
function lengthCounter(elementname) {
  if (typeof elementname == 'undefined') {
    elementname = "lengthcounter";
  }
  $("." + elementname).each(function () {
    var element = $(this);
    element.on("keyup", function () {
      textCounterJquery(element, element.parent().find("." + elementname + "-length"), element.attr("data-lengthcounter"));
    }).on("keydown", function () {
      textCounterJquery(element, element.parent().find("." + elementname + "-length"), element.attr("data-lengthcounter"));
    });
    element.trigger("keyup");
  });
}

function trim(s) {
  return s.replace(/^\s+|\s+$/g, "");
}

function isInt(x) {
  var y = parseInt(x);
  if (isNaN(y)) return false;
  return x == y && x.toString() == y.toString();
}

function isFloat(s) {
  var n = trim(s);
  return /\./.test(n.toString());
}

function zeroFill(number, width) {
  width = width || 2;
  width -= number.toString().length;
  if (width > 0) {
    return new Array(width + (/\./.test(number) ? 2 : 1)).join('0') + number;
  }
  return number + ""; // always return a string
}

function convertSecondsToTimestring(seconds) {
  seconds = parseFloat(seconds);
  if (seconds > 0) {
    //zerofill(floor($seconds / (60 * 60))) . ":" . zerofill(floor( (($seconds / 60) % 60 )));
    return (zeroFill(Math.floor(seconds / 60 / 60), 2)) + ":" + (zeroFill(Math.floor(((seconds / 60) % 60)), 2));
  }
  return "00:00";
}

function convertTimestringToSeconds(timestring) {
  if (timestring.indexOf(":") !== false) {
    var split = timestring.split(":");
    //zerofill(floor($seconds / (60 * 60))) . ":" . zerofill(floor( (($seconds / 60) % 60 )));
    return (parseFloat(split[0]) * 60 * 60) + (parseFloat(split[1]) * 60);
  }
  return 0;
}

function convertSecondsToHours(seconds) {
  seconds = parseFloat(seconds);
  if (seconds > 0) {
    return parseFloat(zeroFill(seconds / 60 / 60, 2)).toFixed(2);
  }
  return "0.00";
}


String.prototype.replaceArray = function (find, replace) {
  var replaceString = this;
  var findIsString = (typeof find === 'string');
  var replaceIsString = (typeof replace === 'string');

  if (findIsString && replaceIsString) {
    replaceString = replaceString.replace(find, replace);
  }
  else if (!findIsString && replaceIsString) {
    for (var i = 0; i < find.length; i++) {
      replaceString = replaceString.replace(find[i], replace);
    }
  }
  else if (!findIsString && !replaceIsString && find.length > 0 && replace.length > 0 && find.length == replace.length) {
    for (var i = 0; i < find.length; i++) {
      replaceString = replaceString.replace(find[i], replace[i]);
    }
  }

  return replaceString;
};

/**
 * Use popper for tooltips. (not yet implemented in default backend)
 * @returns {boolean}
 */
function buildPopper() {
  if (typeof Popper == "undefined") return false;

  let elements = $('.qtipa');
  if (elements.length === 0) return true;

  var tooltip = $('#tooltip');

  if (tooltip.length === 0) {
    //append the tooltip html, if not available
    $("body").append(
      '  <div id="tooltip">\n' +
      '    <div id="tooltip-title">Titel</div>\n' +
      '    <div id="tooltip-content">Content</div>\n' +
      '    <div id="tooltip-arrow" data-popper-arrow></div>\n' +
      '  </div>');
    tooltip = $('#tooltip');
  }

  const tooltipTitle = $('#tooltip-title');
  const tooltipContent = $('#tooltip-content');

  elements.each(function () {
    $(this).on("mouseenter", function () {
      if (!$(this).attr('data-content')) {
        $(this).attr('data-content', $(this).attr('title'));
        $(this).attr('title', "");
      }
      tooltipContent.html($(this).attr('data-content'));
      if ($(this).attr('data-caption') && $(this).attr('data-caption').length > 0) {
        tooltipTitle.html($(this).attr('data-caption'));
        tooltipTitle.show();
      }
      else {
        tooltipTitle.hide();
      }
      if ($(this).attr('data-content').length === 0) {
        tooltipContent.hide();
      }
      else {
        tooltipContent.show();
      }
      Popper.createPopper($(this)[0], tooltip[0], {
        placement: 'top',
        modifiers: [
          {
            name: 'offset',
            options: {
              offset: [0, 10],
            }
          }
        ]
      });
      tooltip.show();
    }).on("mouseleave", function () {
      tooltip.hide();
    })
  });

  return true;
}


/**
 * @deprecated: qtip isn't upgraded sinds 2015. Use buildPopper instead.
 */
function buildQtip() {
  if ($.fn.qtip !== undefined) {
    console.warn("Qtip is deprecated. Stap over naar popper of ander tooltip.");
    $('.qtipa').each(function () {
      $(this).qtip({
        content: {
          text: $(this).attr('title'),
          title: {
            text: $(this).attr('data-caption')
          }
        },
        position: {
          viewport: $(window),
          at: 'top right',
          my: 'bottom left'
        },
        style: {
          classes: 'qtip-def'
        },
        hide: {
          fixed: true,
          delay: 300
        }
      });
    });

  }
  else {
    //@todo: used anywhere?
    $('.qtipa').each(function () {
      if ($(this).attr('data-caption')) {
        $(this).attr("data-toggle", "kt-popover");
        $(this).attr("data-content", $(this).attr('title'));
        $(this).attr("title", $(this).attr('data-caption'));
        $(this).attr("data-html", "true");
      }
      else {
        $(this).attr("data-toggle", "kt-tooltip");
        $(this).attr("data-html", "true");
      }
    });
  }
}

var isMobile = {
  Android: function () {
    return navigator.userAgent.match(/Android/i);
  },
  BlackBerry: function () {
    return navigator.userAgent.match(/BlackBerry/i);
  },
  iOS: function () {
    return navigator.userAgent.match(/iPhone|iPad|iPod/i);
  },
  Opera: function () {
    return navigator.userAgent.match(/Opera Mini/i);
  },
  Windows: function () {
    return navigator.userAgent.match(/IEMobile/i);
  },
  any: function () {
    return (isMobile.Android() || isMobile.BlackBerry() || isMobile.iOS() || isMobile.Opera() || isMobile.Windows());
  }
};

function isValidURL(str) {
  var pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
    '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
    '(\\#[-a-z\\d_]*)?$', 'i'); // fragment locator
  return pattern.test(str);
}


function responsiveyoutube(largerthenorig) {

  // Find all YouTube videos
  var $allVideos = $("iframe[src*='www.youtube.com'], iframe.frame-responsive");

  // The element that is fluid width
  // Figure out and save aspect ratio for each video
  $allVideos.each(function () {

    $(this)
      .data('aspectRatio', this.height / this.width)
      .data('aspectHeight', this.height)
      .data('aspectWidth', this.width)
      .data('largerthenorig', largerthenorig)
    // and remove the hard coded width/height
    ;

  });

  // When the window is resized
  // (You'll probably want to debounce this)
  $(window).resize(function () {

    // Resize all videos according to their own aspect ratio
    $allVideos.each(function () {

      var $el = $(this);

      var newWidth = $el.parent().parent().width();
      var largerthenorig = false;
      if ($el.data('largerthenorig') != 'undefined' && $el.data('largerthenorig') == 1) {
        largerthenorig = true;
      }

      if (!largerthenorig && newWidth > $el.data('aspectWidth')) {
        $el
          .removeAttr('height')
          .removeAttr('width')
          .width($el.data('aspectWidth'))
          .height($el.data('aspectHeight'));
      }
      else if (newWidth > 0) {  //negeer 0 formaat
        $el
          .removeAttr('height')
          .removeAttr('width')
          .width(newWidth)
          .height(newWidth * $el.data('aspectRatio'));
      }

    });

    // Kick off one resize to fix all videos on page load
  }).resize();

}

function validateEmail(email) {
  var re = /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i;
  return re.test(email);
}

function validatePhoneNL(tel) {
  var phoneno = /^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)[1-9]((\s|\s?\-\s?)?[0-9]){8}$/;
  if (tel.match(phoneno)) {
    return true;
  }
  return false;
}

function validatePhone(tel) {
  var phoneno = /^[(]{0,1}[0-9]{3}[)]{0,1}[-\s\.]{0,1}[0-9]{3}[-\s\.]{0,1}[0-9]{4}$/;
  if (tel.match(phoneno)) {
    return true;
  }
  return false;
}

function jquery_id_esc(myid) {
  return myid.replace(/(:|\.|\[|\]|,)/g, "\\$1");
}

function deg2rad(angle) {
  return (angle * Math.PI) / 180;
}

function rad2deg(angle_rad) {
  return (angle_rad * 180) / Math.PI;
}

function nl2br(str, is_xhtml) {
  var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';
  return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
}

function escapeIdJs(id) {
  return id.replace(/\[/g, "\\\\[").replace(/\]/g, "\\\\]");
}

function ibantoscreen(iban) {
  if (iban == '') return '';
  str = iban.substring(0, 4) + ' ';
  str += iban.substring(4, 4) + ' ';
  str += iban.substring(8, 4) + ' ';
  str += iban.substring(12, 4) + ' ';
  str += iban.substring(16);

  return trim(str);
}

/*********************
 Function to prevent being able to click a button multiple times, which can cause problems when it performs database actions
 *********************/
function preventDoubleclick(element) {

  if (element.length) {
    if (element.hasClass('is-clicked')) {
      // button has already been clicked, do not execute action again
      return true;
    }
    else {
      element.addClass('is-clicked');
      return false;
    }
  }

  return false;
}

function refresh_datatable_on_filter_change(datatable) {
  /** refresh table (new ajax request) based on filter changes **/
  if ($('.list-filter-form').length > 0) {

    // selects
    $('.list-filter-form select').each(function () {
      $(this).on('change', function () {
        datatable.draw();
      });
    });

    // checkboxes
    $('.list-filter-form input[type!="text"]').each(function () {
      $(this).on('change', function () {
        datatable.draw();
      });
    });

    // inputs
    //setup before functions
    var typingTimer;                //timer identifier
    var doneTypingInterval = 500;  //time in ms
    if ($('.list-filter-form input[type="text"]').length > 0) {
      $('.list-filter-form input[type="text"]').each(function () {
        $(this).on('input propertychange paste', function () {
          // prevent a ajax call for every keypress
          clearTimeout(typingTimer);
          typingTimer = setTimeout(function () {
            datatable.draw();
          }, doneTypingInterval);
        });
      });
    }

  }

  //add class 'datatable-empty' if the databable is empty
  datatable.on('xhr', function (e, settings, json, xhr) {
    let isEmpty = true;
    if (json != null) {
      if (typeof json.data !== 'undefined') {
        if (json.data.length > 0) {
          isEmpty = false;
        }
      }
    }
    let el = $(datatable.table().container());
    let el_no_items = $("#" + el.attr("id") + "-no-items-message");
    if (isEmpty) {
      el.addClass("datatable-empty");
      el_no_items.show();
    }
    else {
      el.removeClass("datatable-empty");
      el_no_items.hide();
    }
  });


}

function get_default_datatable_config() {

  // set amount of pager items ( not available through config
  $.fn.DataTable.ext.pager.numbers_length = 8;

  return {
    "processing": true,
    "serverSide": true,
    // save sorting / page amount / pagenumber in html5 local storage
    stateSave: true,
    // get data with ajax, see: https://datatables.net/manual/server-side#Example-data
    "ajax": {
      "type": "POST",
      // extend our data with filters
      "data": function (data) {
        if ($('.list-filter-form').length > 0) {
          // selects
          if ($('.list-filter-form select').length > 0) {
            $('.list-filter-form select').each(function () {
              data[$(this).attr('name')] = $(this).val();
            });
          }
          // input
          $('.list-filter-form input[type="text"]').each(function () {
            data[$(this).attr('name')] = $(this).val();
          });
          $('.list-filter-form input[type="checkbox"]:checked').each(function () {
            data[$(this).attr('name')] = $(this).val();
          });
        }
      }
    },
    // disable search, we have our own search
    "searching": false,

    // options for amount of rows per page
    "lengthMenu": [[25, 50, 100, 99999], [25, 50, 100, "Alles"]],

    // order in which the elements are place
    // see: https://datatables.net/reference/option/dom
    dom: "plBfrtip",

    // plugins
    buttons: [
      {
        extend: 'copy',
        exportOptions: {
          // don't export columns with the no-export class
          columns: ':not(.no-export)'
        }
      },
      {
        extend: 'csv',
        exportOptions: {
          // don't export columns with the no-export class
          columns: ':not(.no-export)'
        }
      },
      {
        extend: 'print',
        exportOptions: {
          // don't export columns with the no-export class
          columns: ':not(.no-export)'
        }
      },
    ],
    select: true,
    fixedHeader: true,

    // translations
    "language": {
      "sProcessing": "Een moment geduld aub - bezig met laden...",
      "sLengthMenu": "_MENU_ resultaten weergeven",
      "sZeroRecords": "Geen resultaten gevonden",
      "sInfo": "_START_ tot _END_ van _TOTAL_ resultaten",
      "sInfoEmpty": "Geen resultaten gevonden",
      "sInfoFiltered": " (gefilterd uit _MAX_ resultaten)",
      "sInfoPostFix": "",
      "sSearch": "Zoeken:",
      "sEmptyTable": "Geen resultaten gevonden",
      "sInfoThousands": ".",
      "sLoadingRecords": "Een moment geduld aub - bezig met laden...",
      "oPaginate": {
        "sFirst": "Eerste",
        "sLast": "Laatste",
        "sNext": "<svg width=\"8\" height=\"13\" viewBox=\"0 0 8 13\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n" +
          "<path d=\"M7.33984 6.28516C7.58594 6.55859 7.58594 6.96875 7.33984 7.21484L2.08984 12.4648C1.81641 12.7383 1.40625 12.7383 1.16016 12.4648C0.886719 12.2188 0.886719 11.8086 1.16016 11.5625L5.94531 6.77734L1.16016 1.96484C0.886719 1.71875 0.886719 1.30859 1.16016 1.0625C1.40625 0.789062 1.81641 0.789062 2.0625 1.0625L7.33984 6.28516Z\" fill=\"#15151E\"/>\n" +
          "</svg>\n",
        "sPrevious": "<svg width=\"7\" height=\"13\" viewBox=\"0 0 7 13\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n" +
          "<path d=\"M0.410156 6.28516L5.66016 1.0625C5.90625 0.789062 6.31641 0.789062 6.58984 1.0625C6.83594 1.30859 6.83594 1.71875 6.58984 1.96484L1.77734 6.75L6.5625 11.5625C6.83594 11.8086 6.83594 12.2188 6.5625 12.4648C6.31641 12.7383 5.90625 12.7383 5.66016 12.4648L0.410156 7.21484C0.136719 6.96875 0.136719 6.55859 0.410156 6.28516Z\" fill=\"#A5A5AB\"/>\n" +
          "</svg>\n"
      },
      "oAria": {
        "sSortAscending": ": activeer om kolom oplopend te sorteren",
        "sSortDescending": ": activeer om kolom aflopend te sorteren"
      },
      // plugin translations
      select: {
        rows: "%d rijen geselecteerd"
      },
      buttons: {
        copy: "Kopiëer",
        csv: "CSV bestand",
        selectAll: "Selecteer alles",
        selectNone: "Deselecteer alles",
      },

    },
  }
}


function defaultConfirm(callback, params) {

  if (typeof swal !== 'undefined') {
    vals = {
      title: (typeof params.title !== 'undefined') ? params.title : '',
      type: (typeof params.type !== 'undefined') ? params.type : 'warning',
      showConfirmButton: (typeof params.showConfirmButton !== 'undefined') ? params.showConfirmButton : true,
      showCancelButton: (typeof params.showCancelButton !== 'undefined') ? params.showCancelButton : true,
      cancelButtonText: (typeof params.cancelButtonText !== 'undefined') ? params.cancelButtonText : "Annuleren",
      confirmButtonText: (typeof params.confirmButtonText !== 'undefined') ? params.confirmButtonText : 'Ja',
      useRejections: true //backwards compatible sweetalert2 6 en 7
      //,showLoaderOnConfirm: true: disabled, should work with preConfirm
    };
    vals.confirmButtonColor = (vals.type == 'warning' ? "#DD6B55" : (vals.type == 'info' ? "#34A7DD" : (vals.type == 'success' ? "#34A7DD" : "#AEDEF4")));

    if (typeof params.html !== 'undefined') {
      vals.html = params.html;
    }
    else {
      vals.text = params.text;
    }
    swal(vals).then(
      function (result) {
        // when user clicks OK, callback function should be defined when calling defaultConfirm
        callback();
      }, function (dismiss) {
        // dismiss can be 'cancel', 'overlay', 'esc' or 'timer'
        return false;
      }
    );
  }
  else {
    if (confirm($title)) {
      callback();
    }
  }
}

$(document).ready(function () {

  if (!buildPopper()) {
    buildQtip();
  }

  if (typeof $("a.imagegallery").fancybox == 'function') { //controleer of fancybox is available

    console.warn("Fancybox is deprecated. Stap over naar SimpleLightbox of andere image popup");

    $("a.imagegallery").fancybox({
      'titlePosition': 'inside',
      'transitionIn': 'elastic',
      'transitionOut': 'elastic',
      'speedIn': 600,
      'speedOut': 200,
      // fix for page going to the top when fancybox opens (on mobile)
      helpers: {
        overlay: {
          locked: false
        }
      }
    });
  }

  if (typeof $(".time").on === 'function') { //backwards compatible
    $(".time").on('change', function () {
      if ($(this).val() != '') {
        var allow_single_minutes = $(this).hasClass('allow-single-minutes');
        var convert_hours_format = $(this).hasClass('convert-hours-format');
        var re = /^\d{1,2}:\d{2}([ap]m)?$/;
        var value = $(this).val().trim();
        var splittime = value.split(":");

        if (convert_hours_format && splittime[1]) {
          switch (parseFloat(splittime[1])) {
            case 25:
              splittime[1] = 15;
              break;
            case 50:
              splittime[1] = 30;
              break;
            case 75:
              splittime[1] = 45;
              break;
          }
        }

        var success = true;
        if (!$(this).val().match(re)) {
          success = false;

          var hours = '';
          var minutes = '';
          value = value.replaceArray([",", ";", ":", " ", "."], "");
          if (value.length <= 2) {
            hours = value;
          }
          else {
            hours = value.substring(0, (value.length - 2));
          }
          hours = zeroFill(hours, 2);


          if (value.length >= 3) {
            minutes += zeroFill(value.substring(value.length - 2));
          }
          else {
            minutes += "00";
          }

          if (convert_hours_format) {
            switch (parseFloat(minutes)) {
              case 25:
                minutes = 15;
                break;
              case 50:
                minutes = 30;
                break;
              case 75:
                minutes = 45;
                break;
            }
          }

          var time = hours + ":" + minutes;
          if (time.match(re)) {
            $(this).val(time);
            splittime = time.split(":");
            success = true;
          }
        }

        if (success && (splittime[0] > 23 || splittime[1] > 59)) {
          success = false;
        }
        if (success && !allow_single_minutes && !(splittime[1] == 0 || splittime[1] == '00' || splittime[1] == 15 || splittime[1] == 30 || splittime[1] == 45)) {
          success = false;
        }
        if (!success) {
          $(this).val('');
          var alert_txt = "Voer een geldige tijd in (formaat 00:00)";
          if (!allow_single_minutes) {
            alert_txt += "\nEen tijd bestaat uit uren en kwartieren.";
          }
          alert(alert_txt);
          obj = $(this);
          setTimeout(function () {
            obj.trigger("focus");
          }, 50);
        }
        else {
          $(this).val(zeroFill(splittime[0], 2) + ":" + zeroFill(splittime[1]));
        }
      }
    });
  }

  if (typeof window.flatpickr == 'function') { //controleer of flatpickr aanwezig is
    $(".datepicker").each(function () {
      $(this).flatpickr({
        allowInput: true,
        dateFormat: "d-m-Y",
        locale: "nl",
        weekNumbers: $(this).hasClass('datepicker_weeks'),
        onClose: function (dates, currentdatestring, picker) { //fix manual changing value.
          picker.setDate(picker._input.value);
        }
      });
    })
    $(document).on("change", ".datepicker", function () {
      //when entering 01122020 make 01-12-2020, for fast editing
      const val = $(this).val();
      if (val.length == 8) {
        $(this)[0]._flatpickr.setDate(val.substring(0, 2) + "-" + val.substring(2, 4) + "-" + val.substring(4), true, "d-m-Y");
      }
    });
  }

  $(".emailencrypted").each(function () {

    var emailriddlerarray = $(this).attr("title").split(",");
    var encryptedemail_id24 = ''; //variable to contain encrypted email
    for (var i = 0; i < emailriddlerarray.length; i++) {
      encryptedemail_id24 += String.fromCharCode(emailriddlerarray[i]);
    }
    $(this).attr("href", "mailto:" + encryptedemail_id24);
    $(this).text(encryptedemail_id24);
    $(this).attr("title", "");
  });

  /**
   * Show confirm swal, and redirect to url on success
   * data-gsd-title: titel
   * data-gsd-text: question
   */
  $(document).on('click', "a.gsd-delete", function (event) {
    event.preventDefault();

    var title = "Verwijderen";
    if ($(this).is('[data-gsd-title]')) {
      title = $(this).attr('data-gsd-title');
    }
    else if ($(this).is('[title]')) {
      title = $(this).attr('title');
    }
    var cancelButtonText;
    if ($(this).is('[data-gsd-cancel]')) {
      cancelButtonText = $(this).attr('data-gsd-cancel');
    }
    //function confirmDelete(_href, _title, _text, _type, _showCancelButton, _confirmButtonText, dummy, _html)
    confirmDelete(
      $(this).attr('href'),
      title,
      $(this).attr('data-gsd-text')
      , "",
      null,
      null,
      null,
      $(this).attr('data-gsd-text').replace(/\\n/g, "<br/>").replace(/\n/g, "<br/>"), //\n string en \n karakter omzetten in <br/>
      cancelButtonText
    );
  });

  /**
   * Show confirm swal.
   * When used on a link, after confirm wil be redirected to href
   * If on submit, form wil be submitted
   * data-gsd-title: titel
   * data-gsd-text: question
   */
  $(".gsd-confirm").on('click', function (event) {

    var title = "Opslaan";
    if ($(this).is('[data-gsd-title]')) {
      title = $(this).attr('data-gsd-title');
    }
    else if ($(this).is('[title]')) {
      title = $(this).attr('title');
    }
    var question = "Weet u het zeker?";
    if ($(this).is('[data-gsd-text]')) {
      question = $(this).attr('data-gsd-text').replace(/\\n/g, "\n") //\n string (niet fysieke enter maar tekst) omzetten in br
    }
    var $type = 'warning';
    if ($(this).is('[data-gsd-type]')) {
      $type = $(this).attr('data-gsd-type'); //\n string (niet fysieke enter maar tekst) omzetten in br
    }
    if (typeof swal !== 'undefined') {
      var initial_button = $(this);
      event.preventDefault();
      var vals = {
        title: title,
        type: $type,
        showCancelButton: true,
        confirmButtonColor: ($type == 'warning' ? "#DD6B55" : ($type == 'info' ? "#34A7DD" : ($type == 'success' ? "#34A7DD" : "#AEDEF4"))),
        confirmButtonText: "OK",
        cancelButtonText: "Annuleren",
        useRejections: true //backwards compatible sweetalert2 6 en 7
        ////,showLoaderOnConfirm: true: disabled, should work with preConfirm
      };
      vals.text = question;
      swal(vals).then(
        function (result) {
          if (initial_button.attr('href') !== undefined) { // if this is a link with an href
            window.location.href = initial_button.attr('href');
          }
          else { //submit/button, result is an optional parameter, needed for modals with input
            initial_button.off();
            initial_button.trigger("click");
          }
          return true;
        }, function (dismiss) {
          // dismiss can be 'cancel', 'overlay', 'esc' or 'timer'
          return false;
        }
      );
    }
    else {
      if (!confirm(title + "\n" + question)) {
        event.preventDefault();
        return false;
      }
    }

  });

  if ($('.pager-pageamount').length) {
    $('.pager-pageamount').on('change', function (e) {
      e.preventDefault();
      $(this).parents('form').submit();
    })
  }

  $('.gsd_uploader_wrapper input[type=file]').each(function () {
    $(this).on('change', function (e) {
      if (e.target.value) {
        var filename = e.target.value.split('\\').pop();
        if ($(this).parent().find(".gsd_uploader_filename").length > 0) {
          $(this).parent().find(".gsd_uploader_filename").html('<b> ' + filename + " </b>");
        }
        else {
          $(this).after('<span class="gsd_uploader_filename"><b> ' + filename + " </b></span>");
        }
      }
    });
  });

  /*********************
   Function to prevent being able to click a button multiple times, which can cause problems when it performs database actions
   Use the function preventDoubleclick for ajax buttons
   *********************/
  if ($('.prevent-doubleclick').length) {
    $('.prevent-doubleclick').on('click', function (e) {
      if ($(this).hasClass('is-clicked')) {
        // button has already been clicked, do not execute action again
        e.preventDefault();
      }
      else {
        $(this).addClass('is-clicked');
        // option to set the new text through the data attribute
        if ($(this).is('[data-isloadingtext]')) {
          $(this).text($(this).data('isloadingtext'));
        }
        // option to add a class when the button is disabled through the data attribute
        if ($(this).is('[data-disabledclass]')) {
          $(this).addClass($(this).data('disabledclass'));
        }
      }
    });
  }

  //function to prevent double click on input / a element, and show spinner
  $("input.lockonsubmit,a.lockonsubmit").on("click", function (e) {
    if ($(this).hasClass("disabled")) {
      e.preventDefault();
      return;
    }
    $(this).addClass("disabled");
    if ($(this).is("a")) {
      $(this).append('<span class="fa fa-circle-o-notch fa-spin fa-fw lockonsubmit_spinner"></span>');
    }
    else {
      $(this).after('<span class="fa fa-circle-o-notch fa-spin fa-fw lockonsubmit_spinner"></span>');
    }
    return true;
  });

});

(function ($) {
  $.fn.table_to_div = function (options) {
    var settings = $.extend({}, {}, options);
    return this.each(function () {
      var output = "";
      var data = "";
      //settings.prefix = settings.prefix+"_";
      columns = [];
      table_target = $(this);
      var tr_count = 0;
      //LOOP OVER ALL ROWS


      output = table_target.html()
        .replace(/<tbody/gi, "<div")
        .replace(/<tr/gi, "<div")
        .replace(/<\/tr>/gi, "</div>")
        .replace(/<td/gi, "<div")
        .replace(/<\/td>/gi, "</div>")
        .replace(/<\/tbody/gi, "<\/div")
        .replace(/trhover/gi, "");

      var j_output = $(output);
      //loop over each child div = rows
      j_output.find('> div').each(function () {
        var col_count = 0;
        //loop over each child div = columns
        $(this).find('> div').each(function () {
          if (tr_count == 0) {
            columns.push($(this).html());
          }
          else {
            $(this).wrap("<div>").before('<label>' + columns[col_count] + '</label>\n');
          }
          col_count++;
        });
        if (tr_count == 0) {
          $(this).remove();
        }
        tr_count++;
      });
      $(this).wrap('<div class="default_table_mobile">');

      table_target.replaceWith(j_output.html());

    });
  };
})(jQuery);

function setAcceptsCookies() {
  var cname = "cc_first_visit";
  var cvalue = "true";
  var exdays = 180; //aantal dagen
  var d = new Date();
  d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
  var expires = "expires=" + d.toUTCString();
  document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}

function hasAcceptedCookies() {
  var cname = "cc_first_visit";
  var name = cname + "=";
  var decodedCookie = decodeURIComponent(document.cookie);
  var ca = decodedCookie.split(';');
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) == ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
      //return c.substring(name.length, c.length);
      return true;
    }
  }
  return false;
}

function getCookieValue(cookie_name) {
  var b = document.cookie.match('(^|;)\\s*' + cookie_name + '\\s*=\\s*([^;]+)');
  return b ? b.pop() : '';
}

function setCookie(name, value, days) {
  var expires = "";
  if (days) {
    var date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    expires = "; expires=" + date.toUTCString();
  }
  document.cookie = name + "=" + (value || "") + expires + "; path=/";
}

/**
 * Block submit on enter
 */
function blockEnterSubmit() {
  $(document).ready(function () {
    $(window).on("keydown", function (event) {
      if ((event.keyCode == 13) && event.target.type != "textarea") {
        event.preventDefault();
        return false;
      }
    });
  });
}

/**
 *  Function will parse a json string to JS objects/arrays
 *  and convert all numeric strings to integers (fabricjs fails on numeric strings)
 */
function parseJson(json_string) {
  if (json_string == '') {
    return json_string;
  }

  json_string = JSON.parse(json_string, function (k, v) {
    if (v === 'false') {
      return false
    }
    else if (v === 'true') {
      return true;
    }
    else if (k === "text" || k === "laser_text") {
      return v;
    }
    else if (typeof v === "number") {
      return v;
    }
    // a float eg 0.85
    else if (typeof v !== "object" && isNumeric(v) === true && isNumberFloat(v)) {
      return parseFloat(v, 10);
    }
    // a number with leading zeros, eg 000012
    else if (typeof v == "string" && isNumeric(v) === true && v.substring(0, 1) == 0) {
      return v;
    }
    // a rounded number eg 10
    else if (typeof v !== "object" && isNumeric(v) === true) {
      return parseInt(v, 10);
    }
    else {
      return v;
    }
  });

  return json_string;
}

function isNumeric(mixed_var) {
  return (typeof (mixed_var) === 'number' || typeof (mixed_var) === 'string') && mixed_var !== '' && !isNaN(mixed_var);
}

function isNumberFloat(n) {
  return isNumeric(n) && n % 1 !== 0;
}

/**
 * Listen when an element is scrolled into viewport.
 * Animate when viewed.
 * Use animate.css <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.0/animate.min.css">
 */
function addScrollListener() {
  // Check if element is scrolled into view
  function isScrolledIntoView(elem) {
    //if( $(window).width() <= 767 ) return false; //kleine schermen niet animeren
    var docViewTop = $(window).scrollTop();
    var docViewBottom = docViewTop + $(window).height();

    var elemTop = $(elem).offset().top;
    var elemBottom = elemTop + $(elem).height();

    // return elemTop > docViewTop;
    return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
  }

  // If element is scrolled into view, fade it in
  $(window).on("scroll", function () {
    $('.scroll-animations').each(function () {
      if (isScrolledIntoView(this) === true) {
        var $el = $(this);
        $el.addClass($(this).attr("data-scoll-animations"));
        var delay = 0;
        var duration = '1s';
        if ($el.attr('data-scroll-animations-delay')) {
          delay = $el.attr('data-appear-animation-delay');
        }
        if ($el.attr('data-scroll-animations-duration')) {
          duration = $el.attr('data-appear-animation-duration');
        }
        if (duration != '1s') {
          $el.css('animation-duration', duration);
        }

        setTimeout(function () { //set class after animation done
          $el.addClass('scroll-animations-visible');
        }, delay);

      }
    });
  });
  //triggeren, zodat eventuele element zichtbaar zijn.
  $(window).trigger("scroll");
}

/**
 * Blocks button and shows inline fontawesome loader
 * @param selector: id/classname button
 */
function showButtonLoader(selector) {
  $(selector).attr("disabled", true).prepend('<span class="fa fa-circle-o-notch fa-spin"></span> ');
}

/**
 * Removes button block and loader
 * @param selector: id/classname button
 */
function removeButtonLoader(selector) {
  $(selector).attr("disabled", false).find('.fa-circle-o-notch').remove();
}

/**
 * Enable sorting of tables with tableDnD
 * @param moveurl: url to request on drop row
 */
function gsdRowSorter(moveurl) {

  $("#sortablecontrol").one("click", function () {

    $(".sort-td").show();
    $("#sortablecontrol").val('Sorteren uitzetten').addClass('sorton');

    $("#sortable").tableDnD({
      onDrop: function (table, row) {
        $('#message')
          .addClass("message")
          .text('Volgorde opslaan...')
          .load(moveurl + $.tableDnD.serialize('id'), function () {
            $('#message').text('Volgorde opgeslagen');
          });
      }
    });

    $(this).one("click", function () {
      $(".sort-td").hide();
      $("#sortable tr").off().css({cursor: "default"});
      $("#sortablecontrol").val('Sorteren aanzetten').removeClass('sorton');
      gsdRowSorter(moveurl);
    });
  });

}


/**
 * This function lazy loads thumbs instead of the complete vimeo/youtube video's
 * This will give a much smaller file size, and much faster loading time of page, which is good for SEO.
 * Onclick the thumb is replaced with the youtube/vimeo iframe
 * ALso the thumbs are lazy loaded, so only loaded when in viewport.
 */
function videoSeofriendly() {

  var vimeo = document.querySelectorAll('.vi-lazyload'),

    vimeo_observer,                                                 //Intersection Observer API
    template_wrap,
    template_content,
    template_playbtn,
    template_iframe,
    settings_observer_rootMargin = '200px 0px';                  //Intersection Observer API option - rootMargin (Y, X)

  if (vimeo.length > 0) {

    //create elements
    template_wrap = document.createElement('div');
    template_content = document.createElement('div');
    template_playbtn = document.createElement('div');
    template_iframe = document.createElement('iframe');

    //set attributes
    template_wrap.classList.add('vi-lazyload-wrap');
    template_content.classList.add('vi-lazyload-content');
    template_playbtn.classList.add('vi-lazyload-playbtn');

    template_iframe.setAttribute('allow', 'autoplay;fullscreen');
    //template_iframe.setAttribute('allowfullscreen','');


    vimeo_observer = new IntersectionObserver(function (elements) {

      elements.forEach(function (e) {

        //VARIABLES
        var this_element = e.target,

          this_wrap,
          this_content,
          this_playbtn,
          this_iframe,

          this_data_id = e.target.dataset.id,
          this_data_type = e.target.dataset.type;
        this_data_thumb = e.target.dataset.thumb;

        //if element appears in viewport
        if (e.isIntersecting === true) {


          //wrap
          this_wrap = template_wrap.cloneNode();
          this_element.append(this_wrap);

          //content
          this_content = template_content.cloneNode();
          this_wrap.append(this_content);

          //play btn
          this_playbtn = template_playbtn.cloneNode();
          this_content.append(this_playbtn);

          //background-image
          if (this_data_type === "vimeo") {
            if (this_data_thumb && this_data_thumb != "") {
              //first see if youtube.thumb_url is not empty
              this_content.style.setProperty('--vi-lazyload-img', 'url("' + this_data_thumb + '")');
            }
            else {
              //try json
              $.get("https://vimeo.com/api/v2/video/" + this_data_id + ".json", function (data) {
                if (data[0]) {
                  this_content.style.setProperty('--vi-lazyload-img', 'url("' + data[0].thumbnail_large + '")');
                }
              });
            }

            //onclick create iframe
            this_playbtn.addEventListener('click', function () {
              this_iframe = template_iframe.cloneNode();
              this_iframe.src = 'https://player.vimeo.com/video/' + this_data_id + '?autoplay=1&autopause=0';
              this_content.append(this_iframe);
            });

          }
          else if (this_data_type === "youtube") {
            //youtube thumb is directly available
            if (this_data_thumb && this_data_thumb != "") {
              //first see if youtube.thumb_url is not empty
              this_content.style.setProperty('--vi-lazyload-img', 'url("' + this_data_thumb + '")');
            }
            else {
              var thumb = "https://i.ytimg.com/vi/" + this_data_id + "/hqdefault.jpg";
              this_content.style.setProperty('--vi-lazyload-img', 'url("' + thumb + '")');
            }

            //onclick create iframe
            this_playbtn.addEventListener('click', function () {
              this_iframe = template_iframe.cloneNode();
              this_iframe.src = 'https://www.youtube.com/embed/' + this_data_id + '?autoplay=1';
              this_content.append(this_iframe);
            });

          }
          else {
            console.error("videoSeofriendly: unkown type!!");
          }

          //Unobserve after image lazyloaded
          vimeo_observer.unobserve(this_element);

          //LOG
          //console.log('DONE - ' + this_data_id);
        }

      });

    }, {
      rootMargin: settings_observer_rootMargin,
    });

    vimeo.forEach(function (e) {
      //Intersection Observer API - observe elements
      vimeo_observer.observe(e);
    });

  }

}

/**
 * When using this function, all elements with class lazybg will lazyload the background-image defined in attribute data-lazybg
 * The .lazybg class should only be added to elements outside of the viewport, to keep a good user experience.
 *
 */
function lazyLoadBackgroundImages() {

  document.addEventListener("DOMContentLoaded", function () {
    var lazyloadImages;

    if ("IntersectionObserver" in window) {

      lazyloadImages = document.querySelectorAll(".lazybg");

      var imageObserver = new IntersectionObserver(function (entries, observer) {
        entries.forEach(function (entry) {
          if (entry.isIntersecting) {
            var image = $(entry.target);
            if (image.attr("data-lazybg").length > 0) {
              //console.log("Loading: " + image.attr("data-lazybg"));
              image.css("background-image", "url('" + image.attr("data-lazybg") + "')");
              image.attr("data-lazybg", null);
              imageObserver.unobserve(image[0]);
            }
          }
        });
      });

      lazyloadImages.forEach(function (image) {
        imageObserver.observe(image);
      });

    }
    else {
      var lazyloadThrottleTimeout;
      lazyloadImages = document.querySelectorAll(".lazybg");

      function lazyload() {
        if (lazyloadThrottleTimeout) {
          clearTimeout(lazyloadThrottleTimeout);
        }

        lazyloadThrottleTimeout = setTimeout(function () {
          var scrollTop = window.pageYOffset;
          lazyloadImages.forEach(function (img) {
            if (img.offsetTop < (window.innerHeight + scrollTop)) {
              var image = $(img);
              if (image.attr("data-lazybg").length > 0) {
                //console.log("Loading: " + image.attr("data-lazybg"));
                image.css("background-image", "url('" + image.attr("data-lazybg") + "')");
                image.attr("data-lazybg", null);
              }
            }
          });
          if (lazyloadImages.length == 0) {
            document.removeEventListener("scroll", lazyload);
            window.removeEventListener("resize", lazyload);
            window.removeEventListener("orientationChange", lazyload);
          }
        }, 20);
      }

      document.addEventListener("scroll", lazyload);
      window.addEventListener("resize", lazyload);
      window.addEventListener("orientationChange", lazyload);
    }
  })


}

/**
 * Function for handling javascript errors, and sending them to the error mailbox
 * The error is also logged to jserrors.log
 * Very old browsers IE 11 and older are ignored
 *
 */
function addErrorHandler() {
  if (window.document.documentMode) {
    //this is IE 11 or older...ignore
    console.log("Please upgrade to an up-to-date browser! This website will not function properly.");
    return;
  }
  window.addEventListener('error', function (e) {
    e.url = location.href;
    e.userAgent = window.navigator.userAgent;

    if (e.message === "") return; //ignore empty message
    if (e.message === "Script error.") return; //ignore this general message
    // let bot_identifiers = [
    //   'bot',
    //   'slurp',
    //   'crawler',
    //   'spider',
    //   'curl',
    //   'facebook',
    //   'fetch',
    //   'mediapartners',
    //   'python-requests',
    //   'buck',
    //   'googleimageproxy',
    //   'megaindex',
    //   'bingpreview',
    //   'bubing',
    //   'addscanner',
    //   'google favicon'
    // ];
    //
    // e.userAgent


    let jsonString = JSON.stringify(e, ["message", "arguments", "type", "name", "filename", "lineno", "url", "userAgent"])
    if (jsonString === undefined) return; //stringify failed, exit.

    // console.log(e);
    fetch("/error404?type=js", { //link to backend
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: jsonString
    }).then(res => res.json()).then((response) => {
      //console.log(response);
    });
  })
}

/**
 * Create GsdModal
 *
 * The id is needed when multiple modals are on used on the page
 *
 * Triggers the following events
 *   $(document).on("gsdModalClose", function (e, jsondata) {
 *   //jsondata contains id
 *   });
 *  $(document).on("gsdModalPreOpen", function (e, jsondata) {
 *    //jsondata contains id, and if ajax call url
 *   });
 *  $(document).on("gsdModalPostOpen", function (e, jsondata) {
 *    //jsondata contains id, and if ajax call url
 *   });
 *
 *   //small example
 *   //you could add a trigger gsdModalSelect in the content of the modal
 *   $(document).trigger("gsdModalSelect",[$(this).attr("data-data")]);
 *   //and listen to the content on the source page
 *   $(document).on("gsdModalSelect", function (e, msg) {
 *   });
 *
 */
class GsdModal {

  id = "";
  width = "";
  height = "";
  isPersistant = false;
  position = 'center';

  constructor(id = "gsd-modal-default-id", persistant = false, position = 'center') {
    this.id = id;
    this.isPersistant = persistant
    this.position = position;
  }

  static gsdLoader = null;
  static loader = null;

  setLoader(src, width, maxWidth, height) {
    this.loader = `<img src="${src}" style="width: ${width}; max-width: ${maxWidth}; height: ${height};"/>`;
  }

  /**
   * Set the width of the complete modal.
   * @param width
   */
  setWidth(width) {
    this.width = width;
  }

  getWidthStyle() {
    if (this.width == "") return "";
    return "width: " + this.width + ";";
  }

  /**
   * Set the height of the modal content. (not the complete modal)
   * @param height
   */
  setHeight(height) {
    this.height = height;
  }

  getHeightStyle() {
    if (this.height == "") return "";
    return "height: " + this.height + ";";
  }

  getId() {
    return this.id;
  }

  getPosition() {
    return this.position;
  }

  /**
   * Initiaze modal
   */
  init() {
    let self = this;

    let html =
      `<div class="gsd-modal" id="` + this.getId() + `" data-id="` + this.getId() + `">
        
          <div class="gsd-modal-position-${this.getPosition()}">
        
            <div class="gsd-modal-bg-glass">
              <div></div>
            </div>
        
            <div class="gsd-modal-container" style="` + this.getWidthStyle() + `">
              <div class="gsd-modal-header">
                <div class="gsd-modal-title">
                  Modal Header
                </div>
                <a href="#" class="gsd-modal-close">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><rect opacity="0.3" x="2" y="2" width="20" height="20" rx="5" fill="currentColor"/><rect x="7" y="15.3137" width="12" height="2" rx="1" transform="rotate(-45 7 15.3137)" fill="currentColor"/><rect x="8.41422" y="7" width="12" height="2" rx="1" transform="rotate(45 8.41422 7)" fill="currentColor"/></svg>
                </a>
              </div>
              <div class="gsd-modal-content" style="` + this.getHeightStyle() + `"></div>
            </div>
        
          </div>
        
        </div>`;

    if ($("#" + this.getId()).length == 0) {
      $("body").append(html);
    }
    else if (this.getId() != "loader") {
      //console.log("DOUBLE GSDMODAL: " + this.getId()."")
    }

    $(document).ready(function () {

      $("#" + self.getId() + " .gsd-modal-close").on("click", function (e) {
        self.close();
      });

      $("#" + self.getId() + " .gsd-modal-bg-glass").on("click", function (e) {
        if (!self.isPersistant) self.close();
      });

      //sluiten escape
      $(document).on('keydown', function (event) {
        if (event.key === "Escape" && !self.isPersistant) {
          self.close();
        }
      });

    });

  }

  /**
   * Opens modal, and loads html contents from url
   * @param url
   * @param title
   */
  open(url, title) {

    $(document).trigger("gsdModalPreOpen", {"id": this.getId(), "url": url});

    $("#" + this.getId() + " .gsd-modal-content").html(this.loader);
    if (title != undefined && title != "") {
      $("#" + this.getId() + " .gsd-modal-title").html(title).show();
    }
    else {
      $("#" + this.getId() + " .gsd-modal-title").hide();
    }

    this.show();

    let self = this;

    $.get(url, function (html) {

      $("#" + self.getId() + " .gsd-modal-content").html(html);

      $(document).trigger("gsdModalPostOpen", {"id": self.getId(), "url": url});

    }).fail(function (e) {
      alert("Foutmelding: het is niet gelukt om deze request uit te voeren.");
    });
  }

  /**
   * Opens modal, and loads html contents from url IN IFRAME
   * This is handy for post actions.
   * @param url
   * @param title
   */
  openIframe(url, title) {

    $(document).trigger("gsdModalPreOpen", {"id": this.getId(), "url": url});

    $("#" + this.getId() + " .gsd-modal-content").html(this.loader);
    if (title != undefined && title != "") {
      $("#" + this.getId() + " .gsd-modal-title").html(title).show();
    }
    else {
      $("#" + this.getId() + " .gsd-modal-title").hide();
    }

    this.show();

    let self = this;

    let iframe = $('<iframe src="'+url+'"></iframe>');
    $("#" + self.getId() + " .gsd-modal-content").append(iframe);

    $(document).trigger("gsdModalPostOpen", {"id": self.getId(), "url": url});

  }

  /**
   * Set content of modal, by string
   * @param title: title of modal
   * @param content: html content
   */
  openContent(title, content) {

    $(document).trigger("gsdModalPreOpen", {"id": this.getId()});

    if (title != undefined && title != "") {
      $("#" + this.getId() + " .gsd-modal-title").html(title).show();
    }
    else {
      $("#" + this.getId() + " .gsd-modal-title").hide();
    }

    this.show();

    $("#" + this.getId() + " .gsd-modal-content").html(content);

    $(document).trigger("gsdModalPostOpen", {"id": this.getId()});
  }

  /**
   * Set jquery Object in modal. Handy for reuse of jQuery object, and linked triggers.
   * @param title: title of modal. if empty title container is hidden
   * @param contentJQuery: jQuery object that is placed in content
   */
  openContentJQuery(title, contentJQuery) {

    $(document).trigger("gsdModalPreOpen", {"id": this.getId()});

    if (title != undefined && title != "") {
      $("#" + this.getId() + " .gsd-modal-title").html(title).show();
    }
    else {
      $("#" + this.getId() + " .gsd-modal-title").hide();
    }

    this.show();

    $("#" + this.getId() + " .gsd-modal-content").append(contentJQuery);

    $(document).trigger("gsdModalPostOpen", {"id": this.getId()});
  }

  /**
   * Shows loader modal dialog
   * @param url
   */
  buildLoader(title) {

    if (title != undefined && title != "") {
      $("#" + this.getId() + " .gsd-modal-title").html(title).show();
    }
    else {
      $("#" + this.getId() + " .gsd-modal-title").hide();
    }
    $("#" + this.getId() + " .gsd-modal-content").html(this.loader);

  }

  /**
   * Show dialog
   */
  show() {
    $('body').css('overflow', 'hidden');
    $("#" + this.getId()).addClass("gsd-modal-show");
  }

  /**
   * hide dialog
   */
  hide() {
    $('body').css('overflow', 'auto');
    $("#" + this.getId()).removeClass("gsd-modal-show");
  }

  /**
   * Closes modal
   */
  close() {
    $(document).trigger("gsdModalClose", [this.getId()]);
    this.hide();
  }

  /**
   * Show popup loader
   */
  static showLoader(title = "Loading...", src = '/gsdfw/images/loading1.gif', width = '450px', maxWidth = '100%', height = 'auto') {
    let gsdLoader = new GsdModal("loader");
    gsdLoader.setLoader(src, width, maxWidth, height);
    gsdLoader.setWidth("400px");
    gsdLoader.init();
    gsdLoader.buildLoader(title);
    gsdLoader.show();
    GsdModal.gsdLoader = gsdLoader;
  }

  /**
   * Hide popup loader
   */
  static hideLoader() {
    if (GsdModal.gsdLoader != null) {
      GsdModal.gsdLoader.close();
    }
  }


}

/**
 * Hide popup loader
 */
function hideLoader() {
  swal.close();
}

/**
 * Convert a (multi)select to a bootstrap 5 style select with checkboxes, select all option and searchbox.
 *
 * @param element_id id of original select element
 * @param placeholder (optional) text for placeholder if select is a multiselect
 * @param search_enabled (optional) enable/disable search function; default true
 * @param shortPlaceholder (optional) enable/disable short placeholder for items longer as 18 characters; default true
 */
function gsdSelect(element_id, placeholder = 'Selecteer opties...', search_enabled = true, shortPlaceholder = true) {
  let select_to_replace = document.getElementById(element_id);
  let is_multi = select_to_replace.hasAttribute('multiple');
  let options = select_to_replace.options;

  select_to_replace.style.display = 'none'; // hide original element

  // build new element
  let multiselect = document.createElement('div');
  multiselect.className = 'gsd-select';
  multiselect.setAttribute('data-multiselect-id', element_id);

  function createElementWithHTML(htmlString) {
    let div = document.createElement('div');
    div.innerHTML = htmlString.trim();
    return div.firstChild;
  }

  let multiselect_options = document.createElement('div');
  multiselect_options.className = 'gsd-select-options';
  multiselect.appendChild(createElementWithHTML('<span class="gsd-select-placeholder"><span style="float: right; margin: 4px;" class="fa fa-angle-down"></span></span>'));

  if (search_enabled) {
    let searchContainer = createElementWithHTML('<div style="display: flex; padding-right: 5px"><input type="text" class="gsd-select-searchbox" placeholder="Zoeken..."/><span title="Zoekbalk legen" class="fa fa-times gsd-select-reset" style="align-self: center; cursor: pointer;"></span></div>');
    multiselect_options.appendChild(searchContainer);
  }

  if (is_multi) {
    let selectAllLabel = createElementWithHTML('<label for="' + element_id + '_multiselect_select_all"><input type="checkbox" id="' + element_id + '_multiselect_select_all" /><span>Selecteer alles</span></label>');
    multiselect_options.appendChild(selectAllLabel);
  }

  [...options].forEach(function (optionElement) {
    let option;

    if (!is_multi && [...options].indexOf(optionElement) === 0) {
      let placeHolderText = placeholder;
      if (optionElement.textContent !== '') {
        placeHolderText = optionElement.textContent;
      }
      option = createElementWithHTML('<label for="placeholder_' + element_id + '"><input type="checkbox" style="display: none" class="gsd-select-option" id="placeholder_' + element_id + '" />' + placeHolderText + '</label>');
    }
    else if (optionElement.value === '') {
      option = createElementWithHTML('<span class="gsd-select-empty">' + optionElement.text + '</span>');
    }
    else {
      if (is_multi) {
        option = createElementWithHTML('<label for="' + element_id + '_' + optionElement.value + '"><input type="checkbox" class="gsd-select-option" id="' + element_id + '_' + optionElement.value + '" />' + optionElement.text + '</label>');
      }
      else {
        option = createElementWithHTML('<label for="' + element_id + '_' + optionElement.value + '"><input type="checkbox" style="display: none" class="gsd-select-option" id="' + element_id + '_' + optionElement.value + '" />' + optionElement.text + '</label>');
      }
    }
    multiselect_options.appendChild(option);
  });

  multiselect.appendChild(multiselect_options);

  // Show new element
  select_to_replace.parentNode.insertBefore(multiselect, select_to_replace.nextSibling);

  // Handle events
  gsdSelectHandler(element_id, is_multi, placeholder, shortPlaceholder);
}

/**
 * Function for handling the events of gsdSelect()
 *
 * @param element_id
 * @param is_multi
 * @param orig_placeholder
 * @param shortPlaceholder
 */
function gsdSelectHandler(element_id, is_multi, orig_placeholder, shortPlaceholder) {
  let original_select = document.getElementById(element_id);
  let multiselect = document.querySelector('[data-multiselect-id=' + element_id + ']');
  let options = multiselect.querySelector(".gsd-select-options");
  let select_all = multiselect.querySelector('#' + element_id + "_multiselect_select_all");
  let searchbox = multiselect.querySelector('.gsd-select-searchbox');
  let placeholder = multiselect.querySelector('.gsd-select-placeholder');
  let option_placeholder = options.querySelector('#placeholder_' + element_id);
  let empty_options = options.querySelectorAll('.gsd-select-empty');

  function setPlaceholder() {
    let total_checked = 0;
    let checkedOptions = options.querySelectorAll(':checked');

    checkedOptions.forEach(function (option) {
      if (option.id !== element_id + '_multiselect_select_all') {
        total_checked++;
      }
    });

    let angleDownIcon = document.createElement('span');
    angleDownIcon.style.float = 'right';
    angleDownIcon.style.margin = '4px';
    angleDownIcon.className = 'fa fa-angle-down';

    if (total_checked > 0) {
      placeholder.textContent = orig_placeholder + '(' + total_checked + ')';
      placeholder.appendChild(angleDownIcon);
    }
    else {
      placeholder.textContent = orig_placeholder;
      placeholder.appendChild(angleDownIcon);
    }
  }

  function syncValues(option_to_sync, invert = false) {
    let original_select_option = original_select.querySelector("[value='" + option_to_sync.id.replace(element_id + '_', '') + "']");
    if (option_to_sync.id === 'placeholder_' + element_id) {
      original_select_option = original_select.querySelector('option:not([disabled])');
    }
    if (!option_to_sync || !original_select_option) {
      return;
    }
    let option_text = option_to_sync.parentElement.textContent;
    if (option_text.length > 18 && shortPlaceholder) {
      option_text = option_text.slice(0, 18 - 1) + "&hellip;";
    }
    if (invert) {
      option_to_sync.checked = original_select_option.selected;
      if (!is_multi && option_to_sync.checked) {
        placeholder.innerHTML = option_text + "<span style='float: right; margin: 4px;' class='fa fa-angle-down'></span>";
      }
      else if (is_multi) {
        setPlaceholder();
      }
    }
    else {
      if (!is_multi) {
        options.querySelectorAll(':checked').forEach(function (elem) {
          if (elem.id !== option_to_sync.id) {
            elem.checked = false;
          }
        });
        if (option_to_sync.checked) {
          placeholder.innerHTML = option_text + "<span style='float: right; margin: 4px;' class='fa fa-angle-down'></span>";
        }
        else {
          let default_placeholder_text = option_placeholder.parentElement.textContent;
          if (default_placeholder_text.length > 18 && shortPlaceholder) {
            default_placeholder_text = default_placeholder_text.slice(0, 18 - 1) + "&hellip;";
          }
          placeholder.innerHTML = default_placeholder_text;
          multiselect.querySelector('.gsd-select-placeholder').innerHTML += "<span style='float: right; margin: 4px;' class='fa fa-angle-down'></span>";
        }
      }
      else {
        setPlaceholder();
      }

      if (original_select_option.selected !== option_to_sync.checked) {
        original_select_option.selected = option_to_sync.checked;

        let changeEvent = new Event('change');
        original_select.dispatchEvent(changeEvent);
      }
      else {
        original_select_option.selected = option_to_sync.checked;
      }
    }
  }


  // sync options with original_select on load
  options.querySelectorAll('input[type="checkbox"]').forEach(function (option) {
    syncValues(option, true);
    option.parentElement.classList.toggle('gsd-select-option-selected', option.checked);
  });

  if (options.querySelectorAll(':checked').length === options.querySelectorAll('input[type="checkbox"]').length - 1) {
    select_all.checked = true;
    select_all.parentElement.classList.add('gsd-select-option-selected');
  }

  placeholder.addEventListener("click", function () {
    options.classList.toggle("gsd-select-flex");
    if (searchbox && options.classList.contains("gsd-select-flex")) {
      searchbox.focus();
    }
  });

  document.addEventListener("mouseup", function (e) {
    // Check if the target of the click isn't the container nor a descendant of the container
    if (!multiselect.contains(e.target)) {
      if (window.getComputedStyle(options).display !== "none") {
        options.classList.toggle("gsd-select-flex");
      }
    }
  });

  document.addEventListener("keyup", function (e) {
    if (e.key === "Escape" && options.classList.contains("gsd-select-flex")) {
      options.classList.remove("gsd-select-flex");
    }
  });


  options.querySelectorAll('input[type="checkbox"]').forEach(function (checkbox) {
    checkbox.addEventListener('change', function () {
      let current = this.id === checkbox.id;
      // Adjust original select options on change
      syncValues(this);
      if (!is_multi) {
        options.querySelectorAll('input[type="checkbox"]').forEach(function (cb) {
          if (!current || cb.id !== 'placeholder_' + element_id) {
            cb.parentElement.classList.remove('gsd-select-option-selected');
          }
        });
      }
      if (!this.checked) {
        if (!current || this.id !== 'placeholder_' + element_id) {
          this.parentElement.classList.remove('gsd-select-option-selected');
        }
        if (select_all) {
          select_all.parentElement.classList.remove('gsd-select-option-selected');
          select_all.checked = false;
        }
      }
      else {
        this.parentElement.classList.add('gsd-select-option-selected');
        if (option_placeholder && this.id !== 'placeholder_' + element_id) {
          option_placeholder.parentElement.classList.remove('gsd-select-option-selected');
        }
      }
    });

    if (!is_multi) {
      checkbox.addEventListener('click', function () {
        options.classList.toggle('gsd-select-flex');
      });
    }
  });

  function handleOptionChange() {
    let checkedOptions = multiselect.querySelectorAll('.gsd-select-option:checked');
    let countChildren = options.querySelectorAll('.gsd-select-option');

    if (checkedOptions.length == countChildren.length) {
      select_all.checked = true;
      select_all.parentNode.classList.add('gsd-select-option-selected');
    }
    if (!is_multi && checkedOptions.length == 0) {
      option_placeholder.parentElement.classList.add('gsd-select-option-selected');
    }
  }

  Array.from(multiselect.querySelectorAll(".gsd-select-option")).forEach(function (option) {
    option.addEventListener('change', handleOptionChange);
  });

  if (select_all) {
    select_all.addEventListener("click", function () {
      var select_all_checked = this.checked;

      Array.from(multiselect.querySelectorAll(".gsd-select-option")).forEach(function (option) {
        option.checked = select_all_checked;

        if (select_all_checked) {
          option.parentNode.classList.add("gsd-select-option-selected");
        }
        else {
          option.parentNode.classList.remove("gsd-select-option-selected");
        }

        // adjust original select options on change
        syncValues(option);
      });
    });
  }

  if (searchbox) {
    searchbox.addEventListener("keyup", function () {
      let valthis = this.value.toLowerCase();
      let labels = options.querySelectorAll("label");
      empty_options.forEach(function (element) {
        element.style.display = 'block';
        if (valthis !== '') {
          element.style.display = 'none';
        }
      });

      labels.forEach(function (label) {
        let text = label.textContent.toLowerCase();
        label.style.display = "block";
        if (text.indexOf(valthis) == -1 || (valthis !== '' && (text === 'selecteer alles' || label.getAttribute('for') === 'placeholder_' + element_id))) { // no match.. hide option, also hide 'select all' by default when searching
          label.style.display = "none";
        }
      });
    });
  }

  document.addEventListener('keydown', function (event) { // prevent posting when pressing enter in searchbox
    if (event.key === 'Enter' && document.activeElement.classList.contains("gsd-select-searchbox")) {
      event.preventDefault();
    }
  });

  if (searchbox) {
    multiselect.querySelector('.gsd-select-reset').addEventListener('click', function () {
      searchbox.value = '';
      let keyupEvent = new Event('keyup');
      searchbox.dispatchEvent(keyupEvent);
    });
  }


}

function saveScrollPosition(name, storageObject) {
  if (storageObject.getItem(name)) {
    window.scroll(0, parseInt(storageObject.getItem(name)));
  }
  addEventListener("scroll", (event) => {
    storageObject.setItem(name, Math.round(window.scrollY));
  });
}

/**
 * Function handy for ajax search
 */
function initDelayKeyUp() {
  (function ($) {
    $.fn.delayKeyup = function (callback, ms) {
      var timer = 0;
      $(this).on("keyup", function () {
        clearTimeout(timer);
        timer = setTimeout(callback, ms);
      });
      return $(this);
    };
  })(jQuery);
}

function updateCookieBanner() {
  localStorage.setItem("consentSet", 'true')
  $("#cookie-banner").css('display', 'none');
  $("body").css('overflow', 'auto')
}

/**
 * Initialize standard google map
 * Uses global variable standardGoogleMapSettings
 * standardGoogleMapSettings = {
 *   lat: 52.090737,
 *   lng: 5.121420,
 *   cloud_console_id: 'your-cloud-console-id',
 *   map_id: 'map', //id of html element div for map, default is 'map'
 *   icon_image: '/images/markers/marker_green.png', //optional, default is '/images/markers/marker.png'
 *   info_window: '<h3>Info window</h3><p>Info window content</p>' //optional, if not set no info window is shown
 * }
 */
function initStandardGoogleMap() {
  let mapOptions = {
    zoom: 11,
    center: new google.maps.LatLng(standardGoogleMapSettings.lat, standardGoogleMapSettings.lng),
    mapId: standardGoogleMapSettings.cloud_console_id
  };

  let map = new google.maps.Map(document.getElementById(standardGoogleMapSettings.map_id ? standardGoogleMapSettings.map_id : 'map'), mapOptions);

  const markerEl = document.createElement("img");
  markerEl.src = standardGoogleMapSettings.icon_image ? standardGoogleMapSettings.icon_image : "/images/markers/marker.png";
  markerEl.style.width = "23px";
  markerEl.style.height = "32px";

  let marker = new google.maps.marker.AdvancedMarkerElement({
    position: new google.maps.LatLng(standardGoogleMapSettings.lat, standardGoogleMapSettings.lng),
    map,
    content: markerEl,
  });

  //if set, a info window is opened right away
  if (standardGoogleMapSettings.info_window !== undefined) {
    const infoWindow = new google.maps.InfoWindow({
      content: standardGoogleMapSettings.info_window,
    });

    // Open the InfoWindow right away
    infoWindow.open({
      anchor: marker,
      map,
    });

    //hide close button
    const style = document.createElement("style");
    style.innerHTML = `
      .gm-ui-hover-effect {
        display: none !important;
      }`
    ;
    document.head.appendChild(style);

  }

};