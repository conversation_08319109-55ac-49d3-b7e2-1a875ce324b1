<?php

  /**
   * Actionloader executes logic, sets variables to the template and loads template
   *
   * @package    GSDframework
   * <AUTHOR>
   * @copyright  2006-2018
   * @link       https://www.gsd.nl
   */

  if (defined('LOG_QUERY') && LOG_QUERY == true) {
    $logendtime = microtime(true);
    logToFile("mysql_log", "BEFORE ACTIONLOADER: " . ($logendtime - $logtime) . ' url: ' . $_SERVER['QUERY_STRING']);
  }

  $current_navitem = Navigation::getInstance()->getActiveItem();
  /** @var GsdActions $current_action */
  $current_action = null;
  $actionname = $current_navitem->getAction();
  if (!empty($_GET['action'])) {
    $actionname = $_GET['action'];
  }

  if (empty($actionname)) {
    //Robert: deze check toegevoegd op 16-08-2024. Volgens mij heeft elke pagina een module en action de laatste jaren.
    throw new Exception("Onbekende module en action! Action is leeg: ".$_SERVER['REQUEST_URI']);
  }

  $classname = gsdActions::loadActionClasses($current_navitem->getModule(), $current_navitem->getPlugin());

  if ($classname == "") {
    throw new Exception("Onbekende classname! Module: " . $current_navitem->getModule() . ", action: " . $current_navitem->getPlugin() . " in actionloader ");
  }

  /** @var gsdActions $current_action */
  $current_action = new $classname;
  $current_action->setPlugin($current_navitem->getPlugin());
  $current_action->setModule($current_navitem->getModule());
  $current_action->setAction($actionname);
  $current_action->template = $current_action->getAction() . 'Success.php';
  $current_action->setPageId($pageId);
  if (!method_exists($current_action, 'execute' . ucfirst($current_action->getAction()))) {
    if (DEVELOPMENT) {
      throw new Exception('Unknown action: execute' . ucfirst($current_action->getAction()) . ' in actionloader ' . $classname);
    }
    elseif ($_SERVER['REQUEST_URI'] != "/") {
      //logToFile("notice",'Unknown action: execute'.ucfirst($current_action->getAction()) . ' in actionloader ' . $classname. ". Redirecting to home.");
      ResponseHelper::redirect("/");
    }
  }

  //load languages
  Trans::loadLanguagefiles($current_action->getModule(), $current_action->getAction());
  call_user_func([$current_action, 'preExecute']);
  call_user_func([$current_action, 'execute' . ucfirst($current_action->getAction())]);

  if ($current_action->template == null) {
    //template is null, exit for ajax call
    ResponseHelper::exit();
  }
  if ($current_action->seo_title == "") {
    $current_action->seo_title = PageMap::getName($pageId);
  }

  Context::setAction($current_action);

  extract(get_object_vars($current_action));

  if ($current_action->template_wrapper_clear) {
    //only load html template, not de template wrapper (index.php template)
    include($current_action->getTemplatePath());
    ResponseHelper::exit();
  }

  if ($current_action->template_wrapper_json !== null) {
    ob_start();
    include($current_action->getTemplatePath());
    $current_action->template_wrapper_json["template"] = ob_get_contents();
    ob_end_clean();
    ResponseHelper::exitAsJson($current_action->template_wrapper_json);
  }


  if (defined('LOG_QUERY') && LOG_QUERY == true) {
    $logendtime = microtime(true);
    logToFile("mysql_log", "AFTER ACTIONLOADER: " . ($logendtime - $logtime));
  }



