{"config": {"component-dir": "vendor/components", "allow-plugins": {"kylekatarnls/update-helper": true, "php-http/discovery": true}, "platform": {"php": "8.4"}}, "repositories": [{"type": "vcs", "url": "https://github.com/OpJePl44tsm4n/omnikassa-php-sdk.git"}, {"type": "vcs", "url": "https://github.com/GSDgit/PracticalAfas.git"}, {"type": "vcs", "url": "https://github.com/PPP01/RichFilemanager-PHP.git"}], "require": {"php": "^8.4", "opjeplaatsman/omnikassa-sdk": "dev-master", "google/recaptcha": "^1.1", "globalcitizen/php-iban": "^v4", "ivaynberg/select2": "^4", "snapappointments/bootstrap-select": "^1", "messagebird/php-rest-api": "^3", "mollie/mollie-api-php": "^2", "ivkos/pushbullet": "^3", "wyz/practicalafas": "dev-fix", "tracy/tracy": "^2.5", "setasign/fpdf": "^1.8", "setasign/fpdi": "^2.0", "setasign/tfpdf": "^1.33", "phpoffice/phpspreadsheet": "^4", "guzzlehttp/guzzle": "~7", "wolfcast/browser-detection": "^2.9", "league/csv": "^9", "myparcelnl/sdk": "^7", "mpdf/mpdf": "^8", "symfony/mailer": "^7", "php81_bc/strftime": "^0.7.6", "phpseclib/phpseclib": "^3", "ibericode/vat": "^2", "hubspot/api-client": "^11", "robthree/twofactorauth": "^v2", "google/auth": "^1", "openai-php/client": "^0.14"}, "require-dev": {"composer/composer": "^2.3"}}