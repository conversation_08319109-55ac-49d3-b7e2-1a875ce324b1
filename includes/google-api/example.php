<?php if(count($cvactiesimportant)>0): ?>
	<h2>Onderhoudshistory belangrijk</h2>
  <table style="border:0px; width:100%;" cellspacing="0" cellpadding="2">
    <tr class="dataTableHeadingRow">
    	<td>Klant</td>
    	<td>Onderhoudsdatum</td>
    	<td>Onderhoudsomschrijving</td>
    </tr>	
  <?php foreach($cvactiesimportant as $cvatimp): ?>
    <tr class="dataTableRow trhover">
      <td><a href="<?php echo PageMap::getUrl('M_CUST_DEVICES').'?organid='.$cvatimp->user->organisation_id.'&cvid='.$cvatimp->cvinfoid ?>"><?php echo $cvatimp->user->getNaamEnBedrijfsnaam() ?></a></td>
      <td><?php echo $cvatimp->getDatum() ?></td>
      <td><?php echo $cvatimp->description ?></td>
      <td></td>
    </tr>
  <?php endforeach; ?>
  </table>
<?php endif; 

require_once DIR_INCLUDES."google-api/Google_Client.php";
require_once DIR_INCLUDES."google-api/contrib/Google_CalendarService.php";
require_once DIR_INCLUDES."google-api/contrib/Google_PredictionService.php";

const CLIENT_ID = '************-ct5n3vqgmqshj05pqv8mjs5g787jcrp7.apps.googleusercontent.com';
const SERVICE_ACCOUNT_NAME = '<EMAIL>';

// ---------CLIENT SIDE REQUEST MET FORM -------------
/*
$client = new Google_Client();
$client->setApplicationName("Google Calendar PHP Starter Application");
$client->setUseObjects(true);
$client->setScopes(
	array(
		'https://www.googleapis.com/auth/calendar',
		'https://www.googleapis.com/auth/calendar.readonly',
	)
);


if (isset($_REQUEST['logout'])) {
  unset($_SESSION['access_token']);
}

if (isset($_GET['code'])) {
  $client->authenticate();
  $_SESSION['access_token'] = $client->getAccessToken();
  $redirect = 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['PHP_SELF'];
  header('Location: ' . filter_var($redirect, FILTER_SANITIZE_URL));
}

if (isset($_SESSION['access_token'])) {
  $client->setAccessToken($_SESSION['access_token']);
}

if ($client->getAccessToken()) {
  $status = "Logged In";
} 
else {
  $status = "Logged Out";
  $authUrl = $client->createAuthUrl();
}

if(isset($authUrl)) {
  print "<a class='login' href='$authUrl'>Login</a>";
  $result = "";
  print("</div>");
} 
else {
  print "<a class='login' href='?logout'>Logout</a>";
  $cal = new Google_CalendarService($client);
  $calList = $cal->calendarList->listCalendarList();
  print "<h1>Calendar List</h1><pre>" . print_r($calList, true) . "</pre>";
}
*/
// ---------SERVER SIDE REQUEST MET FORM -------------


$client = new Google_Client();
$client->setApplicationName("Google Prediction Sample");

// Set your cached access token. Remember to replace $_SESSION with a
// real database or memcached.

if (isset($_SESSION['token'])) {
  $client->setAccessToken($_SESSION['token']);
}

// Load the key in PKCS 12 format (you need to download this from the
// Google API Console when the service account was created.
$key = file_get_contents(DIR_INCLUDES.'google-api/a3274edba3f2a1188e626171e87b9c1a6093ab76-privatekey.p12');
$client->setAssertionCredentials(new Google_AssertionCredentials(
  SERVICE_ACCOUNT_NAME,
  array(
  	'https://www.googleapis.com/auth/calendar',
  	'https://www.googleapis.com/auth/calendar.readonly',
  ),
  $key)
);

$client->setClientId(CLIENT_ID);

$cal = new Google_CalendarService($client);
$calList = $cal->calendarList->listCalendarList();
print "<h1>Calendar List</h1><pre>" . print_r($calList, true) . "</pre>";

// We're not done yet. Remember to update the cached access token.
// Remember to replace $_SESSION with a real database or memcached.
if ($client->getAccessToken()) {
  $_SESSION['token'] = $client->getAccessToken();
}


?>