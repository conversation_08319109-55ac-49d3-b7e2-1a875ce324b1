<?php
  /**
   * LET OP:
   * Er worden geen nieuwe functies in dit php bestand aangemaakt.
   * Heb je generieke functies nodig, plaats deze in:
   * - Een Helper class
   * - In de domain structuur
   *
   */

  //-----------------------------FORM FUNCTIONS-----------------------------
  function writeIfSet($name): void {
    if (isset($_POST[$name])) {
      echo $_POST[$name];
    }
    elseif (isset($_GET[$name])) {
      echo $_GET[$name];
    }
    elseif (isset($_SESSION[$name])) {
      echo $_SESSION[$name];
    }
  }

  function writeIfSetVal($name, $value): string {
    if (isset($name->{$value})) {
      return $name->{$value};
    }
    elseif (isset($name[$value])) {
      return $name[$value];
    }
    return "";
  }

  function writeIfChecked($name, $value): void {
    if (isset($_POST[$name]) && $_POST[$name] == $value) {
      echo ' checked="checked" ';
    }
  }

  function writeIfCheckedVal($keyval, $value): void {
    if ($keyval == $value) {
      echo ' checked="checked" ';
    }
  }

  function writeIfSelected($name, $value): void {
    if (isset($_POST[$name]) && $_POST[$name] == $value) {
      echo ' selected ="selected " ';
    }
  }

  function hasError($errors, $item, $inputerror_class = "inputerror") {
    return isset($errors[$item]) ? $inputerror_class : '';
  }

  function getOptionVal($from, $to, $value, $sum = 1, $extra = ''): string {
    $str = "";
    for ($i = $from; $i <= $to; $i = $i + $sum) {
      if ($value == $i && $value !== false) {
        $str .= '<option value="' . $i . '" selected="selected" >' . $i . ' ' . $extra . '</option>';
      }
      else {
        $str .= '<option value="' . $i . '" >' . $i . ' ' . $extra . '</option>';
      }
    }

    return $str;
  }

  function getOption($from, $to, $name): string {
    $str = '';
    for ($i = $from; $i <= $to; $i++) {
      if (isset($_POST[$name]) && $_POST[$name] == $i) {
        $str .= '<option value="' . $i . '" selected="selected" >' . $i . '</option>';
      }
      else {
        $str .= '<option value="' . $i . '">' . $i . '</option>';
      }
    }

    return $str;
  }

  function writeOptionVal($from, $to, $value): void {
    echo getOptionVal($from, $to, $value);
  }

  function writeIfSelectedVal($keyval, $value): void {
    if ($keyval == $value) {
      echo " selected ";
    }
  }

  function writeMonthOptionVal($val): void {
    $monthStr = [
      "Januari",
      "Februari",
      "Maart",
      "April",
      "Mei",
      "Juni",
      "Juli",
      "Augustus",
      "September",
      "Oktober",
      "November",
      "December",
    ];
    for ($i = 0; $i < 12; $i++) {
      if (isset($val) && $val == $i + 1) {
        echo "<option value='" . ($i + 1) . "' selected >$monthStr[$i]</option>";
      }
      else {
        echo "<option value='" . ($i + 1) . "'>$monthStr[$i]</option>";
      }
    }
  }

  /**
   * @param        $inputval : name of input
   * @param string $initialvalue
   * @param bool $disable
   * @param string $extraclass
   * @param string $title
   * @return string
   */
  function getDateSelector($inputval, $initialvalue = '', $disable = false, $extraclass = "", $title = "dd-mm-yyyy"): string {
    return '<input type="text" class="datepicker ' . $extraclass . '" id="' . $inputval . '" name="' . $inputval . '" value="' . $initialvalue . '" size="12" maxlength="10" ' . ($disable ? 'readonly="readonly"' : "") . ' title="' . $title . '" placeholder="' . $title . '" autocomplete="off"/>';
  }

  //----------------------------GLOBAL HELPER FUNCTIONS----------------------------
  /**
   * Opbouwen url. Voeg de request parameters toe welke in de add array staan.
   * @param array $add request parameters welke we gaan toevoegen.
   * @return string
   * @throws Exception
   */
  function reconstructQueryAdd(array $add = []): string {
    $urltext = '';
    $new_pageId = '';
    foreach ($add as $k => $a) {
      //deze niet expliciet toevoegen als request variabelen. Zitten immers al in url voor het vraagteken
      if ($a == 'pageId' || $a == 'lang' || $a == 'rest') {
        if ($a == 'pageId' && isset($_GET['pageId'])) {
          $new_pageId = $_GET['pageId'];
        }
        unset($add[$k]);
      }
      if ($k === 'pageId' && $a !== 'pageId') {
        $new_pageId = $a;
      }
    }
    if ($new_pageId != '') {
      $urltext .= PageMap::getUrl(PageMap::getInstance()->getPageId($new_pageId));
      if (!str_contains($urltext, '?')) { //geen vraagteken, dan toevoegen
        $urltext .= "?";
      }
      elseif (!str_ends_with($urltext, "&")) {
        $urltext .= "&";
      }
    }
    else {
      $urltext = $_SERVER['REQUEST_URI'];
      if (($length = strpos($urltext, '?')) !== false) { //verwijder alles vanaf het vraagteken
        $urltext = substr($urltext, 0, $length);
      }
      $urltext .= "?";
    }

    $additional_params = [];
    //add the assigned $addvalue of the $addkey
    foreach ($add as $addkey => $addvalue) {
      if (is_string($addkey) && $addkey !== $addvalue) { //only add: key => value
        if (is_array($addvalue)) {
          foreach ($addvalue as $av) {
            $additional_params[] = $addkey . "[]=" . urlencode($av);
          }
        }
        else {
          $additional_params[] = $addkey . "=" . urlencode($addvalue ?? '');
        }
        unset($add[$addkey]);
      }
    }

    //add $_GET params
    foreach ($_GET as $key => $value) {
      if (!in_array($key, $add)) continue;
      if (is_array($value)) {
        foreach ($value as $v) {
          if (!is_string($v)) continue;
          $additional_params[] = $key . "[]=" . urlencode($v);
        }
      }
      else {
        $additional_params[] = "$key=" . urlencode($value);
      }
    }
    $urltext .= implode("&", $additional_params);
    if (!str_ends_with($urltext, "&") && !str_ends_with($urltext, "?")) {
      $urltext .= "&";
    }

    return $urltext;
  }

  /**
   * Opbouwen url. Verwijder de request parameters toe welke in de ignore array staan.
   * @param array $ignore request parameters te negeren
   * @return string
   * @throws Exception
   */
  function reconstructQuery(array $ignore = []): string {

    $urltext = '';
    if (($length = strpos($_SERVER['REQUEST_URI'], '?')) !== false) {
      $urltext .= substr($_SERVER['REQUEST_URI'], 0, $length);
    }
    else {
      $urltext .= substr($_SERVER['REQUEST_URI'], 0);
    }

    if (!in_array('pageId', $ignore)) { //als niet expliciet aangeven is de pageId te negeren, dan url opbouwen met PageMap
      global $pageId;
      $urltext = PageMap::getUrl($pageId);
      //zijn er al standaard url parameters? Dan zijn ze al toegevoegd en ignoren maar.
      $pmitem = PageMap::getInstance()->getPageMapItem($pageId);
      $ignore = array_merge($ignore, $pmitem->getParams());
    }
    if ((strpos($urltext, '?')) === false) { // geen vraagteken dan toevoegen
      $urltext .= "?";
    }
    elseif (strrpos($urltext, '&') !== 0 && strrpos($urltext, '?') !== 0) { //laatse karakter niet & of ?  dan toevoegen
      $urltext .= "&";
    }

    $ignore = array_merge($ignore, ['pageId', 'lang', 'rest']);
    foreach ($_GET as $key => $value) {
      if (in_array($key, $ignore)) continue;
      if (is_array($value)) {
        foreach ($value as $v) {
          if (!is_string($v)) continue;
          $urltext .= $key . "[]=" . urlencode($v) . "&";
        }
      }
      else {
        $urltext .= "$key=" . urlencode($value) . "&";
      }

    }

    return $urltext;
  }

  /** -----------------------------------------
   * Good stuff for posting etc.
   * - cleanPostVar: for putting your data on an object
   * - escapeForDB: escaping characters for writing to database
   * - escapeForInput: escape characters for input boxes
   */
  function cleanPostVar($id) {
    if (!isset($_POST[$id])) {
      return '';
    }
    $result = trim(strip_tags($_POST[$id]));
    return $result;
  }

  function cleanPostVarByText($text) {
    $result = trim(strip_tags($text));
    return $result;
  }

  /**
   * Escaping single quotes and converting html-codes of quotes for writing to db
   * @param string $text
   * @return string
   */
  function escapeForDB($text) {
    return DbHelper::escape($text);
  }

  /**
   * Displaying for input fields, both double and single quotes escaped (encodiging is default charset, probably utf-8)
   * @param string $text
   * @return string
   */
  function escapeForInput($text) {
    return htmlspecialchars((string)$text, ENT_HTML5 | ENT_QUOTES); //niet alles naar entities, maar alleen broodnodige.
//    return htmlentities($text, ENT_QUOTES);
//    $text = str_replace('&', "&amp;", $text);
//    $text = str_replace('"', "&quot;", $text);
//    return str_replace("'", "&lsquo;", $text);
  }

  /**
   * @param $text
   * @return string
   */
  function escapeForPDF($text) {
    return html_entity_decode(trim(strip_tags($text)), ENT_QUOTES);
  }

  /**
   * @param string $text
   * @return string
   */
  function escapeForXML($text) {
    return htmlspecialchars($text, ENT_XML1, 'UTF-8');
  }

  /**
   * Escape for csv export (for instance with arrayToCSV)
   * @param string $text
   * @return string
   */
  function escapeForCsv($text) {
    return str_replace(["\t", "\n", "\r"], " ", $text);
  }

  /**
   * Escape for javascript
   * @param string $text
   * @return string
   */
  function escapeForJS($text) {
    if (empty($text)) return "";
    //return str_replace(array("'",'"'), array("\'",'&quot;'), $text);
    return str_replace(["'", '"'], ["\'", '\"'], $text);
  }

  /**
   * Escape id's with [] to be used easy with JQuery selectors
   * @param string $text
   * @return string
   */
  function escapeIdForJQ($text) {
    $text = preg_replace('/\[/i', '\\\\\\[', $text);
    $text = preg_replace('/\]/i', '\\\\\\]', $text);
    return $text;
  }

  function escapeSafe($text) {
    return htmlspecialchars($text ?? "", ENT_COMPAT, 'UTF-8');
  }

  function cleanExcel($str) {
    $str = trim($str);
    $str = str_replace('""', '"', $str);
    if (str_starts_with($str, '"')) {
      $str = mb_substr($str, 1);
    }
    if (str_ends_with($str, '"')) {
      $str = mb_substr($str, 0, -1);
    }
    $str = StringHelper::uft8Encode($str);

    return $str;
  }

  /**
   * OLD STUFF:
   */
  //cleaning posted variables (tags, spaces)
  function cleanPostVariable($id) {
    if (!isset($_POST[$id])) {
      return;
    }
    $result = trim(strip_tags($_POST[$id]));
    return $result;
  }

  //displaying quotes as html, for example in input fields
  function displayAsHtml($text) {
    if (empty($text)) return "";
    $text = str_replace('"', "&quot;", $text);
    return str_replace("'", "&lsquo;", $text);
  }

  function showHelpButton($text, $caption = '', $width = ''): string {
    $str = '<span tabindex="-1" class="qtipa" title="' . preg_replace("/\r?\n/", "<br/>", escapeForInput($text)) . '" ';
    if ($caption != '') {
      $str .= 'data-caption="' . $caption . '"';
    }
    $str .= '>';
    $str .= IconHelper::getHelp();
    $str .= '</span>';

    return $str;
  }

  /**
   * Show alert icon with hover popper
   * @param $text
   * @param $caption
   * @param $extraclass
   * @return string
   */
  function showAlertButton($text, $caption = '', $extraclass = ""): string {
    $str = '<span tabindex="-1" class="qtipa gsd-alert-btn ' . $extraclass . '" title="' . preg_replace("/\r?\n/", "<br/>", escapeForInput($text)) . '" ';
    if ($caption != '') {
      $str .= 'data-caption="' . $caption . '"';
    }
    $str .= '>';
    $str .= IconHelper::getAlert();
    $str .= '</span>';

    return $str;
  }

  /**
   * Show info icon with hover popper
   * @param $text
   * @param $caption
   * @param $extraclass
   * @return string
   */
  function showInfoButton($text, $caption = '', $extraclass = ""): string {
    $str = '<span tabindex="-1" class="qtipa gsd-info-btn ' . $extraclass . '" title="' . preg_replace("/\r?\n/", "<br/>", escapeForInput($text)) . '" ';
    if ($caption != '') {
      $str .= 'data-caption="' . $caption . '"';
    }
    $str .= '>';
    $str .= IconHelper::getInfo();
    $str .= '</span>';

    return $str;
  }

  /**
   * This function wil load the current project language file OR fallsback to the default files.
   * @param string $lang
   * @param string $file
   * @param string $folder
   * @return false|string
   * @throws Exception
   */
  function getLanguageFile(string $lang, string $file = '', string $folder = '/') {
    if (defined('DIR_LANGUAGES_PROJECT') && is_file(DIR_LANGUAGES_PROJECT . $lang . $folder . $file)) {
      return file_get_contents(DIR_LANGUAGES_PROJECT . $lang . $folder . $file);
    }
    elseif (Config::isdefined("GSDFW_PROJECT_EXTENDS") && Config::get("GSDFW_PROJECT_EXTENDS") != "" && is_file(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/languages/' . $lang . $folder . $file)) {
      return file_get_contents(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/languages/' . $lang . $folder . $file);
    }
    elseif (is_file(DIR_LANGUAGES . $lang . $folder . $file)) {
      return file_get_contents(DIR_LANGUAGES . $lang . $folder . $file);
    }
    return false;
  }

  /**
   * @param $price
   * @param string $sep (optional) default: ","
   * @param string $th
   * @param $dec
   * @return string
   */
  function getLocalePrice($price, string $sep = ',', string $th = '', $dec = 2): string {
    if (empty($price)) $price = 0.0;
    return number_format($price, $dec, $sep, $th);
  }

  function getFloatPrice($price): float {
    if (empty($price)) return 0.0;
    return (float)(str_replace(',', '.', $price));
  }

  /**
   * Print value. Use dump() or dumpe() preferably
   * @param $val
   * @return void
   */
  function pd($val): void {
    echo '<pre>' . print_r($val, true) . "</pre>\n";
  }

  /**
   * @param $str
   * @return array
   */
  function getCharcodes($str) {
    $expl = [];
    foreach (str_split($str) as $char) {
      $expl[] = ord($char);
    }
    return $expl;
  }

  /**
   * Functions as substr, strtolower, str_split are not utf-8 safe. This function is.
   * @param string $str
   * @return string
   */
  function strtolowersafe(string $str): string {
    return mb_strtolower($str);
  }

  function findEmailInText($text) {
    preg_match_all("/([\w\d\.\-\_]+)@([\w\d\.\_\-]+)/mi", $text, $matches);
    $emails = [];
    foreach ($matches[0] as $email) {
      if (ValidationHelper::isEmail($email)) {
        $emails[$email] = $email;
      }
    }

    return $emails;
  }

  function strip_tags_allowed(?string $in_text, $tags_allowed = "b|i|u|br") {
    if(empty($in_text)) return "";
    return preg_replace('#</?(?!(' . $tags_allowed . '))\b([^><]*>)#sim', "", $in_text);
  }

  /**
   * Run from commandline?
   * @return bool
   */
  function isCli(): bool {
    if (php_sapi_name() == 'cli' && empty($_SERVER['REMOTE_ADDR'])) {
      return true;
    }
    return false;
  }

  function zerofill($num, $zerofill = 2, $char = '0'): string {
    return str_pad($num, $zerofill, $char, STR_PAD_LEFT);
  }

  /**
   * Highlight a certain string in a string.
   * Often used for a search result page.
   * @param string|null $str
   * @param string|null $higlight
   * @return string
   */
  function highlight(?string $str, ?string $higlight): string {
    if (empty($str)) return "";
    if (empty($higlight)) return $str;
    $strAr = preg_split("/" . $higlight . "+/i", $str);
    if (!is_array($strAr)) return $str;
    return implode('<span class="highlight">' . $higlight . '</span>', $strAr);
  }

  /**
   * Prints out the errors in the $errors array
   *
   * @param string[] $errors (required)
   * @param bool|false $highlightinputs (optional) highlights input
   * @param bool|false $hightlight_bootstrap (optional) highlights form-group bootstrap style
   * @param bool|false $scrollto (optional) scrollt to error
   */
  function writeErrors($errors, $highlightinputs = false, $hightlight_bootstrap = false, $scrollto = false, $general_message = false): void {
    if (empty($errors)) return;

    $out = '<div class="alert alert-danger">';
    if ($general_message === false) {
      $out .= __('Er zijn foutmeldingen opgetreden, controleer uw invoer');
    }
    else {
      $out .= $general_message;
    }

    $out .= ':<ul>';
    foreach ($errors as $error) {
      if (is_array($error) || $error == '' || $error == '1') {
        continue;
      }
      $out .= '<li>' . $error . '</li>';
    }
    $out .= '</ul>';
    $out .= '</div>';


    if ($highlightinputs) {
      $errs = [];
      foreach ($errors as $key => $error) {
        $errs[] = '#' . $key;
      }

      $out .= '<script type="text/javascript">';
      $out .= '$(document).ready(function() {';
      if (!$hightlight_bootstrap) {
        $out .= '$("';
        $out .= implode(',', $errs);
        $out .= '").addClass("inputerror");';
      }
      else {
        $out .= '$("';
        $out .= implode(',', $errs);
        $out .= '").closest(".form-group").addClass("has-error");';
      }

      if ($scrollto) {
        $out .= '$("html, body").animate({';
        $out .= 'scrollTop: $(".alert").offset().top';
        $out .= '}, 500);';
      }

      $out .= '});';
      $out .= '</script>';
    }

    echo $out;

  }

  function writeWarnings($warnings, $highlightinputs = false): void {
    if (empty($warnings)) return;
    $out = '<div class="alert alert-warning">';
    $out .= '<ul>';
    foreach ($warnings as $warning) {
      if (is_array($warning) || $warning == '' || $warning == '1') {
        continue;
      }
      $out .= '<li>' . $warning . '</li>';
    }
    $out .= '</ul>';
    $out .= '</div>';


    if ($highlightinputs) {
      $warns = [];
      foreach ($warnings as $key => $warning) {
        $warns[] = '#' . $key;
      }

      $out .= '<script type="text/javascript">';
      $out .= '$(document).ready(function() {';
      $out .= '$("';
      $out .= implode(',', $warns);
      $out .= '").addClass("inputerror");';
      $out .= '});';
      $out .= '</script>';
    }

    echo $out;

  }

  function writeTime($name, $id, $value = '', $class = '', $style = ''): string {
    $out = '<select name="' . $name . '" id="' . $id . '"';
    if ($class != "") {
      $out .= ' class="' . $class . '"';
    }
    if ($style != "") {
      $out .= ' style="' . $style . '"';
    }
    $out .= '>';

    //per kwartier
    $valueSeconds = DateTimeHelper::convertTimestringToSeconds($value);
    for ($i = 0; $i < 24 * 4; $i++) {
      $optionValue = DateTimeHelper::convertHoursToTimestring(($i / 4));

      $out .= '<option value="' . $optionValue . '"';
      if ($value != '' && $valueSeconds == DateTimeHelper::convertTimestringToSeconds($optionValue)) {
        $out .= ' selected';
      }
      $out .= '>' . $optionValue . '</option>';
    }
    $out .= '</select>';

    return $out;
  }

  /**
   * @param string $key (required)
   * @param array $oblidged (required)
   */
  function writeOblidged($key, $oblidged): void {
    if ($oblidged && is_array($oblidged) && in_array($key, $oblidged)) {
      echo '<span class="input-group-addon asterisk">*</span>';
    }
  }

  function isOblidged($key, $oblidged): bool {
    if ($oblidged && is_array($oblidged) && in_array($key, $oblidged)) {
      return true;
    }
    return false;
  }

  function process_text($tekst): string {
    if (empty($tekst)) return "";
    $regex = "/\[fa=(.*)\]/";
    $processed_tekst = preg_replace_callback($regex, function ($m) {
      return '<i class="fa ' . $m[1] . '"></i>';
    }, $tekst);

    return $processed_tekst;
  }

  function showdate($date, $kort = false, $mobile = false): string {
    if (is_numeric($date)) {
      $date_timestamp = $date;
    }
    else {
      $date_timestamp = strtotime($date);
    }

    $out = DateTimeHelper::strftime("%a", $date_timestamp) . " " . date('j', $date_timestamp);
    if ($mobile) {
      $out .= "<br/>";
    }
    else {
      $out .= " ";
    }
    if (!$kort) {
      $out .= DateTimeHelper::strftime("%B", $date_timestamp);
    }
    else {
      $out .= DateTimeHelper::strftime("%b", $date_timestamp);
    }

    return $out;
  }

  /**
   * Automatically generate a version number for asset caching, based on file modified time
   *
   * @param $file_location  string  Full absolute path of the filename (use DIR_ROOT OR DIR_INCLUDES)
   * @return bool|int
   */
  function get_asset_version($file_location) {
    return filemtime($file_location);
  }

  if (!function_exists('apache_response_headers')) {
    function apache_response_headers() {
      $arh = [];
      $headers = headers_list();
      foreach ($headers as $header) {
        $header = explode(":", $header);
        $arh[array_shift($header)] = trim(implode(":", $header));
      }
      return $arh;
    }
  }

  if (!function_exists('apache_request_headers')) {
    function apache_request_headers() {
      $arh = [];
      $rx_http = '/\AHTTP_/';
      foreach ($_SERVER as $key => $val) {
        if (preg_match($rx_http, $key)) {
          $arh_key = preg_replace($rx_http, '', $key);
          $rx_matches = [];
          // do some nasty string manipulations to restore the original letter case
          // this should work in most cases
          $rx_matches = explode('_', $arh_key);
          if (count($rx_matches) > 0 and strlen($arh_key) > 2) {
            foreach ($rx_matches as $ak_key => $ak_val) $rx_matches[$ak_key] = ucfirst($ak_val);
            $arh_key = implode('-', $rx_matches);
          }
          $arh[$arh_key] = $val;
        }
      }
      return ($arh);
    }
  }

  /**
   * Ophalen stylesheets voor ckeditor content
   * @param bool|Site $currentsite
   * @return string
   * @throws Exception
   */
  function getCkeditorStylesheets(Site|bool $currentsite = false): string {
    $stylesheets = [];
    if (Config::isdefined('PAGE_CKEDITOR_CONTENT_CSS') && count(Config::get('PAGE_CKEDITOR_CONTENT_CSS')) > 0) {
      //pak uit config
      foreach (Config::get('PAGE_CKEDITOR_CONTENT_CSS') as $stylec) {
        $stylesheets[] = $stylec;
      }
    }
    elseif ($currentsite && file_exists($currentsite->getTemplateDir() . 'style/style.css')) {
      //geen config, gebruik standaard van deze site template als bestand bestaat
      $stylesheets[] = $currentsite->getTemplateUrl() . 'style/style.css';
    }
    //vaste css voor ckeditor content
    $stylesheets[] = '/gsdfw/includes/jsscripts/ckeditor4.css';
    if ($currentsite && file_exists($currentsite->getTemplateDir() . 'style/ckeditor_style.css')) { //ckeditor css op project niveau
      $stylesheets[] = $currentsite->getTemplateUrl() . "style/ckeditor_style.css";
    }
    if (count($stylesheets) == 1) { //alleen ckeditor4.css is geladen, dan laad ook maar ckeditor4 contents.css, zodat het er netjes uitziet
      $stylesheets[] = '/gsdfw/includes/ckeditor4/contents.css';
    }
    if (count($stylesheets) > 0) {
      return "'" . implode("','", $stylesheets) . "'";
    }
    return '';
  }


  /** ---- DATETIME SHORTCUTS ---- */

  /**
   * Parse a dutch formated date or datetime (d-m-Y) into a mysql friendly datetime (Y-m-d)
   * @param       $value
   * @param false $ignoretime
   * @return string
   */
  function getTSFromStr($value, bool $ignoretime = false): string {
    $value = trim($value);
    if (empty($value)) return "";

    $vals = explode("-", $value);
    if (count($vals) < 3) {
      return "";
    }

    $date = sprintf("%02d", $vals[2]);
    $month = sprintf("%02d", $vals[1]);
    $year = sprintf("%04d", substr($vals[0], 0, 4));
    if (!DateTimeHelper::isDate($value)) {
      $date = sprintf("%02d", $vals[0]);
      $month = sprintf("%02d", $vals[1]);
      $year = sprintf("%04d", substr($vals[2], 0, 4));
    }
    if ($ignoretime) {
      return $year . "-" . $month . "-" . $date;
    }

    return $year . "-" . $month . "-" . $date . substr($vals[2], 4);

  }

  /**
   * Print windows/linux safe datetime. Also makes nice german datetime
   *
   * @param      $format
   * @param null $timestamp
   * @return string
   */
  function strftimesafe($format, $timestamp = null) {
    if ($timestamp == null) {
      $timestamp = time();
    }
    if ($format == "%e %B %Y" && isset($_SESSION['country']) && $_SESSION['country'] == 'de') {
      $format = "%e. %B %Y";
    }
    if (strtoupper(substr(PHP_OS, 0, 3)) == 'WIN') {
      $format = preg_replace('#(?<!%)((?:%%)*)%e#', '\1%#d', $format);
    }

    return DateTimeHelper::strftime($format, $timestamp);
  }
