<?php
session_start();
require_once("twitteroauth/twitteroauth.php"); //Path to twitteroauth library
 
$twitteruser = "PIDZZorg";
$notweets = 30;
$consumerkey = "pRK1qkFmRKcB3cXMIW4Ng";
$consumersecret = "M5xniU1z4wdpelSbE092W8gO9mykJBOQd8oJUlvvttI";
$accesstoken = "1701960642-tk8mvVJHBMOigLY36JJ04aXVcnBUy2hU1qbeg5o";
$accesstokensecret = "uDiGdJrwmgdjX0M83VE8s34TTlCkF6cKyphuqSTLFM";
 
function getConnectionWithAccessToken($cons_key, $cons_secret, $oauth_token, $oauth_token_secret) {
  $connection = new TwitterOAuth($cons_key, $cons_secret, $oauth_token, $oauth_token_secret);
  return $connection;
}
 
$connection = getConnectionWithAccessToken($consumerkey, $consumersecret, $accesstoken, $accesstokensecret);
 
$tweets = $connection->get("https://api.twitter.com/1.1/statuses/user_timeline.json?screen_name=".$twitteruser."&count=".$notweets);
 
echo json_encode($tweets);
?>