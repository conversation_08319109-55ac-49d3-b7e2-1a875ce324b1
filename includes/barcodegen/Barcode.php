<?php

  use BarcodeBakery\Barcode\BCGdatamatrix;
  use BarcodeBakery\Common\BCGColor;
  use BarcodeBakery\Common\BCGDrawing;
  use BarcodeBakery\Common\BCGFontFile;
  use BarcodeBakery\Common\BCGLabel;

  /**
   * Create a diversity of barcodes. https://www.barcodebakery.com
   */
  class Barcode {

    var $filepath = false; //path to save file

    /**
     * Barcode constructor.
     * @param bool $filepath : path to save file. if not set the image wil be send to output.
     */
    public function __construct($filepath = false) {
      $this->filepath = $filepath;
    }

    /**
     * Generate datamatrix png
     * @param $text : text to embed
     * @return void
     * @throws \BarcodeBakery\Common\BCGArgumentException
     * @throws \BarcodeBakery\Common\BCGDrawException
     * @throws \BarcodeBakery\Common\BCGParseException
     */
    public function generateDatamatrix($text) {

      function __autoloaderBCG($classname) {
        //autoload deze classes
        $exp = explode("\\", $classname);
        $classname = $exp[count($exp) - 1];
        require_once('class/' . $classname . '.php');
      }

      spl_autoload_register('__autoloaderBCG');

      require_once('class/Drawer/BCGDraw.php');
      require_once('class/Drawer/BCGDrawJPG.php');
      require_once('class/Drawer/BCGDrawPNG.php');

      // Label, this part is optional
      $label = new BCGLabel();
      $label->setFont(new BCGFontFile(DIR_INCLUDES . 'pdf/fonts/unifont/arial.ttf', 8));
      $label->setPosition(BCGLabel::POSITION_BOTTOM);
      $label->setAlignment(BCGLabel::ALIGN_CENTER);
      $label->setText($text);

      $code = new BCGdatamatrix();
      $code->setScale(20);
      $code->setSize(BCGdatamatrix::DATAMATRIX_SIZE_SQUARE);
      $code->setForegroundColor(new BCGColor('black')); // Color of bars
      $code->setBackgroundColor(new BCGColor('white')); // Color of spaces
      $code->parse($text);

      if ($this->filepath !== false) {
        $drawing = new BCGDrawing(null, new BCGColor('white'));
        $drawing->setBarcode($code);
        $drawing->finish(BCGDrawing::IMG_FORMAT_PNG, $this->filepath);
      }
      else {
        header('Content-Type: image/png');
        header('Content-Disposition: inline; filename="barcode.png"');
        $drawing = new BCGDrawing(null, new BCGColor('white'));
        $drawing->setBarcode($code);
        $drawing->finish(BCGDrawing::IMG_FORMAT_PNG);
      }
    }

  }