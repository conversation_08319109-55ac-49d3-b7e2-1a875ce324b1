language: node_js
dist: trusty
node_js: stable

cache:
  bundler: true
  directories:
    - node_modules

addons:
  firefox: "51.0" # testem doesn't work with Firefox 52, revisit and set to latest if possible
  google-chrome: latest

before_script:
  - gulp

script:
  - xvfb-run testem ci --reporter dot

deploy:
  api_key:
    secure: TaF0rF0+n/t3jchds6RSahdIwV+VozFEzGXdnwKzX8TnW2afGU7hh50BJHdQA6/mm0VbgxwAfIDMYU2luLwQn8CJCcJYp7g3+g8xsH15q8ny2AUmIkQQ2sWY43Vk1yMHIwzuAlHy5Pv5qKk8cYRVY5fwaCl/4KfvpIn4F2/DjwU=
  email: <EMAIL>
  provider: npm

notifications:
  email:
    on_success: never
