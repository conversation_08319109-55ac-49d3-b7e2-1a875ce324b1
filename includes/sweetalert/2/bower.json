{"name": "sweetalert2", "homepage": "https://limonte.github.io/sweetalert2/", "authors": ["Limon Monte <<EMAIL>> (https://limonte.github.io)", "<PERSON> (https://github.com/birjolaxew)", "<PERSON>-<PERSON> <<EMAIL>> (https://github.com/toverux)", "<PERSON> <<EMAIL>> (http://samturrell.co.uk)", "<PERSON> <<EMAIL>> (http://tristanedwards.me)", "<PERSON> <<EMAIL>> (https://leonardocorrea.com/)", "<PERSON> (https://github.com/acupajoe)"], "dependencies": {"es6-promise": "*"}, "ignore": ["assets", "config", "test", ".babelrc", ".editorcofig", ".giti<PERSON>re", ".sass-lint.yml", ".travis.yml", "contributing.md", "gulpfile.js", "package.json"], "description": "An awesome and accessible (WAI-ARIA) replacement for JavaScript's popup boxes, supported fork of sweetalert", "main": ["dist/sweetalert2.js", "src/sweetalert2.scss", "dist/sweetalert2.css"], "keywords": ["alert", "modal"], "repository": {"type": "git", "url": "**************:limonte/sweetalert2.git"}, "license": "MIT"}