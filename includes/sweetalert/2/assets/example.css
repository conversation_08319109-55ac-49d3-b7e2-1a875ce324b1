@import url("https://fonts.googleapis.com/css?family=Open+Sans:400,600,700,300");
@import url("https://fonts.googleapis.com/css?family=Open+Sans+Condensed:700");
html,
body {
  margin: 0; }

body {
  background-color: #f2f4f6;
  font-family: 'Open Sans', sans-serif;
  text-align: center; }

header h1 {
  font-family: 'Courgette', serif;
  font-size: 60px;
  color: #a8a8a8;
  white-space: nowrap;
  margin: 50px auto; }
  header h1 span {
    margin: 0 10px;
    color: #f77; }
  @media all and (max-width: 440px) {
    header h1 {
      font-size: 50px; } }

header h2 {
  font-size: 20px;
  color: #a9b2bc;
  line-height: 25px;
  text-transform: uppercase;
  font-weight: 300;
  text-align: center;
  display: block; }
  header h2 a {
    color: #3085d6; }

.swal2-alert h2 {
  font-family: 'Open Sans', sans-serif;
  font-weight: 600; }

.modal-types td {
  vertical-align: middle;
  text-align: center;
  width: 33%; }

.modal-types .swal2-icon,
.modal-types button {
  display: inline-block;
  margin: 0; }

.modal-types .swal2-success::before, .modal-types .swal2-success::after {
  background: transparent !important; }

.modal-input-types tr td:first-child {
  padding-top: 45px; }

.modal-input-types td {
  vertical-align: middle;
  text-align: center;
  width: 33%; }

h3 {
  font-size: 28px;
  color: #c7ccd1;
  text-transform: uppercase;
  font-family: 'Open Sans Condensed', sans-serif;
  margin-top: 100px;
  text-align: center;
  position: relative; }
  h3.download-section {
    margin-top: 50px;
    padding-top: 40px; }
  h3::after {
    content: '';
    background-color: #e2e5e8;
    height: 4px;
    width: 700px;
    left: 50%;
    margin-left: -350px;
    position: absolute;
    margin-top: -50px;
    border-radius: 2px; }
    @media all and (max-width: 740px) {
      h3::after {
        width: auto;
        left: 20px;
        right: 20px;
        margin-left: 0; } }

a {
  text-decoration: none;
  color: #abcada;
  font-weight: 600;
  cursor: pointer; }
  a:hover {
    text-decoration: underline; }
  a.github {
    color: #333333; }

p {
  max-width: 826px;
  margin: 30px auto;
  font-size: 17px;
  font-weight: 300;
  color: #848d94;
  line-height: 25px;
  text-align: left; }
  p.center {
    text-align: center; }
  p strong {
    color: #848d94;
    font-weight: 600; }
  @media all and (max-width: 750px) {
    p {
      text-align: center;
      padding: 0 10px; } }

button,
.button {
  background-color: #3085d6;
  color: #fff;
  border: 0;
  box-shadow: none;
  font-size: 17px;
  font-weight: 500;
  border-radius: 3px;
  padding: 15px 35px;
  margin: 26px 5px 0;
  cursor: pointer;
  white-space: nowrap; }
  button:hover,
  .button:hover {
    background-color: #297dce; }
  button:active,
  .button:active {
    background-color: #236bb0; }
  button.cancel,
  .button.cancel {
    background-color: #d0d0d0; }
    button.cancel:hover,
    .button.cancel:hover {
      background-color: #c8c8c8; }
    button.cancel:active,
    .button.cancel:active {
      background-color: #b7b7b7; }

.top-right-button {
  position: fixed;
  right: 80px;
  width: 170px;
  height: 50px;
  line-height: 50px;
  margin: 0;
  z-index: 3;
  text-transform: uppercase;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  cursor: pointer; }
  .top-right-button:hover {
    text-decoration: none; }
  .top-right-button.download {
    top: 30px;
    color: #abcada; }
  .top-right-button.cdn {
    top: 90px;
    color: #eb4c36; }
  .top-right-button.donate {
    top: 210px;
    color: #3085d6;
    font-weight: 600; }
    .top-right-button.donate img {
      vertical-align: -9px; }
  @media all and (max-width: 1278px) {
    .top-right-button {
      display: none; } }

.center-container {
  max-width: 725px;
  margin: 70px auto; }

pre {
  background-color: #49483e;
  color: #f7f7f7;
  padding: 10px;
  border-radius: 5px;
  text-align: left;
  font-size: 14px;
  overflow: hidden; }
  pre .str {
    color: #e6db74; }
  pre .func {
    color: #66d9ef; }
  pre .val {
    color: #a381ff; }
  pre .tag {
    color: #e92772; }
  pre .attr {
    color: #a6e22d; }
  pre .comment {
    color: #8d98a5; }

.showcase {
  background-color: #e2e5e8;
  padding: 20px;
  display: inline-block;
  width: 383px;
  vertical-align: top;
  position: relative; }
  @media all and (max-width: 865px) {
    .showcase {
      margin: 5px auto;
      padding: 35px 10px; } }
  @media all and (max-width: 440px) {
    .showcase {
      width: auto; } }
  .showcase h4 {
    font-size: 16px;
    color: #b3b3b3;
    line-height: 22px;
    margin: 0 auto;
    font-weight: 800; }
  .showcase.sweet h4 {
    font-family: 'Courgette', serif;
    font-size: 24px;
    color: #a8a8a8; }
    .showcase.sweet h4 span {
      margin: 0 4px;
      color: #f77; }
  .showcase button {
    margin-bottom: 10px; }
  .showcase .vs-icon {
    background-image: -webkit-image-set(url("vs_icon.png") 1x, url("<EMAIL>") 2x);
    background-image: image-set(url("vs_icon.png") 1x, url("<EMAIL>") 2x);
    background-repeat: no-repeat;
    width: 69px;
    height: 69px;
    position: absolute;
    right: -34px;
    top: 60px;
    z-index: 2; }
    @media all and (max-width: 865px) {
      .showcase .vs-icon {
        margin: 5px auto;
        right: auto;
        left: 50%;
        margin-left: -35px;
        top: auto;
        bottom: -35px; } }

.examples {
  list-style-type: none;
  width: 750px;
  margin: 0 auto;
  text-align: left;
  padding-left: 0; }
  @media all and (max-width: 758px) {
    .examples {
      width: auto; } }
  .examples li {
    padding-left: 0; }
  .examples .ui,
  .examples pre {
    display: inline-block;
    vertical-align: top; }
    @media all and (max-width: 758px) {
      .examples .ui,
      .examples pre {
        display: block;
        max-width: none;
        margin: 0 auto; } }
  .examples .ui {
    width: 300px;
    text-align: center; }
    .examples .ui button {
      margin-top: 12px; }
    .examples .ui p {
      text-align: center;
      margin-bottom: 0; }
  .examples pre {
    max-width: 420px;
    margin-top: 67px; }
    @media all and (max-width: 758px) {
      .examples pre {
        margin-top: 16px !important;
        margin-bottom: 60px; } }
  .examples .warning pre {
    margin-top: 93px; }

ol {
  max-width: 725px;
  margin: 70px auto;
  list-style-position: inside;
  padding-left: 0; }
  ol li {
    color: #a7adb2; }
    ol li p {
      margin-bottom: 10px; }

table {
  width: 725px;
  font-size: 14px;
  color: #848d94;
  margin: 10px auto;
  text-align: left;
  border-collapse: collapse; }
  @media all and (max-width: 750px) {
    table {
      width: auto;
      margin: 10px auto; } }
  table th {
    background-color: #fff;
    padding: 10px 20px;
    color: #acb9be;
    font-weight: 400;
    position: relative;
    white-space: nowrap; }
    @media all and (max-width: 750px) {
      table th:nth-child(2) {
        display: none; } }
  table td {
    padding: 10px 20px;
    vertical-align: top; }
    @media all and (max-width: 750px) {
      table td:nth-child(2) {
        display: none; } }
    @media all and (max-width: 360px) {
      table td {
        padding: 10px 4px; }
        table td b {
          font-size: 13px; } }

@media all and (max-width: 750px) {
  .mobile-hidden {
    display: none; } }

.github-corner {
  position: fixed;
  top: 0;
  right: 0; }

.carbonads-wrapper {
  height: 126px; }
  .carbonads-wrapper > div {
    display: block;
    overflow: hidden;
    margin: 0 auto;
    padding: 1em;
    max-width: 300px;
    border: solid 1px #a9b2bc;
    border-radius: 2px;
    box-shadow: inset 0 1px #fff;
    text-shadow: 0 1px #fff;
    font-size: 12px;
    line-height: 1.5; }
    .carbonads-wrapper > div a {
      color: #8d98a5;
      -webkit-transition: color .15s ease-in-out;
      transition: color .15s ease-in-out; }
      .carbonads-wrapper > div a:hover {
        color: #f77; }
    .carbonads-wrapper > div span {
      position: relative;
      display: block;
      overflow: hidden; }
    .carbonads-wrapper > div .carbon-img {
      float: left;
      margin-right: 1em; }
      .carbonads-wrapper > div .carbon-img img {
        display: block; }
    .carbonads-wrapper > div .carbon-text {
      display: block;
      float: left;
      max-width: calc(100% - 130px - 1em);
      text-align: left; }
    .carbonads-wrapper > div .carbon-poweredby {
      position: absolute;
      right: 0;
      bottom: 0;
      display: block;
      font-size: 11px; }
