{"name": "sweetalert2", "version": "6.4.3", "repository": "limonte/sweetalert2", "homepage": "https://limonte.github.io/sweetalert2/", "description": "An awesome and accessible (WAI-ARIA) replacement for JavaScript's popup boxes, supported fork of sweetalert", "main": "dist/sweetalert2.js", "jsnext:main": "src/sweetalert2.js", "types": "sweetalert2.d.ts", "dependencies": {"es6-promise": "^4.0.5"}, "devDependencies": {"babel-plugin-external-helpers": "latest", "babel-plugin-transform-object-assign": "latest", "babel-preset-es2015": "latest", "gulp": "latest", "gulp-autoprefixer": "latest", "gulp-clean-css": "latest", "gulp-rename": "latest", "gulp-rollup": "latest", "gulp-sass": "latest", "gulp-sass-lint": "latest", "gulp-standard": "^8.0.0", "gulp-uglify": "latest", "rollup": "latest", "rollup-plugin-babel": "latest", "standard": "^8.0.0", "testem": "latest", "uglify-js": "latest"}, "standard": {"ignore": ["dist/"], "global": ["MutationObserver"]}, "files": ["dist", "src", "sweetalert2.d.ts"], "author": "Limon Monte <<EMAIL>> (https://limonte.github.io)", "contributors": ["<PERSON> (https://github.com/birjolaxew)", "<PERSON>-<PERSON> <<EMAIL>> (https://github.com/toverux)", "<PERSON> <<EMAIL>> (http://samturrell.co.uk)", "<PERSON> <<EMAIL>> (http://tristanedwards.me)", "<PERSON> <<EMAIL>> (https://leonardocorrea.com/)", "<PERSON> (https://github.com/acupajoe)"], "keywords": ["sweetalert", "sweetalert2", "alert", "prompt"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "testem ci", "assume-dist-unchanged": "git ls-files dist | tr '\\n' ' ' | xargs git update-index --assume-unchanged", "no-assume-dist-unchanged": "git ls-files dist | tr '\\n' ' ' | xargs git update-index --no-assume-unchanged"}, "bugs": "https://github.com/limonte/sweetalert2/issues", "license": "MIT"}