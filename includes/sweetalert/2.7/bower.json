{"name": "sweetalert2", "homepage": "https://limonte.github.io/sweetalert2/", "authors": ["Limon Monte <<EMAIL>> (https://limonte.github.io)", "<PERSON>-<PERSON> <<EMAIL>> (https://github.com/toverux)", "<PERSON> (https://github.com/birjolaxew)", "<PERSON> <<EMAIL>> (https://github.com/samturrell)", "<PERSON> (https://github.com/acupajoe)", "<PERSON> (https://github.com/patrick<PERSON><PERSON><PERSON>)"], "ignore": ["assets", "config", "test", ".babelrc", ".editorcofig", ".giti<PERSON>re", ".sass-lint.yml", ".travis.yml", "contributing.md", "gulpfile.js", "package.json"], "description": "A beautiful, responsive, customizable and accessible (WAI-ARIA) replacement for JavaScript's popup boxes, supported fork of sweetalert", "main": ["dist/sweetalert2.js", "src/sweetalert2.scss", "dist/sweetalert2.css"], "keywords": ["alert", "modal"], "repository": {"type": "git", "url": "**************:limonte/sweetalert2.git"}, "license": "MIT"}