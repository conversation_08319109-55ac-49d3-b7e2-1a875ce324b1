@import url('https://fonts.googleapis.com/css?family=Open+Sans:400,600,700,300'); // Open Sans font
@import url('https://fonts.googleapis.com/css?family=Open+Sans+Condensed:700');   // Condensed

$black: #000;
$white: #fff;
$transparent: rgba($black, 0);
$athens: #f2f4f6;
$salmon: #f77;
$emperor: #555;
$dovegray: #666;
$regentgray: #8d98a5;
$chateaugray: #a7adb2;
$curiousblue: #3085d6;
$porcelain: #e2e5e8;
$pear: #a6e22d;
$melrose: #c3bcff;
$spray: #66d9ef;
$chenin: #e6db74;
$armadillo: #49483e;
$cinnabar: #eb4c36;
$cornflowerblue: #2196f3;
$white-button: rgba($white, .8);

@mixin retina-background($url, $type: png) {
  background-image: image-set(url('#{$url}.#{$type}') 1x, url('#{$url}@2x.#{$type}') 2x);
  background-repeat: no-repeat;
}

html,
body {
  margin: 0;
}

body {
  background-color: $athens;
  font-family: 'Open Sans', sans-serif;
  text-align: center;
  color: $emperor;
}

header {
  max-width: 650px;
  margin: auto;

  h1 {
    font-family: 'Courgette', serif;
    font-size: 60px;
    color: lighten($black, 66);
    white-space: nowrap;
    margin: 50px auto 0;

    span {
      margin: 0 10px;
      color: $salmon;
    }

    @media all and (max-width: 440px) {
      font-size: 50px;
    }
  }

  h2 {
    font-size: 20px;
    line-height: 25px;
    text-transform: uppercase;
    font-weight: 300;
    text-align: center;
    display: block;

    a {
      color: $curiousblue;
    }
  }
}

.swal2-alert {
  h2 {
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
  }
}

.modal-types {
  td {
    vertical-align: middle;
    text-align: center;
    width: 33%;
  }

  .swal2-icon,
  button {
    display: inline-block;
    margin: 0;
  }

  .swal2-success {
    &::before,
    &::after {
      background: transparent !important;
    }
  }
}

.modal-input-types {
  tr {
    td {
      &:first-child {
        padding-top: 45px;
      }
    }
  }

  td {
    vertical-align: middle;
    text-align: center;
    width: 33%;
  }
}

h3 {
  font-size: 28px;
  color: $dovegray;
  text-transform: uppercase;
  font-family: 'Open Sans Condensed', sans-serif;
  margin-top: 100px;
  text-align: center;
  position: relative;

  &.download-section {
    margin-top: 50px;
    padding-top: 40px;
  }

  &::after {
    content: '';
    background-color: $porcelain;
    height: 4px;
    width: 700px;
    left: 50%;
    margin-left: -350px;
    position: absolute;
    margin-top: -50px;
    border-radius: 2px;

    @media all and (max-width: 740px) {
      width: auto;
      left: 20px;
      right: 20px;
      margin-left: 0;
    }
  }
}

a {
  text-decoration: none;
  color: $cornflowerblue;
  font-weight: 600;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

p {
  max-width: 826px;
  margin: 30px auto;
  font-size: 17px;
  font-weight: 300;
  line-height: 25px;
  text-align: left;

  &.center {
    text-align: center;
  }

  strong {
    font-weight: 600;
  }

  @media all and (max-width: 750px) {
    text-align: center;
    padding: 0 10px;
  }
}

button,
.button {
  $btn-blue: #3085d6;
  $btn-gray: #d0d0d0;

  background-color: $btn-blue;
  color: $white;
  border: 0;
  box-shadow: none;
  font-size: 18px;
  font-weight: 500;
  border-radius: 3px;
  padding: 15px 35px;
  margin: 26px 5px 0;
  cursor: pointer;
  white-space: nowrap;

  &:hover {
    background-color: darken($btn-blue, 3%);
  }

  &:focus {
    &:not(.swal2-close) {
      &:not(.btn) {
        box-shadow: 0 0 0 2px $athens, 0 0 0 4px $chateaugray;
        outline: none;
      }
    }
  }


  &:active {
    background-color: darken($btn-blue, 10%);
  }

  &.cancel {
    background-color: $btn-gray;

    &:hover {
      background-color: darken($btn-gray, 3%);
    }

    &:active {
      background-color: darken($btn-gray, 10%);
    }
  }
}

.top-right-button {
  position: absolute;
  right: 80px;
  width: 170px;
  height: 50px;
  line-height: 50px;
  margin: 0;
  z-index: 3;
  text-transform: uppercase;
  font-weight: 500;
  background-color: $white-button;
  border-radius: 2px;
  cursor: pointer;

  &:hover {
    text-decoration: none;
  }

  &.download {
    top: 30px;
    color: $cornflowerblue;
  }

  &.cdn {
    top: 90px;
    color: $cinnabar;
  }

  &.donate {
    background-color: $white;
    color: $curiousblue;
    font-weight: 600;
    top: 160px;

    img {
      vertical-align: -9px;
    }
  }

  @media all and (max-width: 1150px) {
    display: none;
  }
}

.center-container {
  max-width: 725px;
  margin: 70px auto;
}

pre {
  background-color: $armadillo;
  color: lighten($black, 97);
  padding: 10px;
  border-radius: 5px;
  text-align: left;
  font-size: 14px;
  overflow: hidden;

  .str {
    color: $chenin;
  }

  .func {
    color: $spray;
  }

  .val {
    color: $melrose;
  }

  .tag {
    color: $salmon;
  }

  .attr {
    color: $pear;
  }

  .comment {
    color: $regentgray;
  }
}

.showcase {
  background-color: $porcelain;
  margin-top: 15px;
  padding: 20px;
  display: inline-block;
  width: 383px;
  vertical-align: top;
  position: relative;

  @media all and (max-width: 865px) {
    padding: 0 10px;

    &.normal,
    h4 {
      display: none;
    }
  }
  @media all and (max-width: 423px) {
    width: 90%;
  }

  h4 {
    font-size: 16px;
    color: lighten($black, 70);
    line-height: 22px;
    margin: 0 auto;
    font-weight: 800;
  }

  &.sweet {
    h4 {
      font-family: 'Courgette', serif;
      font-size: 24px;
      color: lighten($black, 66);

      span {
        margin: 0 4px;
        color: $salmon;
      }
    }
  }

  button {
    margin-bottom: 10px;
  }

  .vs-icon {
    @include retina-background('vs_icon');
    width: 69px;
    height: 69px;
    position: absolute;
    right: -34px;
    top: 60px;
    z-index: 2;

    @media all and (max-width: 865px) {
      margin: 5px auto;
      right: auto;
      left: 50%;
      margin-left: -35px;
      top: auto;
      bottom: -35px;
    }
  }
}


.examples {
  list-style-type: none;
  width: 750px;
  margin: 0 auto;
  text-align: left;
  padding-left: 0;
  @media all and (max-width: 758px) {
    width: auto;
  }

  li {
    padding-left: 0;
  }

  .ui,
  pre {
    display: inline-block;
    vertical-align: top;

    @media all and (max-width: 758px) {
      display: block;
      max-width: none;
      margin: 0 auto;
    }
  }

  .ui {
    width: 300px;
    text-align: center;

    button {
      margin-top: 12px;
    }

    p {
      text-align: center;
      margin-bottom: 0;
    }
  }

  pre {
    max-width: 420px;
    margin-top: 67px;

    @media all and (max-width: 758px) {
      margin-top: 16px !important;
      margin-bottom: 60px;
    }
  }

  .warning {
    pre {
      margin-top: 93px;
    }
  }
}


ol {
  max-width: 725px;
  margin: 70px auto;
  list-style-position: inside;
  padding-left: 0;

  li {
    color: $chateaugray;

    p {
      margin-bottom: 10px;
    }
  }
}


table {
  width: 725px;
  font-size: 14px;
  margin: 10px auto;
  text-align: left;
  border-collapse: collapse;
  @media all and (max-width: 750px) {
    width: auto;
    margin: 10px auto;
  }

  th {
    background-color: $white;
    padding: 10px 20px;
    color: $dovegray;
    font-weight: 400;
    position: relative;
    white-space: nowrap;

    @media all and (max-width: 750px) {
      &:nth-child(2) {
        display: none;
     }
    }
  }

  td {
    padding: 10px 20px;
    vertical-align: top;

    @media all and (max-width: 750px) {
      &:nth-child(2) {
        display: none;
     }
    }
    @media all and (max-width: 360px) {
      padding: 10px 4px;

      b {
        font-size: 13px;
      }
    }
  }
}

.hidden {
  opacity: 0;
}

.mobile-hidden {
  @media all and (max-width: 750px) {
    display: none;
  }
}

.github-corner {
  position: fixed;
  top: 0;
  right: 0;
}

.carbonads-wrapper {
  height: 126px;

  > div {
    display: block;
    overflow: hidden;
    margin: 0 auto;
    padding: 1em;
    max-width: 300px;
    border: solid 1px $regentgray;
    border-radius: 2px;
    box-shadow: inset 0 1px $white;
    text-shadow: 0 1px $white;
    font-size: 12px;
    line-height: 1.5;

    a {
      color: $regentgray;
      transition: color .15s ease-in-out;

      &:hover {
        color: $salmon;
      }
    }

    span {
      position: relative;
      display: block;
      overflow: hidden;
    }

    .carbon-img {
      float: left;
      margin-right: 1em;

      img {
        display: block;
      }
    }

    .carbon-text {
      display: block;
      float: left;
      max-width: calc(100% - 130px - 1em);
      text-align: left;
    }

    .carbon-poweredby {
      position: absolute;
      right: 0;
      bottom: 0;
      display: block;
      font-size: 11px;
    }
  }
}

.donation-options,
.collaborators {
  max-width: 700px;
  margin: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  font-size: 20px;

  a {
    display: inline-block;
    min-width: 140px;
    text-decoration: none;
  }

  img {
    margin-top: 10px;
  }
}

.stats {
  font-size: 13px;
  opacity: 0;
  margin-top: 7px;
}

.collaborators {
  font-size: 16px;
}
