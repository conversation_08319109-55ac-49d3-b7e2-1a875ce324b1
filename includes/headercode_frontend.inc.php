<?php

  Config::set('IS_BACKEND', false); //config to define frontend/backend

  ini_set('memory_limit', '256M');

  //is dit een oude link? Ja, dan redirecten.
  UrlRedirect::redirect($site->id);

  if (isset($_SESSION['project']) && $_SESSION['project'] != PROJECT) {
    //logoff if current project not session project
    GsdSession::stopSession(true, 'frontend');
    ResponseHelper::redirect("/");
  }

  $pageId = null;
  $page_found = false;

  //@todo: deze code hoort thuis in loginAction. Kunnen we niet zomaar verwijderen, omdat dit misschien gebruikt word.
  if (!in_array(PROJECT, ["heblad", "jari"]) && (!isset($_SESSION['loggedIn']) || !$_SESSION['loggedIn'])) {
    if (isset($_POST['emailadres']) && isset($_POST['password']) && $_POST['emailadres'] != "" && $_POST['password'] != "" && !strstr($_POST['emailadres'], "'") && !strstr($_POST['emailadres'], "%") && !strstr($_POST['password'], "'") && !strstr($_POST['password'], "%")) {
      $ignorepassword = false;
      if (Config::isdefined("MASTER_PASSWORD") && $_POST['password'] == Config::get("MASTER_PASSWORD")) {
        $ignorepassword = true;
      }
      $user = User::login($_POST['emailadres'], $_POST['password'], $ignorepassword, Site::getFrontendLoginUsergroups());
      if ($user != null) {
        GsdSession::startSession($user, 'frontend');
        $start = $user->startpage;
        if (isset($_POST['remember_me']) && $_POST['remember_me'] == '1' && Config::isTrue("LOGIN_REMEMBER_ME")) {
          (new \gsdfw\domain\authentication\service\UserLoginByToken((new \gsdfw\domain\authentication\repository\LoginTokenCookie('frontend_user'))))->saveNewToken($user);
        }
        //echo $start;
        if ($start != '0' && $start != '') {
          $pageId = $start;
          $_GET['pageId'] = $start;
        }
        else {
          $pageId = 'M_HOME';
          $_GET['pageId'] = $pageId;
        }
      }
    }
  }

  PageMapBuilder::buildFrontend();
  NavigationBuilderFactory::buildFrontend();

  /* Mogelijkheid om niet gepubliceerde pagina online alvast te bekijken. */
  if (isset($_GET["preview"]) && isset($_GET["pageId"])) {
    $pageId = $_GET['pageId'];
    $pageId = PageMap::getInstance()->getPageId($pageId);
    if ($pageId != false) {
      $page_object = Page::getPageAndContent($pageId);
      if ($page_object) {
        $page_found = true;
        Navigation::getInstance()->addItem(NavigationItem::createNew($page_object));
        PageMap::getInstance()->addPageMapWithPage($page_object);
        Navigation::getInstance()->setNavActive($pageId);
        if ($page_object->published == 0) {
          MessageFlashCoordinator::addMessageAlert("Dit is een voorbeeld van de pagina. Deze pagina staat nog niet online.");
        }
      }
    }
  }

  if (!$page_found && isset($_SERVER["REQUEST_URI"])) {
    /**
     * Er is een request_uri, zoek naar een exacte match. Gevonden, dan laad deze pagina.
     */
    $url = explode("?", $_SERVER["REQUEST_URI"]);
    $url = $url[0];

    //this is a Page?
    $pc = PageContent::getByUrl($url, $site->id);
    if ($pc) {
      $page_found = true;
      $pageId = $pc->page_id;
    }

    if (!$page_found && PageMap::pageidExists("M_SHOP_CATEGORY")) {
      //this is a Category?
      $cc = CategoryContent::getByUrl($url);
      if ($cc) {
        $page_found = true;
        $pageId = 'M_SHOP_CATEGORY';
        $_GET["var2"] = $cc->category_id;
      }
    }

    if (!$page_found && PageMap::pageidExists("M_PRODUCTS")) {
      //this is a Product?
      $pc = ProductContent::getByUrl($url);
      if ($pc) {
        $page_found = true;
        $pageId = 'M_PRODUCTS';
        $_GET["var2"] = $pc->product_id;
      }
    }

    if (Config::isTrue("PRODUCT_CONTAINERS")) {
      if (!$page_found && PageMap::pageidExists("M_PRODUCTS")) {
        //this is a Product container?
        $pc = ProductContContent::getByUrl($url);
        if ($pc) {
          $page_found = true;
          $pageId = 'M_PRODUCTS';
          $_GET["var2"] = $pc->product_cont_id;
        }
      }
    }

    if (!$page_found && PageMap::pageidExists("M_TAGS")) {
      $tc = TagContent::getByUrl($url, $site->id);
      if ($tc) {
        $page_found = true;
        $pageId = 'M_TAGS';
        $_GET["var2"] = $tc->tag_id;
      }
    }

  }

  if (!$page_found) {
    //niks gevonden dan maar op de oude manier.
    if (isset($_GET['pageId']) && $_GET['pageId'] != '') {
      $pageId = $_GET['pageId'];
      $pageId = PageMap::getInstance()->getPageId($pageId);
    }
    elseif (isset($_GET['var1'])) {
      $pageId = PageMap::getInstance()->getPageId($_GET['var1']);
      $pageId = PageMap::getInstance()->getPageId($pageId);
    }
    if ($pageId != false) {
      $page_found = true;
    }
  }

  if (!$page_found) {
    //nog niks gevonden? Dan terugvallen naar homepage/1ste pagina
    if (isset($site) && $site->site_host->homepage_id != null) {
      if (isset($_SERVER["REQUEST_URI"]) && $_SERVER["REQUEST_URI"] != "" && $_SERVER["REQUEST_URI"] != "/" && substr($_SERVER["REQUEST_URI"], 0, 2) != "/?" && $_SERVER["REQUEST_URI"] != "/robots.txt") {
        //we willen de homepage tonen, maar de request uri is niet leeg. 301 redirect naar homepage

        if (ENVIRONMENT == 'LOCAL') {
          throw new GsdException('DEVELOPER MESSAGE: page not found. On production this page will be redirected to / (pageId=' . $pageId . ', url: ' . $_SERVER["REQUEST_URI"] . ') Is this correct behaviour? Hints: correct the URL | define the pageId in routing | add it to the Navigation class');
        }

        ResponseHelper::redirect301("/");
      }
      //open homgepage
      $pageId = $site->site_host->homepage_id;
      $pageId = PageMap::getInstance()->getPageId($pageId);
    }
    else {
      /**
       * @deprecated
       * Het systeem valt hier terug op de eerste pagina die hij kan vinden.
       * Dit is eigenlijk een vreemde situatie, en niet verstandig voor SEO.
       * Deze url kan nu namelijk geindexeerd worden.
       * Beleid moet zijn om altijd een homepage_id in te stellen in de site_host
       * Het gaat hier immers om een frontend applicatie. Voor een backend applicatie maakt dit niet veel uit, deze word niet geindexeerd.
       * Ik heb geen redirect aangemaakt, omdat dit misschien backward incompatibility kan veroorzaken.
       */

      trigger_error('Code is deprecated. Set a homepage_id in site_host.', E_USER_DEPRECATED); //trigger toegevoegd op 12-09-2023

      $pageId = Navigation::getFirstItem()->pageId;
      $pageId = PageMap::getInstance()->getPageId($pageId);
    }
  }

  if ($pageId === false || !Navigation::getInstance()->setNavActive($pageId)) {
    if (isset($_SERVER["REQUEST_URI"]) && $_SERVER["REQUEST_URI"] == "/") {
      die("Loop prevention. Unknown pageId: " . $pageId);
    }
    if (DEVELOPMENT) {
      if ($page_found === true && $pageId === 'M_SHOP_CATEGORY') {
        throw new GsdException('DEVELOPER MESSAGE: did you make a page with module=siteshop en action=home ?');
      }
      throw new GsdException('DEVELOPER MESSAGE: pageId or URL not found. On production page will be redirected to / (pageId=' . $pageId . ', url: ' . $_SERVER["REQUEST_URI"] . ') Is this correct behaviour? Hints: define the pageId in routing | add it to the Navigation class');
    }
    ResponseHelper::redirect301("/");
  }
  elseif (!Privilege::hasRight($pageId)) { //geen rechten tot navigatie
    ResponseHelper::redirectAccessDenied();
  }

  //breadcrumbs
  $skip = [$site->site_host->homepage_id];
  if (Config::isdefined('BREADCRUMBS_IGNORE_IDS')) {
    $skip = array_merge($skip, Config::get('BREADCRUMBS_IGNORE_IDS'));
  }
  Navigation::getInstance()->fillBreadCrumbs($pageId, 'M_ROOT', '', 'Home', $skip);


  Config::set("PRODUCTS_SHOW_PRICES_INC_VAT", true); //show prices including vat.

  require(DIR_INCLUDES . "actionloader.inc.php");



  