<div class="horizontal-menu-bar">
  <nav>

    <?php
      $nav = Navigation::getInstance();

      $hide_children = [
        'M_SHEETS',
        'M_SETTINGS',
        'M_ORGANISATION_OV',
        'M_ORGANISATION_LEV',
        'M_PAGES',
        'M_SELECTSITE',
        'M_PROSPECTS',
      ];

      $root_nav_item = Navigation::getItem('M_TOP');
      $items         = $root_nav_item->getChildren();

      function renderLevelOfNavItems(Navigation $nav, array $navigation_items, NavigationItem $parent_item, array $hide_children) {

        //$parentLink = (!$parent_item->isDisabled()) ? PageMap::getUrl($parent_item->getPageId()) : '#';

        $out = '<ul>';
        foreach ($navigation_items as $item) {

          if (!$item->hasRight() || !$item->isShow() || !$item->isShowMainMenu()) continue;

          $subitems = Navigation::getItem($item->getPageId())->getChildren();
          // if there are no subitems on these conditions
          if ($subitems && (($item->getPageId() == 'M_ORGANISATIONS' && count($subitems) <= 1) || in_array($item->getPageId(), $hide_children))) {
            $subitems = [];
          }

          $hassubitems = false;
          foreach ($subitems as $sub_item) {
            if ($sub_item->hasRight() && $sub_item->isShow() && $sub_item->isShowMainMenu()) {
              $hassubitems = true;
            }
          }

          $link = (!$item->isDisabled()) ? PageMap::getUrl($item->getPageId()) : '#';
          $sub_item_class      = $hassubitems ? 'has-sub-items' : '';
          $open_sub_item_class = ($item->isActive()) ? 'sub-open' : '';
          $target              = ($item->getTarget() != '') ? 'target="' . $item->getTarget() . '"' : '';
          $active_class        = ($item->isActive()) ? ' class="active"' : '';
          $item_name           = $item->getName();
          $subitems_html       = $hassubitems ? renderLevelOfNavItems($nav, $subitems, $item, $hide_children) : '';

          $out .= <<<HTML
            <li class="$sub_item_class $open_sub_item_class">
              <a href="$link" $target $active_class>
                <span class='link-name'>$item_name</span>
              </a>
              $subitems_html
            </li>
        HTML;
        }

        $out .= '</ul>';
        return $out;
      }

    ?>
    <?php echo renderLevelOfNavItems($nav, $items, $root_nav_item, $hide_children); ?>
  </nav>
</div>

