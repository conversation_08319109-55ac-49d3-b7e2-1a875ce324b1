<?php
  $adminLanguagesToSelect = [];
  foreach ($_SESSION['site']->site_host->getPossibleLangs() as $adminLang):
    if ($_SESSION['lang'] === $adminLang) continue;
    if (Config::isdefined("BEHEER_LANGUAGES_HIDDEN") && in_array($adminLang, Config::get("BEHEER_LANGUAGES_HIDDEN"))) continue;
    $adminLanguagesToSelect[] = $adminLang;
  endforeach;
  if (count($adminLanguagesToSelect) > 0): ?>
    <details class="popout-box">
      <summary>
        <img src="/gsdfw/images/flags/<?php echo $_SESSION['lang'] ?>.png" alt="<?php echo $_SESSION['lang'] ?>">
        <i class="fa fa-caret-down fa-xs caret"></i>
      </summary>
      <div class="popout-content right" tabindex="-1">
        <?php foreach ($adminLanguagesToSelect as $adminLanguage): ?>
          <a href="/<?php echo $adminLanguage ?>/">
            <img src="/gsdfw/images/flags/<?php echo $adminLanguage ?>.png" alt="<?php echo LanguageHelper::getLanguageByCode($adminLanguage) ?>">
          </a>
        <?php endforeach; ?>
      </div>
    </details>
  <?php endif; ?>