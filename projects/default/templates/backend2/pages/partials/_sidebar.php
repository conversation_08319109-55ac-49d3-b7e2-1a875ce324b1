<aside class="sidebar">

  <?php if (Config::isTrue("ORGANISATION_LOGO_SHOW_HEADER") && $_SESSION['userObject']->organisation->logo_orgin != ''): ?>
    <a class="logo logo-image" href="/" style="background-image: url('<?php echo URL_UPLOADS . 'logo/' . $_SESSION['userObject']->organisation->logo_prev ?>');"></a>
  <?php else: ?>
    <a class="logo logo-image" href="/"></a>
  <?php endif; ?>

  <div class="toggle-menu">
    <i class="ki-solid toggle-sidebar"></i>
  </div>

  <?php
    $nav = Navigation::getInstance();

    $hide_children = [
      'M_SHEETS',
      'M_SETTINGS',
      'M_ORGANISATION_OV',
      'M_ORGANISATION_LEV',
      'M_PAGES',
      'M_SELECTSITE',
      'M_PROSPECTS',
    ];

    $root_nav_item = Navigation::getItem('M_TOP');
    $items = $root_nav_item->getChildren();

    function renderLevelOfNavItems(Navigation $nav, array $navigation_items, NavigationItem $parent_item, array $hide_children) {

      //$parentLink = (!$parent_item->isDisabled()) ? PageMap::getUrl($parent_item->getPageId()) : '#';

      $out = '<ul>';
//      $out .= '<li class="sub-items-header">';
//      $out .= $parent_item->getName();
//      $out .= '</li>';
      foreach ($navigation_items as $item) {

        if (!$item->hasRight() || !$item->isShow() || !$item->isShowMainMenu()) continue;

        $subitems = Navigation::getItem($item->getPageId())->getChildren();
        // if there are no subitems on these conditions
        if ($subitems && (($item->getPageId() == 'M_ORGANISATIONS' && count($subitems) <= 1) || in_array($item->getPageId(), $hide_children))) {
          $subitems = [];
        }

        $hassubitems = false;
        foreach ($subitems as $sub_item) {
          if ($sub_item->hasRight() && $sub_item->isShow() && $sub_item->isShowMainMenu()) {
            $hassubitems = true;
          }
        }

        $link = (!$item->isDisabled()) ? PageMap::getUrl($item->getPageId()) : '#';
        $sub_item_class = $hassubitems ? 'has-sub-items' : '';
        $open_sub_item_class = ($item->isActive()) ? 'sub-open' : '';
        $target = ($item->getTarget() != '') ? 'target="' . $item->getTarget() . '"' : '';
        $active_class = ($item->isActive()) ? ' active' : '';
        $item_name = $item->getName();
        $icon_class = trim($item->getPageId());
        $sub_item_icon = $hassubitems ? '<i class="fa fa-chevron-down fa-sm arrow"></i>' : '';
        $subitems_html = $hassubitems ? renderLevelOfNavItems($nav, $subitems, $item, $hide_children) : '';

        $out .= <<<HTML
            <li class="$sub_item_class $open_sub_item_class">
              <div class="nav-item $active_class">
              <i class="ki-solid nav-item-icon $icon_class"></i>
              <a href="$link" $target $active_class>
                <span class='link-name'>$item_name</span>
              </a>
              $sub_item_icon
              </div>
              $subitems_html
            </li>
        HTML;
      }

      $out .= '</ul>';
      return $out;
    }

  ?>

  <nav>
    <?php echo renderLevelOfNavItems($nav, $items, $root_nav_item, $hide_children); ?>
  </nav>
</aside>

<script type="text/javascript">
  let arrow = document.querySelectorAll(".arrow");

  for (var i = 0; i < arrow.length; i++) {
    arrow[i].addEventListener("click", (e) => {
      let arrowParent = e.target.parentElement.parentElement;
      arrowParent.classList.toggle("sub-open");
    });
  }

  let sidebar = document.querySelector(".sidebar");
  let sidebarBtn = document.querySelector(".toggle-menu");
  sidebarBtn.addEventListener("click", () => {
    toggle_sidebar_minimized(!sidebar.classList.contains('minimized'));
    window.localStorage.setItem('gsdfw_sidebar_minimized', sidebar.classList.contains('minimized'));
  });

  toggle_sidebar_minimized((window.localStorage.getItem('gsdfw_sidebar_minimized') && window.localStorage.getItem('gsdfw_sidebar_minimized') === 'true'));

  /**
   * toggle between minimized and full sidebar
   *
   * @param bool is_minimized
   */
  function toggle_sidebar_minimized(is_minimized) {
    if (is_minimized) {
      sidebarBtn.classList.add("open");
      sidebar.classList.add("minimized");
      // we have to set a small timeout for this, else the hover would immediately trigger and the sidebar would not close
      setTimeout(function () {
        sidebar.classList.add("minimized-hover");
      }, 200);
      document.getElementById('main-grid').classList.add('minimized-sidebar')
    }
    else {
      sidebarBtn.classList.remove("open");
      sidebar.classList.remove("minimized");
      setTimeout(function () {
        sidebar.classList.remove("minimized-hover");
      }, 200);
      document.getElementById('main-grid').classList.remove('minimized-sidebar')
    }
  }
</script>