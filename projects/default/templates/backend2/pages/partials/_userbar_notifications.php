<?php if (Config::get("MESSAGECOORDINATOR_TYPES", true)): ?>
  <?php
  /** for testing purposes */
  /*      $projectmessages = [];
          foreach (Orders::find_all_by(['status' => 'ordered']) as $l_order):
            $msg               = new Message();
            $msg->title        = $l_order->getOrderNr();
            $msg->message      = "Order in bestelling: " . $l_order->getOrderNr();
            $msg->url          = PageMap::getUrl('M_ORDER') . '?action=edit&id=' . $l_order->id;
            $msg->url_title    = "Bewerk bestelling";
            $projectmessages[] = $msg;
          endforeach;*/
  ?>
  <?php foreach (Config::get("MESSAGECOORDINATOR_TYPES") as $mtype): ?>
    <?php $projectmessages = MessageCoordinator::getMessagesFromSessionByType(Message::MESSAGE_TYPE_PROJECT); ?>
    <details class="popout-box notification">
      <summary title="<?php echo __("Meldingen"); ?>">
        <i class="fa fa-bell-o fa-lg"></i>
        <?php if (count($projectmessages) > 0): ?>
          <div class="notifications-amount"><?php echo (count($projectmessages) > 9) ? '+' : count($projectmessages); ?></div>
        <?php endif; ?>
      </summary>
      <?php if (count($projectmessages) > 0): ?>
        <div class="popout-content w-96 right" tabindex="-1">
          <?php foreach ($projectmessages as $prj_message): ?>
            <a href="<?php echo $prj_message->url ?>" class="notification-item">
              <?php echo $prj_message->message; ?>
              <br>
              <span><?php echo $prj_message->url_title ?></span>
            </a>
          <?php endforeach; ?>
        </div>
      <?php endif; ?>
    </details>
  <?php endforeach; ?>
<?php endif; ?>