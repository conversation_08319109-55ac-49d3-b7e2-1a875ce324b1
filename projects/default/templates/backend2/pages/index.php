<?php
  use gsdfw\domain\backendpreferences\service\BackendPreferences;
  include("gsdfw/includes/headercode_backend.inc.php");
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="<?php echo $_SESSION['lang'] ?>" xml:lang="<?php echo $_SESSION['lang'] ?>">

<?php TemplateHelper::includeBackendIndexPartial("_head.php", $current_action); ?>

<body class="<?php if ($current_action != null) echo "body_" . $current_action->getModule() . " body-action-" . $current_action->getAction() ?>">
<div id="loading-spinner">
  <div class="spinner"></div>
</div>
<section class="main-grid <?php
  if (Navigation::getInstance()->getWritenav($pageId) == false || $current_action->template_wrapper_only == true) echo 'minimal-layout ';
  echo sprintf("%s-menu-active ", BackendPreferences::isTop() ? 'horizontal' : 'vertical');
?>" id="main-grid">

  <?php if (Navigation::getInstance()->getWritenav($pageId) == false || $current_action->template_wrapper_only == true): ?>

    <header>
      <?php TemplateHelper::writeStagingMessage() ?>
      <?php MessageFlashCoordinator::showMessages() ?>
    </header>

    <main>
      <?php include($current_action->getTemplatePath()); ?>
    </main>

  <?php else: ?>

    <header>
      <?php TemplateHelper::writeStagingMessage() ?>
      <?php TemplateHelper::writeUpdaterMessages() ?>
    </header>

    <?php if (BackendPreferences::isTop()): ?>
      <?php TemplateHelper::includeBackendIndexPartial("_horizontal_menu.php", $current_action); ?>
    <?php else: ?>
      <?php TemplateHelper::includeBackendIndexPartial("_sidebar.php", $current_action); ?>
    <?php endif; ?>

    <?php TemplateHelper::includeBackendIndexPartial("_userbar.php", $current_action); ?>

    <?php TemplateHelper::includeBackendIndexPartial("_content.php", $current_action); ?>
    <?php TemplateHelper::includeBackendIndexPartial("_footer.php", $current_action); ?>
  <?php endif; ?>

</section>
<?php if(!empty($current_action) && $current_action->getModule()=="login"): ?>
  <div class="circles closing">
    <div class="bigCircle"></div>
    <div class="smallCircle"></div>
  </div>
<?php endif; ?>
</body>
</html>
