<div id="head">
  <div style="margin: 0 auto;width: 1200px;" class="header">
    <a href="/" id="homelink">&nbsp;</a>
    <?php if(isset($_SESSION['userObject']) && Config::isTrue("MESSAGECOORDINATOR_TYPES")): ?>
      <div id="top-center-menu">
        <?php foreach(Config::get("MESSAGECOORDINATOR_TYPES") as $mtype): ?>
          <?php $projectmessages = MessageCoordinator::getMessagesFromSessionByType(Message::MESSAGE_TYPE_PROJECT); ?>
          <a title="<?php echo __("Meldingen"); ?>" href="javascript:void(0);" class="iconsprite <?php echo $mtype ?>_icon<?php if(count($projectmessages)==0) echo  '_grey' ?> icon_link" id="<?php echo $mtype ?>_icon"><i class="fa fa-bell-o" aria-hidden="true"></i></a>
          <?php if(count($projectmessages)>0): ?>
            <div class="icondot"><?php echo (count($projectmessages)>9)?'+':count($projectmessages); ?></div>
            <div class="iconmessagebut" id="<?php echo $mtype ?>_messages_but"><a title="<?php echo __("Meldingen"); ?>" href="javascript:void(0);" class="iconsprite project_icon" id="project_icon"><i class="fa fa-bell-o" aria-hidden="true"></i></a></div>
            <div class="iconmessage" id="<?php echo $mtype ?>_messages">
              <ul>
                <li style="border: 0;">
                  <div class="iconmessagediv_head"><?php echo __("Meldingen"); ?></div>
                </li>
                <?php foreach($projectmessages as $prj_message): ?>
                  <li onclick="location.href='<?php echo $prj_message->url  ?>';">
                    <div class="iconmessagediv"><?php echo $prj_message->message . '<br/><a href="'. $prj_message->url . '">'. $prj_message->url_title.'</a><br />'; ?></div>
                  </li>
                <?php endforeach;?>
              </ul>
            </div>
          <?php endif; ?>
        <?php endforeach; ?>
      </div>
    <?php endif; ?>
    <div id="topmenu">
      <?php if(isset($_SESSION['userObject'])): ?>
        <?php if(Config::isTrue("ORGANISATION_LOGO_SHOW_HEADER") && $_SESSION['userObject']->organisation->logo_orgin!=''): ?>
          <div id="header_company_logo"><span id="header_company_logo_h"></span><img src="<?php echo URL_UPLOADS.'logo/' .$_SESSION['userObject']->organisation->logo_prev ?>" alt="logo"></div>
        <?php endif; ?>
        <div id="header_my_info">
          <?php if(isset($_SESSION['was_admin']) && $_SESSION['was_admin'] != ""): ?>
            <a href="#return2admin" id="return2admin" title="TERUG NAAR UW ACCOUNT"><i class="fa fa-reply"></i></a>
            |
          <?php endif; ?>
          <span class="name"><?php echo $_SESSION['userObject']->getNaam(); ?></span>
          <?php if($_SESSION['userObject']->organisation->name!=""): ?>
            | <span  class="header_companyname"><?php echo $_SESSION['userObject']->organisation->name   ?> </span>
          <?php endif; ?>
          <?php if(defined("CREDITS_ENABLED") && CREDITS_ENABLED && $_SESSION['userObject']->organisation->credits!==null): ?>
            | <a href="<?php echo PageMap::getUrl('M_SETTINGS_CREDITS'); ?>" title="Naar uw credit geschiedenis">Credits: <?php echo getLocalePrice($_SESSION['userObject']->organisation->credits) ?> €</a>
          <?php endif; ?>
          <?php if(count($_SESSION['site']->site_host->getPossibleLangs())>1): ?>
            <?php foreach($_SESSION['site']->site_host->getPossibleLangs() as $cslang):
              if(Config::isdefined("BEHEER_LANGUAGES_HIDDEN") && in_array($cslang,Config::get("BEHEER_LANGUAGES_HIDDEN"))) continue;
              ?>
              <span> |
                   <?php if($_SESSION['lang']!=$cslang): ?><a href="/<?php echo $cslang ?>/"><?php endif; ?>
                  <?php echo LanguageHelper::getLanguageByCode($cslang) ?>
                  <?php if($_SESSION['lang']!=$cslang): ?></a><?php endif; ?>
                   </span>
            <?php endforeach; ?>
          <?php endif; ?>
          <div style="padding-top: 5px;display:block;">
            <a href="<?php echo PageMap::getUrl('M_SETTINGS_PERS') ?>"><?php echo PageMap::getName('M_SETTINGS_PERS') ?></a> |
            <?php if(isset($_SESSION['basket']) && isset($_SESSION['basket']['products']) && Privilege::hasRight('M_BASKET')): ?>
              <a href="<?php echo PageMap::getUrl('M_BASKET') ?>" style="color: #C4013D;"><?php echo PageMap::getName('M_BASKET') ?>(<?php echo count($_SESSION['basket']['products']) ?>)</a> |
              <a href="<?php echo PageMap::getUrl('M_BASKET') ?>&action=pay1" style="color: #C4013D;">Afrekenen</a> |
            <?php elseif(Privilege::hasRight('M_BASKET')): ?>
              <a href="<?php echo PageMap::getUrl('M_BASKET') ?>"><?php echo PageMap::getName('M_BASKET') ?></a> |
              <a href="<?php echo PageMap::getUrl('M_BASKET') ?>&action=pay1">Afrekenen</a> |
            <?php endif; ?>
            <a href="<?php echo PageMap::getUrl('M_LOGOFF') ?>"><?php echo PageMap::getName('M_LOGOFF') ?></a>
          </div>
        </div>
      <?php endif; ?>
    </div>
  </div>
</div>
