<?php
  if(!Privilege::hasRight('M_PRODUCTS')) return;
  if(!isset($cats)) $cats = Category::find_all('WHERE online_uc=1 AND category.void=0 AND category.parent_id IS NULL ORDER BY category.sort ASC');
?>
<div class="div_leftmenu">
  <ul id="leftmenu">
    <?php foreach ($cats as $cat): ?>
      <li>
        <a href="<?php echo PageMap::getUrl('M_PRODUCTS') ?>/<?php echo StringHelper::slugify($cat->getName($_SESSION['lang'])) ?>?catid=<?php echo $cat->id ?>" style="<?php if(isset($category) && $category->id == $cat->id): ?>color: black;<?php endif; ?>">
          <?php echo $cat->getName($_SESSION['lang']) ?>
        </a>
      </li>
    <?php endforeach ?>
  </ul>
</div>