<head>
  <title><?php echo $seo_title ?></title>

  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta http-equiv="expires" content="-1" />
  <meta http-equiv="pragma" content="no-cache" />
  <meta name="description" content="<?php echo strip_tags(escapeForInput($seo_description)) ?>" />
  <meta name="author" content="GSD - gsd.nl">

  <meta property="og:title" content="<?php echo $seo_title ?>" />
  <meta property="og:type" content="company" />
  <meta property="og:image" content="<?php echo $site->site_host->getDomain(true) ?>/gsdfw/projects/default/templates/backend/images/gsd_logo_rand.svg" />
  <meta property="og:site_name" content="<?php echo PROJECT ?>"/>
  <meta property="og:locale" content="nl-NL" />
  <meta property="og:description" content="<?php echo $seo_title." - ". PROJECT ?>"/>

  <link href="<?php echo isset($favicon_url)?$favicon_url:'/gsdfw/projects/default/templates/backend/images/favicon.ico' ?>" rel="shortcut icon" type="image/x-icon" />

  <link href="//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" type="text/css" />
  <link href="//fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />

  <script type="text/javascript" src="//cdn.jsdelivr.net/es6-promise/latest/es6-promise.auto.min.js"></script>

  <?php if(file_exists($site->getTemplateDir()."dist/")): //project specific css/js dist folder ?>
    <script type="text/javascript" src="<?php echo $site->getTemplateUrl() ?>dist/deploy<?php echo !DEVELOPMENT?'.min':'' ?>.js?v=<?php echo get_asset_version(DIR_ROOT . $site->getTemplateUrl() . '/dist/deploy' . (!DEVELOPMENT?'.min':'') . '.js') ?>"></script>
  <link rel="stylesheet" type="text/css" href="<?php echo $site->getTemplateUrl() ?>dist/main<?php echo !DEVELOPMENT?'.min':'' ?>.css?v=<?php echo get_asset_version(DIR_ROOT . $site->getTemplateUrl() . '/dist/main' . (!DEVELOPMENT?'.min':'') . '.css') ?>">
  <?php else: ?>
    <script type="text/javascript" src="/gsdfw/projects/default/templates/backend/dist/deploy<?php echo !DEVELOPMENT?'.min':'' ?>.js?v=<?php echo get_asset_version(DIR_ROOT_GSDFW . '/projects/default/templates/backend/dist/deploy' . (!DEVELOPMENT?'.min':'') . '.js') ?>"></script>
  <link rel="stylesheet" type="text/css" href="/gsdfw/projects/default/templates/backend/dist/main<?php echo !DEVELOPMENT?'.min':'' ?>.css?v=<?php echo get_asset_version(DIR_ROOT_GSDFW . '/projects/default/templates/backend/dist/main' . (!DEVELOPMENT?'.min':'') . '.css') ?>">
  <?php endif; ?>

  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/popper', true) ?>

  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/general', true); ?>

  <?php if(file_exists($site->getTemplateDir().'js/project.js')): ?>
    <?php echo TemplateHelper::includeJavascript($site->getTemplateUrl() . 'js/project'); ?>
  <?php endif; ?>

  <?php Context::printJavascripts() ?>
  <?php Context::printStylesheets() ?>

  <?php if(file_exists($site->getTemplateDir().'style/style.css')): ?>
    <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl() . 'style/style'); ?>
  <?php endif; ?>

  <?php if(DEVELOPMENT): ?>
    <script src="https://code.jquery.com/jquery-migrate-3.3.2.js"></script>
  <?php endif; ?>


  <script type="text/javascript">
    $(document).ready(function() {

      $("#navmenu-h li").hover(
        function () {
          $(this).addClass("iehover");
        },
        function () {
          $(this).removeClass("iehover");
        });

      <?php if(Config::isTrue("MESSAGECOORDINATOR_ENABLED")): ?>
        <?php foreach(Config::get("MESSAGECOORDINATOR_TYPES") as $mtype): ?>
          $("#<?php echo $mtype ?>_icon").on("click", function () {
            $("#<?php echo $mtype ?>_messages,#<?php echo $mtype ?>_messages_but").show();
          });
        <?php endforeach; ?>

        var mouse_is_inside = false;
        $('.iconmessagebut,.iconmessage').hover(function () {
          mouse_is_inside = true;
        }, function () {
          mouse_is_inside = false;
        });
        $("body").on("mouseup",function(){
          if(!mouse_is_inside) $('.iconmessagebut,.iconmessage').hide();
        });
      <?php endif; ?>

    });

    <?php if(isset($_SESSION['userObject'])): ?>

      $(document).ready(function() {
        $("#return2admin").on("click", function (event) {
          event.preventDefault();
          return2admin();
        });
      });

      function return2admin() {
        $.getJSON("<?php echo reconstructQueryAdd(['pageId' => 'M_AJAX'])?>inner_action=return2admin").done(
          function (data) {
            if (data != 'false') {
              window.location = data;
            }
          }
        );
      }

    <?php endif; ?>

    <?php if(ENVIRONMENT != 'LOCAL' && Config::isdefined("ERROR_HANDLER_JS") && Config::get("ERROR_HANDLER_JS")["enabled"]): ?>
      addErrorHandler();
    <?php endif; ?>
  </script>
</head>