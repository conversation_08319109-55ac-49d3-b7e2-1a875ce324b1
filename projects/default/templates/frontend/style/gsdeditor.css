/**
  THIS IS THE DEFAULT GSDEDITOR.CSS
  Do not change this file, so we can copy it if new features are added
*/

.gsd-editor {
  width: 100%;
  background-color: white;
}

.gsd-container {
  width: 100%;
  margin: 0 auto;
  padding: 50px 0 50px 0;
}

.gsd-container > div {
  /*width: 100%;*/
  margin: 0 auto;
  padding: 0 15px;
  display: flex;
  max-width: 1097px;
  gap: 30px;
}

/** one column grids **/
.gsd-container.gsd-image,
.gsd-container.gsd-wysiwyg,
.gsd-container.gsd-image > div,
.gsd-container.gsd-wysiwyg > div {
  /*width: 100%;*/
}

/** two column grids **/
.gsd-container.gsd-wysiwyg-wysiwyg > div > div,
.gsd-container.gsd-wysiwyg-image > div > div,
.gsd-container.gsd-image-wysiwyg > div > div,
.gsd-container.gsd-image-image > div > div {
  width: 50%;
}

/** three column grids **/
.gsd-container.gsd-wysiwyg-wysiwyg-wysiwyg > div > div,
.gsd-container.gsd-image-image-image > div > div {
  flex: 33.33%;
}

/** some default styling **/
.gsdblock-image-block img {
  max-width: 100%;
}
.gsdblock-image-block.image-block-backgroundimage {
  min-height: 250px;
}

.image-block-backgroundimage {
  background-size: cover;
  background-position: center;
}

.gsd-wysiwyg .gsdblock-wysiwyg-block {
  padding: 0 15px;
  width: 94%;
}


.gsd-wysiwyg .gsdblock-wysiwyg-block p:last-child {
  margin-block-end: 0;
}

.gsd-wysiwyg-image .gsdblock-wysiwyg-block {
  margin-right: 50px;
}

.gsd-image-wysiwyg .gsdblock-wysiwyg-block {
  margin-left: 50px;
}

.gsdblock-wysiwyg-block h1 {
  font-size: 4rem;
  line-height: 5rem;
  margin-top: 0;
}

.gsdblock-wysiwyg-block h2 {
  font-size: 3rem;
  line-height: 4rem;
  margin-top: 0;
}

.gsdblock-wysiwyg-block h3 {
  font-size: 2rem;
  line-height: 3rem;
  margin-top: 0;
}

.image-block-title {
  padding: 10px 15px;
  font-weight: bold;
  background-color: #ebebeb;
}

.image-block-description {
  padding: 10px 15px;
  border: 1px solid #ebebeb;
}

.text-middle-width > div {
  max-width: 760px;
}

/** responsive **/
@media (max-width: 767px) {

  .gsd-container > div {
    flex-direction: column;
    padding: 0;
    align-items: center;
    gap: 20px;
  }

  /** two column grids **/
  .gsd-container.gsd-wysiwyg-wysiwyg > div > div,
  .gsd-container.gsd-wysiwyg-image > div > div,
  .gsd-container.gsd-image-wysiwyg > div > div,
  .gsd-container.gsd-image-image > div > div {
    width: 94%;
  }

  /** three column grids **/
  .gsd-container.gsd-wysiwyg-wysiwyg-wysiwyg > div > div,
  .gsd-container.gsd-image-image-image > div > div {
    flex: 100%;
  }

  /** if text-image reverse order on mobile **/
  .gsd-container.gsd-wysiwyg-image > div {
    flex-direction: column-reverse;
  }

}