<?php

  namespace domain\workday\service;

  use classes\TimesTrait;
  use Config;
  use GsdMailer;
  use IndufastCalendarEvent;
  use IndufastWorkday;

  class WorkdayMailer {

    use TimesTrait;

    public function send(): void {
      try {
        // Get all active employees
        $employees = \IndufastEmployee::find_all();
        $activeEmployees = \IndufastEmployee::filterActiveEmployees($employees);

        // Calculate date range for last week (Monday to Sunday)
        $endDate = date('Y-m-d', strtotime('last Sunday'));
        $startDate = date('Y-m-d', strtotime('last Monday', strtotime($endDate)));

        foreach ($activeEmployees as $employee) {
          $this->sendEmployeeWeeklyReport($employee, $startDate, $endDate);
        }
      }
      catch (\Exception $e) {
        dump($e->getMessage());
      }
    }

    /**
     * @throws \GsdException
     * @throws \Exception
     */
    private function sendEmployeeWeeklyReport(\IndufastEmployee $employee, string $startDate, string $endDate): void {
      // Get workdays for the week.
      if (!$workdays = \AppModel::mapObjectIds(IndufastWorkday::find_all_by([
        'employee_id' => $employee->id,
        'status'      => IndufastWorkday::STATUS_PROCESSED,
      ], "AND date BETWEEN '$startDate' AND '$endDate'"), 'date')) {
        return;
      }

      // Calculate workdays and ensure we have data for each day.
      foreach ($workdays as $workday) {
        $workday->calculate();
      }

      // Generate email content.
      $emailContent = $this->generateEmailContent($employee, $startDate, $endDate, $workdays);

      // Send email using GsdMailer.
      $weekNumber = date('W', strtotime($startDate));
      $subject = "Indufast - Overzicht uren week $weekNumber";

      $mailTo = Config::get('EMAIL_TEST_MODE', true) ? Config::get('EMAIL_TEST_MAIL_TO') : $employee->extra_email;
      $mailer = GsdMailer::build($mailTo, $subject, $emailContent);
      $mailer->setFrom(Config::get('WEEKLY_HOURS_EMAIL_FROM'));
      $mailer->send();
    }

    private function generateEmailContent(\IndufastEmployee $employee, string $startDate, string $endDate, array $workdaysByDate): string {
      $weekNumber = date('W', strtotime($startDate));
      $formattedStartDate = date('d-m-Y', strtotime($startDate));
      $formattedEndDate = date('d-m-Y', strtotime($endDate));

      // Generate table rows
      $tableRows = $this->generateTableRows($workdaysByDate, $startDate, $endDate);

      return <<<HTML
          <!DOCTYPE html>
          <html lang="nl">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Overzicht uren week $weekNumber</title>
          </head>
          <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
              <div style="padding: 20px; font-size: 14px; line-height: 1.6; margin-bottom: 30px;">
                  <p>Beste $employee->name,</p>
                  
                  <p>Hierbij een overzicht van de uren van vorige week. Mochten er in deze lijst zaken niet kloppen dan vernemen we dit graag van je.</p>
                  
                  <p><strong>Contactpersoon bij Indufast is de vrouw die het allemaal regelt: Jolanda van Kruijsdijk</strong><br>
                  Te bereiken tussen 08.15 en 16.30 uur op: <strong>0497-229116</strong> of als je liever mailt: <a href="mailto:<EMAIL>"><strong><EMAIL></strong></a></p>
                  
                  <p style="color: #ff6b6b; font-weight: bold;">** DIT BERICHT IS AUTOMATISCH GEGENEREERD DOOR DAT KLEINE COMPUTERMANNETJE, IMMERS EEN DAG HEEFT 24 UUR **</p>
              </div>
              
              <div style="max-width: 1000px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                  <div style="text-align: center; margin-bottom: 30px;">
                      <img src="{$this->getLogoBase64()}" alt="Indufast Logo" style="max-height: 60px; height: auto;">
                  </div>

                  <h1 style="color: #353535; margin-bottom: 20px; border-bottom: 3px solid #dd0015; padding-bottom: 10px;">
                      Overzicht uren week $weekNumber
                  </h1>
                  
                  <div style="margin-bottom: 20px;">
                      <p><strong>Medewerker:</strong> $employee->name</p>
                      <p><strong>Periode:</strong> $formattedStartDate - $formattedEndDate</p>
                  </div>
          
                  <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                      <thead>
                          <tr style="background-color: #dd0015; color: white;">
                              <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Dag</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Uren 100%</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Uren 125%</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Uren 150%</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Uren 200%</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Netto reisuren</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Verlof</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Bijzonder verlof</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Ziek</th>
                              <th style="padding: 12px; text-align: right; border: 1px solid #ddd;">Ongeoorloofd afwezig</th>
                          </tr>
                      </thead>
                      <tbody>
                          $tableRows
                      </tbody>
                  </table>
              </div>
          </body>
          </html>
          HTML;
    }

    private function generateTableRows(array $workdaysByDate, string $startDate, string $endDate): string {
      $rows = '';
      $current = strtotime($startDate);
      $end = strtotime($endDate);

      // Arrays to collect times for totals
      $totalNormalHours = [];
      $total125Hours = [];
      $total150Hours = [];
      $total200Hours = [];
      $totalTravelHours = [];
      $totalLeaveHours = [];
      $totalSpecialLeaveHours = [];
      $totalSickHours = [];
      $totalUnexcusedLeaveHours = [];

      while ($current <= $end) {
        $date = date('Y-m-d', $current);
        $dayName = $this->getDutchDayName(date('w', $current));
        $formattedDate = date('d-m', $current);

        /** @var IndufastWorkday $workday */
        $workday = $workdaysByDate[$date] ?? null;
        if ($workday) {
          $travelHours = $workday->travelDurationNet !== '00:00:00' ? $workday->travelDurationNet : '';
          $leaveHours = $workday->specialHoursLeave !== '00:00:00' ? $workday->specialHoursLeave : '';
          $specialLeaveHours = $workday->specialHoursSpecialLeave !== '00:00:00' ? $workday->specialHoursSpecialLeave : '';
          $sickHours = $workday->specialHoursSick !== '00:00:00' ? $workday->specialHoursSick : '';
          $unexcusedLeaveHours = $workday->specialHoursUnexcusedLeave !== '00:00:00' ? $workday->specialHoursUnexcusedLeave : '';
          $dayOfWeek = date('N', strtotime($date));

          // Saturday - all hours are 150%
          if ($dayOfWeek == 6) {
            $hours150 = $workday->workdayDurationSaturdayNet !== '00:00:00' ? $workday->workdayDurationSaturdayNet : '';
          }

          // Sunday or Holiday - all hours are 200%
          elseif ($dayOfWeek == 7 || in_array($date, IndufastCalendarEvent::holidays())) {
            $hours200 = $workday->workdayDurationSundayOrHolidayNet !== '00:00:00' ? $workday->workdayDurationSundayOrHolidayNet : '';
          }

          // Weekday - calculate normal, 125%, and 150% hours
          else {
            $normalHours = $workday->workdayDurationNetClipped !== '00:00:00' ? $workday->workdayDurationNetClipped : '';

            // 125% hours: overtimeBelow + workdayOutsideWorkingHours
            $hours125Parts = array_filter([
              $workday->overtimeBelow !== '00:00:00' ? $workday->overtimeBelow : '',
              $workday->workdayOutsideWorkingHours !== '00:00:00' ? $workday->workdayOutsideWorkingHours : '',
            ]);
            $hours125 = $hours125Parts ? $this->addTimes($hours125Parts) : '';

            // 150% hours: overtimeAbove
            $hours150 = $workday->overtimeAbove !== '00:00:00' ? $workday->overtimeAbove : '';
          }

          $workHours = $normalHours ?? '';
          $overtime125Hours = $hours125 ?? '';
          $overtime150Hours = $hours150 ?? '';
          $overtime200Hours = $hours200 ?? '';

          if ($workHours) $totalNormalHours[] = $workHours;
          if ($overtime125Hours) $total125Hours[] = $overtime125Hours;
          if ($overtime150Hours) $total150Hours[] = $overtime150Hours;
          if ($overtime200Hours) $total200Hours[] = $overtime200Hours;
          if ($travelHours) $totalTravelHours[] = $travelHours;
          if ($leaveHours) $totalLeaveHours[] = $leaveHours;
          if ($specialLeaveHours) $totalSpecialLeaveHours[] = $specialLeaveHours;
          if ($sickHours) $totalSickHours[] = $sickHours;
          if ($unexcusedLeaveHours) $totalUnexcusedLeaveHours[] = $unexcusedLeaveHours;

          $rows .= sprintf(
            '<tr style="border-bottom: 1px solid #eee;">
                      <td style="padding: 10px; border: 1px solid #ddd;">%s %s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                      <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">%s</td>
                  </tr>',
            $dayName,
            $formattedDate,
            $workHours,
            $overtime125Hours,
            $overtime150Hours,
            $overtime200Hours,
            $travelHours,
            $leaveHours,
            $specialLeaveHours,
            $sickHours,
            $unexcusedLeaveHours
          );
        }
        $current = strtotime('+1 day', $current);
      }

      $totalNormalTime = $totalNormalHours ? $this->addTimes($totalNormalHours) : '';
      $total125Time = $total125Hours ? $this->addTimes($total125Hours) : '';
      $total150Time = $total150Hours ? $this->addTimes($total150Hours) : '';
      $total200Time = $total200Hours ? $this->addTimes($total200Hours) : '';
      $totalTravelTime = $totalTravelHours ? $this->addTimes($totalTravelHours) : '';
      $totalLeaveTime = $totalLeaveHours ? $this->addTimes($totalLeaveHours) : '';
      $totalSpecialLeaveTime = $totalSpecialLeaveHours ? $this->addTimes($totalSpecialLeaveHours) : '';
      $totalSickTime = $totalSickHours ? $this->addTimes($totalSickHours) : '';
      $totalUnexcusedLeaveTime = $totalUnexcusedLeaveHours ? $this->addTimes($totalUnexcusedLeaveHours) : '';

      $rows .= sprintf(
        '<tr style="border-top: 3px solid #dd0015; background-color: #f8f9fa; font-weight: bold;">
                  <td style="padding: 12px; border: 1px solid #ddd; color: #353535;">Totaal</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
                  <td style="padding: 12px; text-align: right; border: 1px solid #ddd; color: #353535;">%s</td>
              </tr>',
        $totalNormalTime,
        $total125Time,
        $total150Time,
        $total200Time,
        $totalTravelTime,
        $totalLeaveTime,
        $totalSpecialLeaveTime,
        $totalSickTime,
        $totalUnexcusedLeaveTime,
      );

      return $rows;
    }

    private function getDutchDayName(int $dayOfWeek): string {
      $days = ['Zondag', 'Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrijdag', 'Zaterdag'];
      return $days[$dayOfWeek];
    }

    private function getLogoBase64(): string {
      $logoPath = __DIR__ . '/../../../templates/portal/images/logo.png';

      if (!file_exists($logoPath)) {
        return '';
      }

      $imageData = file_get_contents($logoPath);
      $base64 = base64_encode($imageData);
      $mimeType = 'image/png';

      return "data:$mimeType;base64,$base64";
    }

  }