<?php

namespace domain\google\service;

use Google\Client;
use Google\Service\Oauth2;

class GoogleAuthService {
  private Client $client;
  private string $redirectUri;
  private const array SCOPES = [
    'email',
    'profile',
    'https://www.googleapis.com/auth/drive.readonly',
  ];

  public function __construct() {
    $this->client = new Client();

    // Client JSON secret from Google cloud console
    $this->client->setAuthConfig(\Config::get('GOOGLE_API_OAUTH_CONFIG'));
    $this->client->addScope(self::SCOPES);

    $host = $_SERVER['HTTP_HOST'];

    // In development, use localhost redirect URI
    $this->redirectUri = DEVELOPMENT ?
      "https://localhost/redirect/$host/nl/api/googleAuthCallback" :
      "https://$host/nl/api/googleAuthCallback";

    $this->client->setRedirectUri($this->redirectUri);
  }

  public function getAuthUrl(): string {
    return $this->client->createAuthUrl(self::SCOPES);
  }

  public function getOAuthUser(string $code): Oauth2\Userinfo {
    try {
      // Exchange authorization code for access token
      $token = $this->client->fetchAccessTokenWithAuthCode($code);
      $this->client->setAccessToken($token);

      // Get user info
      $oauth2 = new Oauth2($this->client);
      return $oauth2->userinfo->get();
    } catch (\Exception $e) {
      throw new \Exception('Failed to authenticate with Google: ' . $e->getMessage());
    }
  }

  public function getToken(): ?array {
    $token = $this->client->getAccessToken();

    if (!$token) return null;

    return [
      'access_token' => $token['access_token'],
      'expires_at' => time() + ($token['expires_in'] ?? 3600),
      'expires_in' => $token['expires_in'] ?? 3600
    ];
  }

  public function getGooglePickerConfig(): array {
    $config = json_decode(file_get_contents(\Config::get('GOOGLE_API_PICKER_CONFIG')), true);
    $token = $this->getToken();
    $config['token'] = $token ? $token['access_token'] : null;
    $config['locale'] = $_SESSION['lang'] ?? 'nl';
    return $config;
  }
} 