// Plugins
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import Fonts from 'unplugin-fonts/vite'
import Layouts from 'vite-plugin-vue-layouts'
import Vue from '@vitejs/plugin-vue'
import VueRouter from 'unplugin-vue-router/vite'
import Vuetify, { transformAssetUrls } from 'vite-plugin-vuetify'

// Utilities
import { defineConfig } from 'vite'
import { fileURLToPath, URL } from 'node:url'

const skipHash = [
  "background.jpg",
  "logo.png",
  "logo_large.png",
  "materialdesignicons-webfont.eot",
  "materialdesignicons-webfont.ttf",
  "materialdesignicons-webfont.woff",
  "materialdesignicons-webfont.woff2",
];

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    VueRouter(),
    Layouts(),
    Vue({
      template: {
        transformAssetUrls,
        compilerOptions: {
          isCustomElement: (tag) => ['drive-picker', 'drive-picker-docs-view'].includes(tag),
        }
      },
    }),
    // https://github.com/vuetifyjs/vuetify-loader/tree/master/packages/vite-plugin#readme
    Vuetify({
      autoImport: true,
      styles: {
        configFile: 'src/styles/settings.scss',
      },
    }),
    Components(),
    Fonts({
      google: {
        families: [{
          name: 'Poppins',
          styles: 'wght@400;500;700',
        }],
      },
    }),
    AutoImport({
      imports: [
        'vue',
        'vue-router',
      ],
      eslintrc: {
        enabled: true,
      },
      vueTemplate: true,
    }),
  ],
  define: { 'process.env': {} },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@gsdvuefw': fileURLToPath(new URL('./../../../../gsdfw/resources/vueapps/gsdvuefw', import.meta.url)),
      '@vueapps': fileURLToPath(new URL('./node_modules/', import.meta.url)),
    },
    extensions: [
      '.js',
      '.json',
      '.jsx',
      '.mjs',
      '.ts',
      '.tsx',
      '.vue',
    ],
  },
  server: {
    port: 3000,
    cors: true,
  },
  css: {
    preprocessorOptions: {
      sass: {
        api: 'modern-compiler',
      },
    },
  },
  base: '',
  build: {
    // Output folder for build files.
    outDir: __dirname + '../../../../templates/portal/dist',
    emptyOutDir: true,

    // AssetsDir is relative to outDir.
    assetsDir: 'assets',

    // Minify based on environment.
    minify: process.env.NODE_ENV !== 'development',
    rollupOptions: {
      output: {
        assetFileNames: function (file) {
          return skipHash.includes(file.name)
            ? `assets/[name].[ext]`
            : `assets/[name]-[hash].[ext]`;
        },
      },
    },
  },
})
