{"name": "portal", "private": true, "type": "module", "version": "*******", "scripts": {"dev": "vite", "build": "vite build", "build-dev": "vite build --mode development", "preview": "vite preview", "lint": "eslint . --fix"}, "dependencies": {"@googleworkspace/drive-picker-element": "^0.5.1", "@mdi/font": "7.4.47", "@schedule-x/calendar-controls": "^2.32.0", "@schedule-x/events-service": "^2.32.0", "@schedule-x/theme-default": "^2.32.0", "@schedule-x/vue": "^2.30.0", "@tinymce/tinymce-vue": "^6.1.0", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/core": "^13.0.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "maska": "^3.1.0", "pinia-plugin-persistedstate": "^4.2.0", "roboto-fontface": "*", "tinymce": "^7.9.0", "vue": "^3.4.31", "vue-page-title": "^2.2.2", "vuetify": "^3.6.14"}, "devDependencies": {"@eslint/js": "^9.14.0", "@vitejs/plugin-vue": "^5.0.5", "eslint": "^9.14.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.4.0", "eslint-plugin-vue": "^9.30.0", "pinia": "^2.1.7", "sass": "1.77.8", "sass-embedded": "^1.77.8", "unplugin-auto-import": "^0.17.6", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^0.27.2", "unplugin-vue-router": "^0.10.0", "vite": "^5.4.0", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vuetify": "^2.0.3", "vue-router": "^4.4.0"}}