import { onBeforeRouteLeave } from 'vue-router'
import { useEventListener } from '@vueuse/core'

export function useUnsavedChangesPrompt(hasUnsavedChangesRef, message = 'Er zijn nog wijzigingen die niet zijn opgeslagen. Weet je zeker dat je de pagina wil verlaten?') {
  const preventClose = (event) => {
    if (hasUnsavedChangesRef.value) {
      event.preventDefault()
      event.returnValue = ''
    }
  }

  // native beforeunload event
  useEventListener(window, 'beforeunload', preventClose)

  // vue router navigation guard
  onBeforeRouteLeave((_to, _from, next) => {
    if (hasUnsavedChangesRef.value) {
      if (confirm(message)) {
        next()
      }
    } else {
      next()
    }
  })
}
