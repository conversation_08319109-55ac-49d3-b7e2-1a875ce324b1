<script setup>
import {definePage} from "unplugin-vue-router/runtime";
import {useAuthenticationStore} from '@/stores/authentication.js';
import {onMounted, ref} from "vue";
import createApiService from "@/services/api.js";
import {useRouter} from "vue-router";
import AccredisImportResultDialog from "@/components/dashboard/accredisImportResultDialog.vue";

const router = useRouter();
const api = createApiService(router);
const dashboard = ref([]);
const showAccredisDialog = ref(false);

definePage({
  meta: {
    title: 'Dashboard',
    requiresAuth: true,
  },
})

onMounted(() => {
  updateDashboard();
});

const updateDashboard = () => {
  api
    .get("dashboard")
    .then((response) => {
      dashboard.value = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });
}

const accredisHasErrors = () => {
  return dashboard.value.last_accredis_import_result && dashboard.value.last_accredis_import_result.some(item => item.errors && item.errors.length > 0);
}

const authStore = useAuthenticationStore();
</script>

<template>
  <accredis-import-result-dialog
    v-model="showAccredisDialog"
    :dashboard="dashboard"
  />

  <v-card>
    <v-card-title>
      Welkom bij de Indufast Portal, {{ authStore.getCurrentUserName() }}
    </v-card-title>
    <v-card-text>
      <p>Het is vandaag <b>{{ $filters.formatDate(new Date()) }}</b>.</p>
      <v-container
        fluid
        class="pa-0 mt-4"
      >
        <v-table class="dashboard">
          <tr>
            <th>
              Aantal projecten vandaag
            </th>
            <td>
              {{ dashboard.events_today }}
            </td>
          </tr>
          <tr>
            <th>
              Laatste import Accredis-data
            </th>
            <td>
              {{ $filters.ucFirst($filters.formatDate(dashboard.last_accredis_import)) }}
              <v-icon
                v-if="dashboard.last_accredis_import"
                class="ml-2"
                :icon="accredisHasErrors() ? 'mdi-close-octagon' : 'mdi-information'"
                :color="accredisHasErrors() ? 'error' : 'primary'"
                @click="showAccredisDialog = true"
              />
            </td>
          </tr>
          <tr>
            <th>
              Aantal te controleren werkdagen
            </th>
            <td>
              {{ dashboard.open_workdays }}
            </td>
          </tr>
        </v-table>
      </v-container>
    </v-card-text>
  </v-card>
</template>

<style lang="scss">
.dashboard table {
  width: 500px !important;

  th {
    text-align: left;
    padding-right: 15px;
    white-space: nowrap;
  }

  td {
    white-space: nowrap;
  }
}

</style>
