<script setup>
import {onMounted} from "vue";
import {useAuthenticationStore} from "@/stores/authentication.js";
import {useConfigStore} from "@/stores/config.js";
import {useRouter} from "vue-router";
import createApiService from "@/services/api.js";

const authStore = useAuthenticationStore();
const configStore = useConfigStore();
const router = useRouter();
const api = createApiService(router);

onMounted(() => {
  api.post("/logout").then(() => {
    authStore.logOut();
    configStore.clearConfig();
    router.push({name: "/"});
  })
});
</script>
