<script setup>
import {computed, onMounted, onUnmounted, ref, watch} from "vue";
import ProjectDetails from "@/components/planning/ProjectDetails.vue";
import Projects from "@/components/planning/Projects.vue";
import MultiDay from "@/components/planning/MultiDay.vue";
import createApiService from "@/services/api.js";
import {onBeforeRouteLeave, useRoute, useRouter} from "vue-router";
import {definePage} from "unplugin-vue-router/runtime";
import {format} from "date-fns";
import {industryTypes, employeeAvailability, plannable} from "@/helpers/constants.js";
import {useSnackbarStore} from "@/stores/snackbar.js";
import EmployeeDetails from "@/components/planning/EmployeeDetails.vue";
import {useAuthenticationStore} from "@/stores/authentication.js";
import {isEventInPast} from "@/helpers/helpers.js";
import {useUnsavedChangesPrompt} from "@/composables/useUnsavedChangesPrompt.js";

const authStore = useAuthenticationStore();
const router = useRouter();
const route = useRoute();
const loading = ref({
  projects: true,
  employees: true,
  save_all: false,
});
const employees = ref([]);
const displayedEmployee = ref();
const showEmployeeDialog = ref(false);
const api = createApiService(router);
const projects = ref([]);
const draggedEventEmployee = ref({});
const sourceCalendarEvent = ref({});
const snackbarStore = useSnackbarStore();
const changedCalendarEvents = ref([]);
const date = ref(route.params.date?.length ? route.params.date : format(new Date(), 'yyyy-MM-dd'));
const availabilityInterval = setInterval(() => {
  updateAvailability(false);
}, 30_000);
const multiDayDialog = ref(false);
const multiDayProject = ref({});
const multiDayTargetEvent = ref({});
const multiDayEmployee = ref({});

definePage({
  name: "/planning",
  meta: {
    title: 'Planning',
  },
})

onMounted(() => {
  updateEmployees()
  updateProjects()
});

onBeforeRouteLeave((to, from, next) => {
  if (hasUnsavedChanges.value) {
    if (confirm('Er zijn nog wijzigingen die niet zijn opgeslagen. Weet je zeker dat je de pagina wil verlaten?')) {
      next();
    }
  }
  else {
    next();
  }
});

onUnmounted(() => {
  if (availabilityInterval) {
    clearInterval(availabilityInterval);
  }

  window.removeEventListener("beforeunload", preventClose);
});

const preventClose = (event) => {
  if (changedCalendarEvents.value.length) {
    event.preventDefault();
    event.returnValue = '';
  }
};

window.addEventListener('beforeunload', preventClose);

const updateEmployees = () => {
  api
    .get("employeeList?" + new URLSearchParams({
      active: true,
      sort: 'rank',
    }).toString())
    .then((response) => {
      employees.value = response.data.data;
      loading.value.employees = false;
      updateAvailability();
    })
    .catch((error) => {
      loading.value.employees = false;
      snackbarStore.showMessage("Onbekende fout bij het ophalen van de medewerkers.", "error");
      console.log(error);
    });
}

const updateAvailability = (showLoading = true) => {
  if (showLoading) {
    loading.value.employees = true;
    for (let i in employees.value) {
      employees.value[i]._availability = 'unknown';
    }
  }

  api
    .get("employeeAvailabilityList?" + new URLSearchParams({
      date: date.value,
    }).toString())
    .then((response) => {
      const events = projects.value.flatMap(project => project.events);
      for (let i in response.data.data) {
        const employee = employees.value.find((employee) => employee.id === response.data.data[i].employee.id);
        if (employee) {
          employee._availability = response.data.data[i].availability;
          employee._events = response.data.data[i].events;
          setEmployeeStatus(employee, events);
        }
      }
      loading.value.employees = false;
      detectAllConflicts();
    })
    .catch((error) => {
      loading.value.employees = false;
      snackbarStore.showMessage("Onbekende fout bij het ophalen van de beschikbaarheid.", "error");
      console.log(error);
    });
}

const setEmployeeStatus = (employee, events) => {
  for (const event of events) {
    const googleEvent = employee._events?.find(e => e.id === event.google_calendar_event_id);
    const eventEmployee = event.employees?.find(e => e.employee.id === employee.id);
    if (eventEmployee) {
      eventEmployee.responseStatus = googleEvent?.responseStatus;
    }
  }
};

const setEmployeeConflict = (employee, calendarEvent, conflictEvent) => {
  // Vind de betreffende eventEmployee in de conflicterende event
  const eventEmployee = calendarEvent.employees.find(e => e.employee.id === employee.id);

  if (eventEmployee) {
    eventEmployee.conflict ??= { internal: !!conflictEvent };

    const project = findProjectByEvent(calendarEvent.id);
    if (project && project.plannable !== plannable.NOT_PLANNABLE_EXTERNAL) {
      project.plannable = plannable.NOT_PLANNABLE_INTERNAL;
    }
  }

  if (!conflictEvent) return;

  // Als we ook de conflicterende event weten, markeer die eventEmployee ook
  const conflictEventEmployee = conflictEvent.employees.find(e => e.employee.id === employee.id);

  if (conflictEventEmployee) {
    conflictEventEmployee.conflict ??= { internal: true };

    const project = findProjectByEvent(conflictEvent.id);
    if (project && project.plannable !== plannable.NOT_PLANNABLE_EXTERNAL) {
      project.plannable = plannable.NOT_PLANNABLE_INTERNAL;
    }
  }
};

const updateInternalAvailability = (employee, detectConflicts = false) => {
  if (employee._availability === 'not_available' || employee._availability === 'unknown') {
    return employee._availability;
  }

  let available_morning = employee._availability !== 'available_afternoon';
  let available_afternoon = employee._availability !== 'available_morning';

  const allEvents = filterEvents(projects.value.flatMap(project => project.events));

  // filter out only the events where the employee was planned on and didn't decline
  let calendarEvents = allEvents.filter(event => event.employees.some(e => e.employee.id === employee.id && e.responseStatus !== 'declined'));
  let morningEvent = null;
  let afternoonEvent = null;
  let wholeDayEvent = null;

  if (calendarEvents.length) {
    for (let j in calendarEvents) {
      const currentEvent = calendarEvents[j];

      // Skip current dragged event when calculating availability
      if (Object.keys(draggedEventEmployee.value).length &&
        employee.id === draggedEventEmployee.value.employee.id &&
        sourceCalendarEvent.value &&
        sourceCalendarEvent.value.id === currentEvent.id) {
        continue;
      }

      switch (calendarEvents[j].day_part) {
        case 'morning':
          if (!available_morning && detectConflicts) {
            setEmployeeConflict(employee, currentEvent, morningEvent || wholeDayEvent);
          }
          available_morning = false;
          morningEvent = currentEvent;
          break;

        case 'afternoon':
          if (!available_afternoon && detectConflicts) {
            setEmployeeConflict(employee, currentEvent, afternoonEvent || wholeDayEvent);
          }
          available_afternoon = false;
          afternoonEvent = currentEvent;
          break;

        case 'whole_day':
          if ((!available_morning || !available_afternoon) && detectConflicts) {
            setEmployeeConflict(employee, currentEvent, morningEvent || afternoonEvent || wholeDayEvent);
          }
          available_morning = false;
          available_afternoon = false;
          wholeDayEvent = currentEvent;
          break;
      }
    }

    if (!available_morning && !available_afternoon) {
      return 'not_available';
    }
    else if (available_morning && available_afternoon) {
      return 'available';
    }
    else if (available_morning) {
      return 'available_morning';
    }
    else if (available_afternoon) {
      return 'available_afternoon';
    }
  }

  return employee._availability;
}

const updateProjects = () => {
  if (!date.value) {
    return;
  }

  if (hasUnsavedChanges.value) {
    if (!confirm('Er zijn nog wijzigingen die niet zijn opgeslagen. Weet je zeker dat je de projecten wil verversen?')) {
      return;
    }
  }

  loading.value.projects = true;
  api
    .get("projectList?" + new URLSearchParams({
      archive: false,
      date: date.value,
    }).toString())
    .then((response) => {
      loading.value.projects = false;
      projects.value = response.data.data;
      changedCalendarEvents.value = [];
      detectAllConflicts();
    })
    .catch((error) => {
      loading.value = false;
      console.log(error);
    });
}

const filterEvents = (events, workOnly = false) => {
  return events.filter((event) => {
    return (workOnly ? event.type === 'work' : true) && format(event.start, 'yyyy-MM-dd') === date.value;
  });
};

const calendarEventHasEmployee = (calendarEvent, employee) => {
  return calendarEvent.employees.some((w) => w.employee.id === employee.id);
};

const sortEmployeesByRank = (calendarEvent) => {
  calendarEvent.employees.sort((a, b) => {

    // Compare ranks alphabetically.
    if (a.employee.rank !== b.employee.rank) {
      return a.employee.rank.localeCompare(b.employee.rank);
    }

    // If ranks are the same, compare rank numbers numerically.
    return a.employee.rank_number - b.employee.rank_number;
  });
};

const onDragStart = (eventEmployee, calendarEvent, event) => {
  draggedEventEmployee.value = eventEmployee;
  sourceCalendarEvent.value = calendarEvent;
  event.target.classList.add("dragging");
};

const onDragEnd = (event) => {
  draggedEventEmployee.value = {};
  sourceCalendarEvent.value = {};
  event.target.classList.remove("dragging");
};

const checkAvailability = (targetCalendarEvent, employee, showMessages = false) => {
  const availability = updateInternalAvailability(employee, true);

  if (calendarEventHasEmployee(targetCalendarEvent, employee)) {
    if (showMessages && sourceCalendarEvent.value?.id !== targetCalendarEvent.id) {
      snackbarStore.showMessage(`${employee.name} is al aan dit project gekoppeld.`, 'error');
    }
    return false;
  }

  let result = true;
  const isMorning = targetCalendarEvent.day_part === 'morning';
  const isAfternoon = targetCalendarEvent.day_part === 'afternoon';

  switch (availability) {
    case 'available_morning':
      result = isMorning ? true : 'warning';
      break;
    case 'available_afternoon':
      result = isAfternoon ? true : 'warning';
      break;
    case 'available_morning_afternoon':
      result = (isMorning || isAfternoon) ? true : 'warning';
      break;
    case 'not_available':
    case 'not_available_non_working_day':
      result = 'warning';
      break;
    default:
      break;
  }

  if (showMessages) {
    if (result === true) {
      snackbarStore.showMessage(`${employee.name} is aan dit project gekoppeld.`, 'success');
    } else if (result === 'warning') {
      snackbarStore.showMessage(`${employee.name} is niet beschikbaar maar toch ingepland.`, 'warning');
    } else {
      snackbarStore.showMessage(`${employee.name} is niet beschikbaar.`, 'error');
    }
  }
  return result;
};

const onDragEnter = (calendarEvent, event) => {
  if (calendarEvent.type === 'work' && calendarEvent.id !== sourceCalendarEvent.value.id) {
    // Check if the event is in the past - if so, treat it as invalid dropzone
    if (isEventInPast(calendarEvent)) {
      event.currentTarget.classList.add("drag-over-invalid");
    } else {
      const availability = checkAvailability(calendarEvent, draggedEventEmployee.value.employee);
      if (availability === true) {
        event.currentTarget.classList.add("drag-over-highlight");
      } else if (availability === 'warning') {
        event.currentTarget.classList.add("drag-over-warning");
      } else {
        event.currentTarget.classList.add("drag-over-invalid");
      }
    }
  }

  event.preventDefault();
};

const onDragLeave = (event) => {
  event.currentTarget.classList.remove("drag-over-highlight");
  event.currentTarget.classList.remove("drag-over-invalid");
  event.currentTarget.classList.remove("drag-over-warning");
};

const removeEmployeeFromEvent = (calendarEvent, eventEmployeeIndex) => {
  const removedEmployee = calendarEvent.employees.splice(eventEmployeeIndex, 1)[0];

  const project = findProjectByEvent(calendarEvent.id);
  if (project) {
    handleEmployeeRemovalFromProject(project, removedEmployee);
  }

  markCalendarEventAsChanged(calendarEvent);
  snackbarStore.showMessage(removedEmployee.employee.name + " is verwijderd van het project.", "success");

  detectAllConflicts();
};

const findProjectByEvent = (eventId) => {
  return projects.value.find((project) =>
    project.events.some((event) => event.id === eventId)
  );
};

const handleEmployeeRemovalFromProject = (project, removedEmployee) => {
  const isStillInProject = project.events.some((event) =>
    event.employees.some((employee) => employee.employee.id === removedEmployee.employee.id)
  );
  if (isStillInProject) return;

  // Remove the employee from any event where they were team lead or material loader
  project.events.forEach(event => {
    if (event.team_lead_employee_id === removedEmployee.employee.id) {
      event.team_lead_employee_id = null;
      event.team_lead = null;
    }
    if (event.material_load_employee_ids?.includes(removedEmployee.employee.id)) {
      event.material_load_employee_ids = event.material_load_employee_ids.filter(id => id !== removedEmployee.employee.id);
      event.material_loaders = event.material_loaders?.filter(loader => loader.id !== removedEmployee.employee.id) || [];
    }
  });
};

const onDrop = (targetCalendarEvent, event) => {
  onDragLeave(event);

  if (targetCalendarEvent.type !== 'work') {
    return;
  }

  // Prevent dropping on events that are in the past
  if (isEventInPast(targetCalendarEvent)) {
    snackbarStore.showMessage('Kan geen medewerkers toewijzen aan projecten in het verleden.', 'error');
    return;
  }

  if (checkAvailability(targetCalendarEvent, draggedEventEmployee.value.employee, true)) {
    draggedEventEmployee.value.calendar_event_id = targetCalendarEvent.id;
    draggedEventEmployee.value.employee_id = draggedEventEmployee.value.employee.id;
    draggedEventEmployee.value.conflict = null;
    targetCalendarEvent.employees.push(draggedEventEmployee.value);
    sortEmployeesByRank(targetCalendarEvent);

    markCalendarEventAsChanged(targetCalendarEvent);

    if (Object.keys(sourceCalendarEvent.value).length) {
      markCalendarEventAsChanged(sourceCalendarEvent.value);

      const index = sourceCalendarEvent.value.employees.findIndex(
        (w) => w.employee.id === draggedEventEmployee.value.employee_id
      );
      if (index !== -1) {
        const removedEmployee = sourceCalendarEvent.value.employees.splice(index, 1)[0];

        // Handle employee removal from the source project
        const sourceProject = findProjectByEvent(sourceCalendarEvent.value.id);
        if (sourceProject) {
          handleEmployeeRemovalFromProject(sourceProject, removedEmployee);
        }
      }
    }
    detectAllConflicts();

    // Check if the target project has multiple work events.
    const targetProject = projects.value.find(project =>
      project.events.some(event => event.id === targetCalendarEvent.id)
    );
    if (targetProject) {
      const workEvents = targetProject.events.filter(event => event.type === 'work');
      const hasAtLeastOneOtherValidEvent = workEvents.some(event =>
        event.id !== targetCalendarEvent.id && !isEventInPast(event)
      );
      if (hasAtLeastOneOtherValidEvent) {
        multiDayDialog.value = true;
        multiDayProject.value = targetProject;
        multiDayEmployee.value = draggedEventEmployee.value.employee;
        multiDayTargetEvent.value = targetCalendarEvent;
      }
    }
  }
};

const hideMultiDayDialog = () => {
  multiDayDialog.value = false;
};

const saveMultiDayChanges = (events) => {
  events.forEach((event) => {
    const targetEvent = projects.value
      .flatMap(project => project.events)
      .find(e => e.id === event.id);

    if (targetEvent) {
      targetEvent.employees.push({
        employee: multiDayEmployee.value,
        calendar_event_id: event.id,
        employee_id: multiDayEmployee.value.id,
      });

      markCalendarEventAsChanged(targetEvent);
    }
  });

  hideMultiDayDialog();
};

const detectAllConflicts = () => {
  clearResolvedInternalConflicts();
  // Loop door alle medewerkers en controleer conflicten
  employees.value.forEach(employee => {
    updateInternalAvailability(employee, true);
  });
};

const markCalendarEventAsChanged = (calendarEvent) => {
  if (!changedCalendarEvents.value.some((event) => event.id === calendarEvent.id)) {
    changedCalendarEvents.value.push(calendarEvent);
  }
};

const saveAllChanges = async () => {
  loading.value.save_all = true;

  const changedCalendarEventsCopy = [...changedCalendarEvents.value];
  try {
    const eventPromises = changedCalendarEventsCopy.map((event) => {
      const data = {
        employee_ids: event.employees.map((employee) => employee.employee.id),
        team_lead_employee_id: event.team_lead_employee_id,
        material_load_employee_ids: event.material_load_employee_ids || []
      };

      return api.post(`eventUpdate?id=${event.id}`, JSON.stringify(data))
        .then(() => {
          changedCalendarEvents.value = changedCalendarEvents.value.filter(
            (e) => e.id !== event.id
          );
        });
    });

    await Promise.all(eventPromises);

    loading.value.save_all = false;

    snackbarStore.showMessage("Alle gewijzigde projecten zijn opgeslagen.", "success");

    // Wait 5 seconds to make sure google calendar is updated
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Update employee availability after all changes are saved
    await api.put("employeeAvailabilityUpdate");

    loading.value.save_all = false;
  } catch (error) {
    snackbarStore.showMessage("Onbekende fout bij het opslaan van de gewijzigde projecten.", "error");
    loading.value.save_all = false;
    console.error(error);
  }
};

const projectIsChanged = (project) => {
  return project.events.some((event) => {
    return changedCalendarEvents.value.some((changedEvent) => {
      return event.id === changedEvent.id;
    });
  });
};

const showEmployeeDetails = (employee) => {
  displayedEmployee.value = employee;
  showEmployeeDialog.value = true;
};

const hasUnsavedChanges = computed(() => !!changedCalendarEvents.value.length);
useUnsavedChangesPrompt(hasUnsavedChanges);

watch(route, () => {
  if (route.params.date !== date.value) {
    if (hasUnsavedChanges.value) {
      if (!confirm('Er zijn nog wijzigingen die niet zijn opgeslagen. Weet je zeker dat je de pagina wil verlaten?')) {
        router.replace({ name: '/planning', params: { date: date.value } });
        return;
      }
    }

    date.value = route.params.date?.length ? route.params.date : format(new Date(), 'yyyy-MM-dd')
    changedCalendarEvents.value = [];
    updateProjects();
    updateAvailability();
  }
})

const clearResolvedInternalConflicts = () => {
  projects.value.forEach(project => {
    // Clear internal conflicts
    filterEvents(project.events)
      .flatMap(event => event.employees)
      .filter(employee => employee.conflict?.internal)
      .forEach(employee => employee.conflict = null);

    if (project.plannable === plannable.NOT_PLANNABLE_INTERNAL) {
      project.plannable = plannable.PLANNABLE;
    }
  });
};

const readonly = computed(() => !authStore.hasPermission('edit'));
</script>

<template>
  <v-card class="flex-grow-1 d-flex flex-column">
    <v-card-title class="space-between pt-4 pb-4">
      Planning voor {{ $filters.formatDate(date) }}
      <div class="d-flex ga-2">
        <v-btn
          prepend-icon="mdi-calendar"
          color="primary"
          :to="{ name: '/calendar', params: { date: date } }"
        >
          Kalender
        </v-btn>
        <v-btn
          v-if="!readonly"
          prepend-icon="mdi-content-save"
          color="primary"
          :disabled="!hasUnsavedChanges"
          :loading="loading.save_all"
          @click="saveAllChanges"
        >
          Opslaan ({{ changedCalendarEvents.length }})
        </v-btn>
      </div>
    </v-card-title>
    <v-divider />
    <v-row class="pl-3 pr-3 flex-grow-1">
      <v-col
        cols="4"
        class="section paddingSection d-flex flex-column"
      >
        <v-card-title class="pl-0 pr-0">
          Medewerkers
          <v-icon
            :disabled="loading.employees"
            color="primary"
            size="small"
            @click="updateEmployees"
          >
            mdi-refresh
          </v-icon>
          <v-progress-linear
            :active="loading.employees"
            indeterminate
            color="primary"
          />
        </v-card-title>
        <div class="flex-1-0-0 overflow-y-auto">
          <v-card
            v-for="employee in employees"
            :key="employee.id"
            :draggable="!readonly"
            :class="{ draggable: !readonly }"
            class="pa-0 mb-3"
            border
            :ripple="false"
            @dragstart="(event) => { onDragStart({ employee: employee }, {}, event); }"
            @dragend="onDragEnd"
            @click="showEmployeeDetails(employee)"
          >
            <v-card-text class="employee-wrapper pa-2">
              <span class="employee-name">
                <v-icon
                  :color="employeeAvailability.find(type => type.value === updateInternalAvailability(employee))?.color"
                  :title="employeeAvailability.find(type => type.value === updateInternalAvailability(employee))?.title"
                >
                  {{ employeeAvailability.find(type => type.value === updateInternalAvailability(employee))?.icon }}
                </v-icon>
                {{ employee.name }}
              </span>
              <span class="employee-data">
                <v-icon
                  v-for="(industry, index) in employee.industries"
                  :key="index"
                  class="mr-1"
                  :color="industryTypes.find(type => type.value === industry)?.color"
                  :title="industryTypes.find(type => type.value === industry)?.title"
                >
                  {{ industryTypes.find(type => type.value === industry)?.icon }}
                </v-icon>
                {{ employee.rank }}{{ employee.rank_number }}
              </span>
            </v-card-text>
          </v-card>
        </div>
      </v-col>

      <v-col
        cols="4"
        class="section paddingSection background d-flex flex-column"
      >
        <v-card-title class="pl-0">
          Projecten
          <v-icon
            :disabled="loading.projects"
            color="primary"
            size="small"
            @click="updateProjects"
          >
            mdi-refresh
          </v-icon>
        </v-card-title>
        <v-progress-linear
          :active="loading.projects"
          indeterminate
          color="primary"
        />
        <div class="flex-1-0-0 overflow-y-auto">
          <Projects
            :projects="projects"
            :on-drag-enter="onDragEnter"
            :on-drag-leave="onDragLeave"
            :on-drop="onDrop"
            :dragged-event-employee="draggedEventEmployee"
            :filter-events="filterEvents"
            :project-is-changed="projectIsChanged"
            :mark-calendar-event-as-changed="markCalendarEventAsChanged"
            :has-unsaved-changes="hasUnsavedChanges"
            :readonly="readonly"
            @saved="updateProjects"
          />
          <v-card-text
            v-if="!projects.length && !loading.projects"
            class="pa-0"
          >
            Geen projecten gevonden.
          </v-card-text>
        </div>
      </v-col>

      <v-col
        cols="4"
        class="section paddingSection d-flex flex-column"
      >
        <v-card-title class="pl-0">
          Details
        </v-card-title>
        <div class="flex-1-0-0 overflow-y-auto">
          <ProjectDetails
            :projects="projects"
            :employees="employees"
            :on-drag-start="onDragStart"
            :on-drag-end="onDragEnd"
            :on-drag-enter="onDragEnter"
            :on-drag-leave="onDragLeave"
            :on-drop="onDrop"
            :dragged-event-employee="draggedEventEmployee"
            :source-calendar-event="sourceCalendarEvent"
            :remove-employee-from-event="removeEmployeeFromEvent"
            :filter-events="filterEvents"
            :show-employee-details="showEmployeeDetails"
          />
        </div>
      </v-col>
    </v-row>
  </v-card>

  <employee-details
    v-model="showEmployeeDialog"
    :employee="displayedEmployee"
    :update-internal-availability="updateInternalAvailability"
  />

  <MultiDay
    :show="multiDayDialog"
    :project="multiDayProject"
    :save-multi-day-changes="saveMultiDayChanges"
    :employee="multiDayEmployee"
    :target-event="multiDayTargetEvent"
    :hide-multi-day-dialog="hideMultiDayDialog"
  />
</template>


<style lang="scss">
.draggable {
  width: 100%;
  color: black;
  cursor: grab;
}

.paddingSection {
  padding-left: 1rem;
  padding-right: 1rem;
}

.fill-height {
  padding-left: 0;
  padding-right: 0;
}

.drag-over-highlight > * {
  background-color: rgba(var(--v-theme-indufastGreen), var(--v-light-opacity)) !important;
  transition: background-color 0.2s ease-in-out !important;
}

.drag-over-invalid > * {
  background-color: rgba(var(--v-theme-indufastRed), var(--v-light-opacity)) !important;
  transition: background-color 0.2s ease-in-out !important;
}

.drag-over-warning > * {
  background-color: rgba(var(--v-theme-warning), var(--v-light-opacity)) !important;
  transition: background-color 0.2s ease-in-out !important;
}

.drag-over-warning *,
.drag-over-invalid *,
.drag-over-highlight * {
  color: rgb(var(--v-theme-on-surface)) !important;
}

.dragging {
  opacity: 0.5;
}

.employee-wrapper {
  display: flex !important;
  justify-content: space-between;
  background-color: rgb(var(--v-theme-GSDBackground));
}
</style>
