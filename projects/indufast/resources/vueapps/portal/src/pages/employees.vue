<script setup>
import {onMounted, ref, computed} from "vue";
import {useRouter} from 'vue-router';
import createApiService from '@/services/api';
import {definePage} from "unplugin-vue-router/runtime";
import {employeeTypes, industryTypes, userGroups} from "@/helpers/constants.js";
import {sortNullLast} from "@/helpers/helpers.js";
import SearchBox from "@/components/SearchBox.vue";
import EmployeeEditDialog from "@/components/employee/EmployeeEditDialog.vue";
import { useAuthenticationStore } from "@/stores/authentication.js";

const router = useRouter();
const api = createApiService(router);
const authStore = useAuthenticationStore();

const employees = ref([]);
const loading = ref(false);
const editEmployeeDialog = ref(false);
const editEmployeeData = ref({});
const search = ref('');
const archive = ref(false);

definePage({
  meta: {
    title: 'Medewerkers',
  },
})

onMounted(() => {
  updateEmployees()
});

const sortType = (a, b) => {
  return employeeTypes.find(type => type.value === a)?.title.localeCompare(employeeTypes.find(type => type.value === b)?.title);
}

const sortRank = (a, b) => {
  const aRank = a.rank ?? 'Z';
  const bRank = b.rank ?? 'Z';
  return aRank !== bRank ? aRank.localeCompare(bRank) : a.rank_number - b.rank_number;
}

const employeeHeader = [
  {title: "Naam", value: "name", sortable: true, nowrap: true, maxWidth: 350},
  {title: "Type", value: "type", cellProps: {class: "no-wrap"}, sort: sortType},
  {title: "Gebruikersgroep", value: "usergroup", cellProps: {class: "no-wrap"}},
  {title: "E-mail", value: "email", sortable: true},
  {title: "Extra e-mail", value: "extra_email", sort: sortNullLast},
  {title: "Rang", value: "rank", cellProps: {class: "no-wrap"}, sortable: true, sortRaw: sortRank},
  {title: "Branches", value: "industries", cellProps: {class: "no-wrap"}},
  {title: "Actief", value: "active", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Mag inloggen", value: "may_login", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Contactpersoon bouwplaats", value: "team_lead", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Privé", value: "private", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Accredis", value: "name_accredis", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Acties", value: "actions", cellProps: {class: "no-wrap center"}},
];

const editEmployee = (employee) => {
  editEmployeeData.value = {...employee};
  editEmployeeDialog.value = true;
}

const newEmployee = () => {
  editEmployeeData.value = {};
  editEmployeeDialog.value = true;
}

const updateEmployees = async () => {
  loading.value = true;

  try {
    const params = { archive: archive.value ? 'true' : 'false' }
    const response = await api.get("employeeList", { params });
    employees.value = response.data.data;
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
}

const readonly = computed(() => !authStore.hasPermission('edit'));
</script>

<template>
  <v-card>
    <v-card-title class="space-between pt-4 pb-4">
      <span>
        {{ archive ? 'Niet-actieve medewerkers' : 'Actieve medewerkers' }}
        <v-icon
          icon="mdi-refresh"
          size="small"
          color="primary"
          @click="updateEmployees"
        />
      </span>
      <div class="d-flex align-center ga-4">
        <search-box v-model="search" />
        <v-icon
          :title="archive ? 'Toon actieve medewerkers' : 'Toon gearchiveerde medewerkers'"
          size="small"
          @click="archive = !archive; updateEmployees()"
        >
          {{ archive ? 'mdi-archive-off' : 'mdi-archive' }}
        </v-icon>
        <v-btn
          v-if="!readonly"
          color="primary"
          prepend-icon="mdi-plus"
          @click="newEmployee"
        >
          Nieuwe medewerker
        </v-btn>
      </div>
    </v-card-title>
    <v-divider />
    <v-card-text>
      <v-data-table
        :headers="employeeHeader"
        :items="employees"
        item-key="id"
        density="compact"
        :loading="loading"
        must-sort
        :sort-by="[{key: 'name', order: 'asc'}]"
        :search="search"
        class="stickyFirstColumn stickyLastColumn rowHover"
      >
        <template #item.type="{ item }">
          {{ employeeTypes.find(type => type.value === item.type).title }}
        </template>
        <template #item.rank="{ item }">
          <span v-if="item.rank">{{ item.rank }}{{ item.rank_number || '' }}</span>
        </template>
        <template #item.industries="{ item }">
          <v-icon
            v-for="(industry, index) in item.industries"
            :key="index"
            class="mr-1"
            :color="industryTypes.find(type => type.value === industry)?.color"
            :title="industryTypes.find(type => type.value === industry)?.title"
          >
            {{ industryTypes.find(type => type.value === industry)?.icon }}
          </v-icon>
        </template>
        <template #item.private="{ item }">
          <v-icon :color="item.private ? 'indufastGreen' : 'indufastRed'">
            {{ item.private ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.team_lead="{ item }">
          <v-icon :color="item.team_lead ? 'indufastGreen' : 'indufastRed'">
            {{ item.team_lead ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.active="{ item }">
          <v-icon :color="item.active ? 'indufastGreen' : 'indufastRed'">
            {{ item.active ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.may_login="{ item }">
          <v-icon :color="item.may_login ? 'indufastGreen' : 'indufastRed'">
            {{ item.may_login ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.name_accredis="{ item }">
          <v-icon :color="item.name_accredis?.length ? 'indufastGreen' : 'indufastRed'">
            {{ item.name_accredis?.length ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.usergroup="{ item }">
          {{ userGroups.find(group => group.value === item.usergroup)?.title }}
        </template>
        <template #item.actions="{ item }">
          <v-icon @click="editEmployee(item)" color="primary">
            {{ !readonly ? 'mdi-pencil' : 'mdi-eye' }}
          </v-icon>
        </template>
      </v-data-table>
      <employee-edit-dialog
        v-model="editEmployeeDialog"
        :employee-data="editEmployeeData"
        @saved="updateEmployees"
      />
    </v-card-text>
  </v-card>
</template>
