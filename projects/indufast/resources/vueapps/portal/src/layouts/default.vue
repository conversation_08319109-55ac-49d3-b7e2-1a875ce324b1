<script setup>
import {useMagic<PERSON>eys} from "@vueuse/core";
import {watch, computed} from "vue";
import router from "@/router/index.js";
import {useRoute} from "vue-router";
import {useAuthenticationStore} from "@/stores/authentication.js";
import TopBar from "@/components/TopBar.vue";
import TheFooter from "@/components/TheFooter.vue";

const authStore = useAuthenticationStore();
const route = useRoute();
const keys = useMagicKeys();
const userStore = useAuthenticationStore();
const keyEmployees = keys['Alt+m'];
const keyPlanning = keys['Alt+p'];
const keyProjects = keys['Alt+r'];
const keyTimeRegistration = keys['Alt+t'];

const items = computed(() => {
  const baseItems = [
    {title: 'Planning', routeName: '/planning', icon: 'mdi-clipboard-account-outline', hint: 'Alt + P'},
    {title: 'Projecten', routeName: '/project/', icon: 'mdi-clipboard-check-multiple-outline', hint: 'Alt + R'},
  ];
  if (authStore.hasPermission('edit')) {
    baseItems.push({title: 'Tijdsregistratie', routeName: '/tijdsregistratie/', icon: 'mdi-clock-check-outline', hint: 'Alt + T'});
  }
  return baseItems;
});

watch(keyEmployees, (pressed) => {
  if (pressed) {
    router.push({name: '/employees'});
  }
});
watch(keyPlanning, (pressed) => {
  if (pressed && route.name !== '/planning') {
    router.push({name: '/planning'});
  }
});
watch(keyProjects, (pressed) => {
  if (pressed) {
    router.push({name: '/project/'});
  }
});
watch(keyTimeRegistration, (pressed) => {
  if (pressed) {
    router.push({name: '/tijdsregistratie/'});
  }
});

const toggleRail = () => {
  userStore.rail = !userStore.rail
}

const routeIsActive = (routeName) => {
  let routePath = router.resolve({name : routeName})?.path;
  return route.matched.some(({ path }) => path === routePath);
};
</script>

<template>
  <v-navigation-drawer
    location="left"
    permanent
    :rail="userStore.rail"
    color="GSDMenuBackground"
  >
    <div>
      <router-link :to="{name: '/'}">
        <img
          src="@/assets/logo.png"
          class="logo"
          alt="Indufast"
        >
      </router-link>
    </div>
    <div class="rail">
      <v-icon
        color="GSD"
        @click="toggleRail"
      >
        {{ userStore.rail ? 'mdi-chevron-double-right' : 'mdi-chevron-double-left' }}
      </v-icon>
    </div>
    <v-divider />
    <v-list
      density="compact"
      nav
    >
      <v-list-item
        v-for="(item, index) in items"
        :key="index"
        :prepend-icon="item.icon"
        :to="{ name: item.routeName, params: item.params }"
        color="GSD"
        :active="routeIsActive(item.routeName)"
        class="main-menu-item"
      >
        <v-list-item-title :title="item.hint">
          {{ item.title }}
        </v-list-item-title>
      </v-list-item>
    </v-list>
  </v-navigation-drawer>
  <v-main class="d-flex flex-column flex-grow-1">
    <v-container
      fluid
      class="flex-grow-1 d-flex flex-column pb-0"
    >
      <TopBar />
      <v-card
        border
        class="flex-grow-1 d-flex flex-column"
      >
        <router-view />
      </v-card>
    </v-container>
    <the-footer />
  </v-main>
</template>

<style lang="scss">
.v-navigation-drawer {
  .v-list-item--active {
    background-color: rgb(var(--v-theme-GSDMenuHighlight));

    .v-list-item__overlay {
      background-color: transparent;
    }
  }
}

.main-menu-item {
  .v-list-item-title {
    font-size: 16px;
  }

  .v-list-item__prepend {
    width: 40px;
  }
}

.rail {
  width: 100%;
  text-align: right;
  padding-right: 15px;
  padding-bottom: 5px;
}

.logo {
  max-width: 100%;
}
</style>
