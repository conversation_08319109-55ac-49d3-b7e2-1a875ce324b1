import { defineStore } from 'pinia'

export const useConfigStore = defineStore('config', {
  state: () => ({
    config: null,
    developerUrl: null,
    environment: null,
  }),
  getters: {
  },
  actions: {
    setConfig(config) {
      this.config = config;
    },
    setDeveloperUrl(url) {
      this.developerUrl = url;
    },
    setEnvironment(environment) {
      this.environment = environment;
    },
    clearConfig() {
      this.config = null;
      this.developerUrl = null;
      this.environment = null;
    }
  },
  persist: { storage: localStorage },
})
