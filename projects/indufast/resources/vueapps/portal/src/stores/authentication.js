import { defineStore } from 'pinia'
import { ROLE_PERMISSIONS } from "@/helpers/roles";

export const useAuthenticationStore = defineStore('authentication', {
  state: () => ({
    isLoggedIn: false,
    currentUser: null,
    rail: true,
    token: null,
  }),
  getters: {
    isTokenValid: (state) => {
      if (!state.token || !state.token.expires_at) return false;
      return Date.now() / 1000 < state.token.expires_at;
    },
    validToken: (state) => {
      return state.isTokenValid ? state.token?.access_token : null;
    },
    hasPermission: (state) => (permission) => {
      if (!state.currentUser || !state.currentUser.usergroup) return false;
      const perms = ROLE_PERMISSIONS[state.currentUser.usergroup] || [];
      return perms.includes(permission);
    }
  },
  actions: {
    setLoginData(user, state) {
      this.setCurrentUser(user)
      this.setIsLoggedIn(state);
    },
    setIsLoggedIn(loggedInState) {
      this.isLoggedIn = loggedInState;
    },
    setCurrentUser(user) {
      this.currentUser = user;
    },
    logOut() {
      this.setCurrentUser(null);
      this.setIsLoggedIn(false);
      this.clearToken();
    },
    getCurrentUserName() {
      return this.currentUser ? this.currentUser.firstName : '';
    },
    getCurrentUserFullName() {
      return this.currentUser ? [this.currentUser.firstName, this.currentUser.insertion, this.currentUser.lastName].join(' ') : '';
    },
    setToken(token) {
      if (!token) return;

      if (!token.expires_at) {
        token.expires_at = (Date.now() / 1000) + token.expires_in;
      }

      this.token = token;
    },
    clearToken() {
      this.token = null;
    }
  },
  persist: {storage: localStorage}, // store in localstorage
})
