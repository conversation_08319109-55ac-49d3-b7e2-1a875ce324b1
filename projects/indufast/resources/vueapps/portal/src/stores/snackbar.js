import { defineStore } from 'pinia';

export const useSnackbarStore = defineStore('snackbar', {
  state: () => ({
    message: '',
    type: '',
    visible: false,
    timeout: null,
  }),
  actions: {
    showMessage(msg, type = 'info') {
      this.message = msg;
      this.type = type;
      this.visible = true;

      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.visible = false;
      }, 5000);
    },
    hideMessage() {
      this.visible = false;
      clearTimeout(this.timeout);
    },
  },
});
