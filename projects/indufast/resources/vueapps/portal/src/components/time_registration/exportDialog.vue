<script setup>

import {onMounted, ref} from "vue";
import {useRouter} from "vue-router";
import createApiService from "@/services/api.js";
import {useSnackbarStore} from "@/stores/snackbar.js";

defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
});

const emit = defineEmits(['update:modelValue']);
const router = useRouter();
const api = createApiService(router);
const snackbarStore = useSnackbarStore();
const employees = ref([]);
const exportDate = ref([]);
const exportEmployeeId = ref();
const loading = ref(false);

const closeDialog = () => {

  emit('update:modelValue', false);
};

const fetchEmployees = async () => {
  loading.value = true;
  api
    .get("employeeList?" + new URLSearchParams({
      accredis: true,
    }).toString())
    .then((response) => {
      loading.value = false;
      employees.value = response.data.data;
    })
    .catch((error) => {
      loading.value = false;
      snackbarStore.showMessage("Onbekende fout bij het ophalen van de medewerkers.", "error");
      console.log(error);
    });
};

const downloadWorkdays = () => {
  if (exportDate.value.length !== 2) {
    snackbarStore.showMessage("Ongeldige datumselectie.", "error");
  }

  loading.value = true;

  api.get("workdayExport", {
    params: {
      start: exportDate.value[0],
      end: exportDate.value[1],
      employee_id: exportEmployeeId.value,
    },
    responseType: 'blob',
  })
    .then((response) => {
      const blob = new Blob([response.data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      const employeeName = (exportEmployeeId.value) ? employees.value.find(emp => emp.id === exportEmployeeId.value)?.name.replace(/[^a-z0-9]/gi, '_') : 'Alle-medewerkers';
      const filename = `Werkdagen_${exportDate.value[0]}_${exportDate.value[1]}_${employeeName}.xlsx`

      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      snackbarStore.showMessage("Werkdagen succesvol geëxporteerd.", 'success');
      loading.value = false;
    })
    .catch((error) => {
      if (error.status === 404) {
        snackbarStore.showMessage("Geen afgeronde werkdagen gevonden voor de opgegeven periode.", "warning");
      }
      else {
        snackbarStore.showMessage("Fout bij het downloaden van werkdagen.", "error");
      }

      loading.value = false;
      console.error("Error downloading workdays:", error);
    });
};

onMounted(() => {
  fetchEmployees();
});


</script>

<template>
  <v-dialog
    :model-value="modelValue"
    color="primary"
    width="1000px"
    scrollable
    @update:model-value="closeDialog"
  >
    <v-toolbar color="primary">
      <v-toolbar-title>Download afgeronde werkdagen</v-toolbar-title>
      <v-toolbar-items>
        <v-btn
          icon="mdi-close"
          @click="closeDialog(false)"
        />
      </v-toolbar-items>
    </v-toolbar>
    <v-card :loading="loading">
      <v-card-text>
        <v-row>
          <v-col>
            <VueDatePicker
              v-model="exportDate"
              range
              hide-offset-dates
              multi-calendars
              auto-apply
              model-type="yyyy-MM-dd"
              inline
              :enable-time-picker="false"
              week-numbers="iso"
              locale="nl-NL"
              week-num-name="#"
            />
            <p
              v-if="exportDate[0]"
              class="mt-3 space-between"
            >
              <template v-if="exportDate[0] !== exportDate[1]">
                {{ $filters.ucFirst($filters.formatDate(exportDate[0])) }} t/m {{ $filters.formatDate(exportDate[1]) }}
              </template>
              <template v-else>
                {{ $filters.ucFirst($filters.formatDate(exportDate[0])) }}
              </template>
              <v-icon
                icon="mdi-delete"
                color="indufastRed"
                @click="exportDate = []"
              />
            </p>
            <p
              v-else
              class="mt-3"
            >
              Selecteer een dag of periode.
            </p>
          </v-col>
          <v-col>
            <v-autocomplete
              v-model="exportEmployeeId"
              :items="employees"
              item-title="name"
              item-value="id"
              label="Selecteer medewerker"
              hint="Laat leeg voor alle medewerkers"
              :persistent-hint="!!exportEmployeeId"
              clearable
              autocomplete="off"
            />
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-btn
          color="primary"
          variant="elevated"
          prepend-icon="mdi-download"
          :disabled="!exportDate || exportDate.length !== 2"
          :loading="loading"
          @click="downloadWorkdays"
        >
          Download
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style>

</style>
