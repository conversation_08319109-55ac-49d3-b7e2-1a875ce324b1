<script setup>
import { ref, watch, toRaw } from 'vue'
import { vMaska } from "maska/vue"
import createApiService from "@/services/api.js";
import {useRouter} from "vue-router";
import {translateErrors} from "@/helpers/translateErrors.js";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  workday: {
    type: Object,
    default: () => ({})
  }
})

const router = useRouter();
const api = createApiService(router);
const errors = ref({});
const loading = ref(false);
const emit = defineEmits(['update:modelValue', 'saved'])

const dialog = ref(false)
const editWorkdayData = ref({})

watch(() => props.modelValue, (value) => {
  dialog.value = value
  if (value) {
    initializeData()
  }
})

watch(dialog, (newVal) => {
  emit('update:modelValue', newVal)
})

const initializeData = () => {
  errors.value = {};
  editWorkdayData.value = structuredClone(toRaw(props.workday))
}

const close = () => {
  dialog.value = false
}

const save = async () => {
  loading.value = true;
  try {
    const json = { ...editWorkdayData.value }

    // convert work_time and break_time to HH:MM:SS
    if (json.work_time) json.work_time += ':00'
    if (json.break_time) json.break_time += ':00'

    const response = await api.post(`workdayUpdate?id=${props.workday.id}`, JSON.stringify(json))
    emit('saved', response.data.data)
    close()
  } catch (error) {
    errors.value = error.response?.data?.data;
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <v-dialog
    v-model="dialog"
    max-width="500"
  >
    <v-card>
      <v-card-title class="bg-primary">
        Werkdag bewerken - {{ $filters.formatDate(workday?.date) }}
      </v-card-title>
      <v-card-text class="pt-4">
        <v-text-field
          v-model="editWorkdayData.work_time"
          v-maska="'##:##'"
          label="Werktijd"
          placeholder="08:00"
          prepend-icon="mdi-clock-outline"
          clearable
          hint="Bruto werktijd inclusief pauze"
          persistent-hint
          class="mb-5"
          :error-messages="translateErrors(errors.work_time, 'Werktijd')"
        />
        <v-text-field
          v-model="editWorkdayData.break_time"
          v-maska="'##:##'"
          label="Pauzetijd"
          placeholder="00:30"
          prepend-icon="mdi-coffee"
          hint="Laat leeg om automatisch te berekenen"
          persistent-hint
          clearable
          :error-messages="translateErrors(errors.break_time, 'Pauzetijd')"
        />
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn
          text="Annuleren"
          @click="close"
        />
        <v-btn
          text="Opslaan"
          color="primary"
          variant="elevated"
          :loading="loading"
          @click="save"
        />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
