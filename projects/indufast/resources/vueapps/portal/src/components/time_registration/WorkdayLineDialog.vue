<script setup>
import {ref, watch} from 'vue'
import {vMaska} from "maska/vue"
import createApiService from "@/services/api.js";
import {useRouter} from "vue-router";
import {translateErrors} from "@/helpers/translateErrors.js";
import {useSnackbarStore} from "@/stores/snackbar.js";
import {castTimeForDisplay, castTimeToNextDay, isNextDay} from "@/helpers/helpers.js";

const router = useRouter();
const api = createApiService(router);
const snackbarStore = useSnackbarStore();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  workday: {
    type: Object,
    required: true,
  },
  workdayLine: {
    type: Object,
    required: true,
  }
});

const emit = defineEmits(['update:modelValue', 'saved']);

const loading = ref(false);
const formData = ref({});
const errors = ref({});

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    errors.value = {};
    formData.value = JSON.parse(JSON.stringify(props.workdayLine));

    formData.value.startNextDay = isNextDay(formData.value.start);
    formData.value.endNextDay = isNextDay(formData.value.end);
    formData.value.start = castTimeForDisplay(formData.value.start);
    formData.value.end = castTimeForDisplay(formData.value.end);
  }
});

const closeDialog = () => {
  emit('update:modelValue', false);
};

const saveWorkdayLine = async () => {
  loading.value = true;
  try {
    let startTime = formData.value.start;
    let endTime = formData.value.end;

    // Apply next day conversion if checkboxes are selected
    if (formData.value.startNextDay && startTime) {
      startTime = castTimeToNextDay(startTime);
    }
    if (formData.value.endNextDay && endTime) {
      endTime = castTimeToNextDay(endTime);
    }

    const json = JSON.stringify({
      start: startTime,
      end: endTime,
      workday_id: props.workday.id,
    });

    const response = await api.post(formData.value.id ? 'workdayLineUpdate?id=' + formData.value.id: "workdayLineCreate", json);
    emit('saved', response.data.data);
    closeDialog();
  } catch (error) {
    snackbarStore.showMessage("Er is een fout opgetreden bij het opslaan!", "error");
    errors.value = error.response?.data?.data;
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    max-width="600"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <v-card>
      <v-card-title class="bg-primary">
        Regel {{ formData.id ? 'bewerken' : 'toevoegen' }} op {{ $filters.formatDate(workday?.date) }}
      </v-card-title>
      <v-card-text>
        <v-text-field
          v-model="formData.start"
          v-maska="'##:##'"
          autofocus
          placeholder="07:00"
          label="Van"
          prepend-icon="mdi-clock-time-four-outline"
          :error-messages="translateErrors(errors.start, 'Van')"
        >
          <template #append>
            <v-checkbox
              v-model="formData.startNextDay"
              true-icon="mdi-calendar-arrow-right"
              false-icon="mdi-calendar-today"
              color="indufastBlue"
              :title="formData.startNextDay ? 'Volgende dag' : 'Zelfde dag'"
              hide-details
              tabindex="99"
            />
          </template>
        </v-text-field>
        <v-text-field
          v-model="formData.end"
          v-maska="'##:##'"
          placeholder="17:00"
          label="Tot"
          prepend-icon="mdi-clock-time-four-outline"
          :error-messages="translateErrors(errors.end, 'Tot')"
        >
          <template #append>
            <v-checkbox
              v-model="formData.endNextDay"
              true-icon="mdi-calendar-arrow-right"
              false-icon="mdi-calendar-today"
              color="indufastBlue"
              :title="formData.endNextDay ? 'Volgende dag' : 'Zelfde dag'"
              hide-details
              tabindex="99"
            />
          </template>
        </v-text-field>
      </v-card-text>
      <v-card-actions>
        <v-btn
          text="Annuleren"
          tabindex="99"
          @click="closeDialog"
        />
        <v-btn
          text="Opslaan"
          color="primary"
          variant="elevated"
          :loading="loading"
          @click="saveWorkdayLine"
        />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
