<script setup>
import {useRoute} from "vue-router";
import TopBarFilterMonth from "@/components/time_registration/TopBarFilterMonth.vue";
import TopBarFilterDate from "@/components/planning/TopBarFilterDate.vue";
import Changelog from "@/components/Changelog.vue";
import Menu from "@/components/Menu.vue";

const route = useRoute();
</script>

<template>
  <v-app-bar
    scroll-behavior="elevate"
    color="GSDBackground"
    density="compact"
    class="pt-2"
  >
    <v-spacer />
    <div class="d-flex align-center">
      <TopBarFilterMonth v-if="route.name === 'time-registration-details'"/>
      <TopBarFilterDate v-if="route.name === '/planning' || route.name === '/calendar'"/>
    </div>
    <v-spacer/>
    <div class="d-flex align-center">
      <Changelog />
      <Menu />
    </div>
  </v-app-bar>
</template>

