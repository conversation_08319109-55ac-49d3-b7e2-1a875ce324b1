<script setup>
import { ref, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import createApiService from '@/services/api'
import { employeeTypes, industryTypes, nonWorkingDays, userGroups } from "@/helpers/constants.js"
import { translateErrors } from "@/helpers/translateErrors.js"
import { useSnackbarStore } from '@/stores/snackbar'
import { useAuthenticationStore } from "@/stores/authentication.js"
import LogsDisplay from "@/components/LogsDisplay.vue"

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  employeeData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'saved'])

const router = useRouter()
const api = createApiService(router)
const snackbarStore = useSnackbarStore()
const authStore = useAuthenticationStore()

const dialog = ref(false)
const loading = ref(false)
const editEmployeeData = ref({})
const errors = ref({})
const nonWorkingDaysChanged = ref(false)
const workPercentageChanged = ref(false)
const activeTab = ref('employee')
const logs = ref([])
const logsLoading = ref(false)

const readonly = computed(() => !authStore.hasPermission('edit'))

watch(() => props.modelValue, (value) => {
  dialog.value = value
  if (value) {
    initializeData()
  }
})

watch(dialog, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    errors.value = {}
  }
})

watch(() => editEmployeeData.value.rank, (newRank) => {
  if (!newRank) {
    editEmployeeData.value.rank_number = null
  }
})

const initializeData = () => {
  errors.value = {}
  editEmployeeData.value = { ...props.employeeData }
  
  // Initialize arrays if they don't exist
  if (!editEmployeeData.value.non_working_days_even_weeks) {
    editEmployeeData.value.non_working_days_even_weeks = []
  }
  if (!editEmployeeData.value.non_working_days_uneven_weeks) {
    editEmployeeData.value.non_working_days_uneven_weeks = []
  }
  
  // Set defaults for new employee
  if (!editEmployeeData.value.id) {
    editEmployeeData.value = {
      active: true,
      may_login: false,
      team_lead: false,
      private: false,
      monthly_percentage: 100,
      non_working_days_even_weeks: [],
      non_working_days_uneven_weeks: [],
      ...editEmployeeData.value
    }
  }
  
  nonWorkingDaysChanged.value = false
  workPercentageChanged.value = false
  activeTab.value = 'employee'

  // Load logs if employee exists
  if (editEmployeeData.value.id) {
    loadLogs()
  } else {
    logs.value = []
  }
}

const close = () => {
  dialog.value = false
}

const save = async () => {
  loading.value = true
  
  try {
    const uri = editEmployeeData.value.id 
      ? `employeeUpdate?id=${editEmployeeData.value.id}` 
      : "employeeCreate"
    
    const response = await api.post(uri, JSON.stringify(editEmployeeData.value))
    editEmployeeData.value.id = response.data.data.id
    
    snackbarStore.showMessage("Medewerker succesvol opgeslagen!", "success")
    emit('saved', response.data.data)
    close()
  } catch (error) {
    errors.value = error.response?.data?.data || {}
    snackbarStore.showMessage("Er is een fout opgetreden bij het opslaan!", "error")
    console.log(error)
  } finally {
    loading.value = false
  }
}

const loadLogs = async () => {
  if (!editEmployeeData.value.id) return

  logsLoading.value = true
  try {
    const response = await api.get(`logList?entity_name=IndufastEmployee&entity_id=${editEmployeeData.value.id}`)
    logs.value = response.data.data || []
  } catch (error) {
    console.error('Error loading employee logs:', error)
    logs.value = []
  } finally {
    logsLoading.value = false
  }
}
</script>

<template>
  <v-dialog
    v-model="dialog"
    width="1000px"
    scrollable
  >
    <v-card>
      <v-card-title class="pa-0">
        <v-toolbar color="primary">
          <v-toolbar-title>
            {{ editEmployeeData.id ? 'Medewerker ' + editEmployeeData.name + ' bewerken' : 'Nieuwe medewerker' }}
          </v-toolbar-title>
          <v-toolbar-items>
            <v-btn
              icon
              @click="close"
            >
              <v-icon icon="mdi-close" />
            </v-btn>
          </v-toolbar-items>
        </v-toolbar>
      </v-card-title>

      <v-tabs
        v-model="activeTab"
        color="primary"
      >
        <v-tab value="employee">
          <v-icon class="mr-2">
            mdi-account
          </v-icon>
          Medewerker
        </v-tab>
        <v-tab
          value="logs"
          :disabled="!editEmployeeData.id"
        >
          <v-icon class="mr-2">
            mdi-history
          </v-icon>
          Logs
        </v-tab>
      </v-tabs>
      <v-tabs-window
        v-model="activeTab"
        class="overflow-y-auto"
      >
        <v-tabs-window-item value="employee">
          <v-card-text class="pa-2">
            <v-row no-gutters>
              <v-col>
                <v-card>
                  <v-card-title>Medewerker</v-card-title>
                  <v-card-text>
                    <v-text-field
                      v-model="editEmployeeData.name"
                      label="Naam"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.name, 'Naam')"
                      class="required mb-5"
                    />
                    <v-select
                      v-model="editEmployeeData.type"
                      label="Type"
                      :items="employeeTypes"
                      item-title="title"
                      item-value="value"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.type, 'Type')"
                      class="required mb-5"
                    />
                    <v-text-field
                      v-model="editEmployeeData.email"
                      label="E-mail"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.email, 'E-mail')"
                      class="required mb-5"
                    />
                    <v-text-field
                      v-model="editEmployeeData.extra_email"
                      label="Extra e-mail"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.extra_email, 'Extra e-mail')"
                    />
                  </v-card-text>
                </v-card>
                <v-card>
                  <v-card-title>Rang</v-card-title>
                  <v-card-text>
                    <v-select
                      v-model="editEmployeeData.rank"
                      label="Rang"
                      :items="['A', 'B', 'C', 'D', 'E']"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.rank, 'Rang')"
                      :clearable="!readonly"
                      class="mb-5"
                      :class="{ required: editEmployeeData.active }"
                    />
                    <v-slider
                      v-if="editEmployeeData.rank"
                      v-model="editEmployeeData.rank_number"
                      min="1"
                      max="10"
                      step="1"
                      thumb-label="always"
                      show-ticks="always"
                      tick-size="2"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.rank_number, 'Rangnummer')"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col>
                <v-card>
                  <v-card-title>Werktijden</v-card-title>
                  <v-card-text>
                    <v-slider
                      v-model="editEmployeeData.monthly_percentage"
                      min="10"
                      max="100"
                      step="10"
                      thumb-label="always"
                      show-ticks="always"
                      tick-size="2"
                      hide-details
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.monthly_percentage, 'Maandelijkse percentage')"
                      @update:model-value="workPercentageChanged = true"
                    >
                      <template #thumb-label="{ modelValue }">
                        {{ modelValue }}%
                      </template>
                    </v-slider>
                    <div
                      v-if="workPercentageChanged"
                      class="hint"
                    >
                      Let op: het wijzigen van de werkpercentage heeft effect op openstaande maanden in de urenregistratie.
                    </div>
                  </v-card-text>
                  <v-card-title>
                    Roostervrije dagen
                  </v-card-title>
                  <v-card-text>
                    <v-row no-gutters>
                      <v-col>
                        <v-card-subtitle class="pl-0">
                          Even weken
                        </v-card-subtitle>
                        <v-checkbox
                          v-for="(label, name) in nonWorkingDays"
                          :key="name"
                          v-model="editEmployeeData.non_working_days_even_weeks"
                          :value="name"
                          :label="label"
                          color="primary"
                          hide-details
                          density="compact"
                          :readonly="readonly"
                          @change="nonWorkingDaysChanged = true"
                        />
                      </v-col>
                      <v-col>
                        <v-card-subtitle class="pl-0">
                          Oneven weken
                        </v-card-subtitle>
                        <v-checkbox
                          v-for="(label, name) in nonWorkingDays"
                          :key="name"
                          v-model="editEmployeeData.non_working_days_uneven_weeks"
                          :label="label"
                          :value="name"
                          color="primary"
                          hide-details
                          density="compact"
                          :readonly="readonly"
                          @change="nonWorkingDaysChanged = true"
                        />
                      </v-col>
                    </v-row>
                    <div
                      v-if="nonWorkingDaysChanged"
                      class="hint"
                    >
                      Let op: het wijzigen van de roostervrije dagen heeft effect op openstaande maanden in de urenregistratie.
                    </div>
                  </v-card-text>
                  <v-card-text>
                    <v-text-field
                      v-model="editEmployeeData.name_accredis"
                      label="Naam in Accredis"
                      :error-messages="translateErrors(errors.name_accredis, 'Naam in Accredis')"
                      :readonly="readonly"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col>
                <v-card>
                  <v-card-title>Overig</v-card-title>
                  <v-card-text>
                    <v-select
                      v-model="editEmployeeData.industries"
                      label="Branches"
                      :items="industryTypes"
                      item-title="title"
                      item-value="value"
                      multiple
                      :readonly="readonly"
                    >
                      <template #selection="{ item }">
                        <v-chip
                          :color="item.raw.color"
                          variant="flat"
                          :prepend-icon="item.raw.icon"
                        >
                          {{ item.title }}
                        </v-chip>
                      </template>
                    </v-select>
                    <v-switch
                      v-model="editEmployeeData.active"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Actief"
                      hide-details
                      :readonly="readonly"
                    />
                    <v-switch
                      v-model="editEmployeeData.may_login"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Mag inloggen"
                      hide-details
                      :readonly="readonly"
                    />
                    <v-select
                      v-if="editEmployeeData.may_login"
                      v-model="editEmployeeData.usergroup"
                      label="Gebruikersgroep"
                      :items="userGroups"
                      item-title="title"
                      item-value="value"
                      :readonly="readonly"
                      :error-messages="translateErrors(errors.usergroup, 'Gebruikersgroep')"
                    />
                    <v-switch
                      v-model="editEmployeeData.team_lead"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Contactpersoon bouwplaats"
                      hide-details
                      :readonly="readonly"
                    />
                    <v-switch
                      v-model="editEmployeeData.private"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Privé"
                      hide-details
                      :readonly="readonly"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions v-if="!readonly">
            <v-btn
              variant="elevated"
              color="primary"
              prepend-icon="mdi-content-save"
              :loading="loading"
              @click="save"
            >
              Opslaan
            </v-btn>
          </v-card-actions>
        </v-tabs-window-item>

        <v-tabs-window-item value="logs">
          <v-card-text>
            <logs-display
              :logs="logs"
              :loading="logsLoading"
              @refresh="loadLogs"
            />
          </v-card-text>
        </v-tabs-window-item>
      </v-tabs-window>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.hint {
  font-size: 0.75rem;
  padding-top: 6px;
  color: rgb(var(--v-theme-error));
}
</style>
