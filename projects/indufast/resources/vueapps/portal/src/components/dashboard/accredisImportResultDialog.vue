<script setup>
import { defineProps, defineEmits } from 'vue'

defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  dashboard: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue']);
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    max-width="800px"
    max-height="90%"
    scrollable
    @update:model-value="emit('update:modelValue', false)"
  >
    <v-card>
      <v-toolbar color="primary">
        <v-toolbar-title>
          {{ $filters.ucFirst($filters.formatDateTime(dashboard.last_accredis_import)) }}
        </v-toolbar-title>
        <v-toolbar-items>
          <v-btn
            icon="mdi-close"
            @click="emit('update:modelValue', false)"
          />
        </v-toolbar-items>
      </v-toolbar>
      <v-card-text>
        <v-card
          v-for="(item, index) in dashboard.last_accredis_import_result"
          :key="index"
        >
          <v-card-title>{{ $filters.ucFirst($filters.formatTime(item.time, true)) }}: {{ item.filename }}</v-card-title>
          <template v-if="item.messages.length">
            <ul class="pl-10 mb-4">
              <li
                v-for="(message, messageIndex) in item.messages"
                :key="messageIndex"
              >
                {{ message }}
              </li>
            </ul>
          </template>
          <template v-if="item.warnings.length">
            <v-card-title class="text-h6">
              <v-icon
                icon="mdi-alert"
                color="warning"
                class="mr-1"
              />
              Waarschuwingen
            </v-card-title>
            <ul class="pl-10 mb-4">
              <li
                v-for="(message, messageIndex) in item.warnings"
                :key="messageIndex"
              >
                {{ message }}
              </li>
            </ul>
          </template>
          <template v-if="item.errors.length">
            <v-card-title class="text-h6">
              <v-icon
                icon="mdi-close-octagon"
                color="error"
                class="mr-1"
              />
              Foutmeldingen
            </v-card-title>
            <ul class="pl-10">
              <li
                v-for="(message, messageIndex) in item.errors"
                :key="messageIndex"
              >
                {{ message }}
              </li>
            </ul>
          </template>
          <v-divider v-if="index !== dashboard.last_accredis_import_result.length - 1" />
        </v-card>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<style scoped>

</style>
