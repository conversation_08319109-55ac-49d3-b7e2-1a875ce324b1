<script setup>
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Bevestigen'
  },
  message: {
    type: String,
    default: 'Weet je het zeker?'
  },
  loading: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    default: 'warning'
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

const closeDialog = () => {
  emit('update:modelValue', false);
};

const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
  closeDialog();
};
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    max-width="500"
    :persistent="loading"
    @update:model-value="closeDialog"
  >
    <v-card>
      <v-toolbar :color="color">
        <v-toolbar-title>
          {{ title }}
        </v-toolbar-title>
        <v-toolbar-items>
          <v-btn
            icon
            :disabled="loading"
            @click="closeDialog"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar-items>
      </v-toolbar>

      <v-card-text class="py-6">
        <div class="text-body-1">
          {{ message }}
        </div>
      </v-card-text>

      <v-card-actions class="px-6 pb-4">
        <v-spacer />
        <v-btn
          variant="text"
          :disabled="loading"
          @click="handleCancel"
        >
          Annuleren
        </v-btn>
        <v-btn
          :color="color"
          variant="elevated"
          :loading="loading"
          @click="handleConfirm"
        >
          Bevestigen
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
