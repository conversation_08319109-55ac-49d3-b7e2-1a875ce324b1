<script setup>
import {defineProps, ref} from "vue";
import {projectStatus} from "@/helpers/constants.js";
import ProjectViewDialog from "@/components/project/ProjectViewDialog.vue";
import ProjectEditDialog from "@/components/project/ProjectEditDialog.vue";
import {eventHasExternalConflicts, eventHasInternalConflicts, needsLoading} from "@/helpers/helpers.js";

const showViewProjectDialog = ref(false);
const showEditProjectDialog = ref(false);
const projectToView = ref(null);
const projectToEdit = ref(null);

const props = defineProps({
  projects: {
    type: Array,
    required: true,
  },
  projectIsChanged: {
    type: Function,
    required: true,
  },
  draggedEventEmployee: {
    type: Object,
    required: true,
  },
  hasUnsavedChanges: {
    type: Boolean,
    required: true,
  },
  onDrop: {
    type: Function,
    required: true,
  },
  onDragEnter: {
    type: Function,
    required: true,
  },
  onDragLeave: {
    type: Function,
    required: true,
  },
  filterEvents: {
    type: Function,
    required: true,
  },
  markCalendarEventAsChanged: {
    type: Function,
    required: true,
  },
  readonly: {
    type: Boolean,
    required: true,
  },
});
const emit = defineEmits(["saved"]);

const openView = (project) => {
  projectToView.value = project;
  showViewProjectDialog.value = true;
};

const openEdit = (project) => {
  showViewProjectDialog.value = false;
  projectToEdit.value = project;
  showEditProjectDialog.value = true;
};

const getFte = (project, event) => {
  return event.fte || project.fte;
}
</script>

<template>
  <template
    v-for="project in projects"
    :key="project.id"
  >
    <v-card
      v-for="(calendarEvent, index) in filterEvents(project.events)"
      :key="index"
      :class="{
        dropzone: Object.keys(props.draggedEventEmployee).length !== 0,
        'pa-0 pl-0 mb-4 project': true,
        'not-plannable-internal': eventHasInternalConflicts(calendarEvent),
        'not-plannable-external': eventHasExternalConflicts(calendarEvent),
      }"
      @dragenter="(event) => onDragEnter(calendarEvent, event)"
      @dragover.prevent
      @dragleave="(event) => onDragLeave(event)"
      @drop="(event) => onDrop(calendarEvent, event)"
      @click="openView(project)"
    >
      <v-toolbar
        class="ma-0 pa-1"
        height="25"
        :color="(calendarEvent.type === 'work') ? projectStatus.find(status => status.value === project.status)?.color : 'indufastYellow'"
      >
        <v-toolbar-title class="ml-1 projectTitle">
          <v-icon
            v-if="eventHasInternalConflicts(calendarEvent)"
            title="Dit project heeft medewerkers die dubbel zijn ingepland"
            icon="mdi-alert-circle-outline"
            class="mr-1"
          />
          <v-icon
            v-else-if="eventHasExternalConflicts(calendarEvent)"
            title="Dit project heeft medewerkers die niet meer beschikbaar zijn op dit moment"
            icon="mdi-alert-circle-outline"
            class="mr-1"
          />
          <v-icon
            v-if="calendarEvent.type === 'work' && getFte(project, calendarEvent) > calendarEvent.employees.length"
            title="Er zijn minder medewerkers ingepland dan het aantal FTE's van dit project"
            icon="mdi-account-plus"
            class="mr-1"
          />
          <v-icon
            v-if="calendarEvent.type === 'work' && !calendarEvent.team_lead_employee_id"
            title="Geen contactpersoon bouwplaats aangewezen"
            icon="mdi-account-hard-hat-outline"
            class="mr-1"
          />
          <v-icon
            v-if="calendarEvent.type === 'work' && needsLoading(project, calendarEvent) && !calendarEvent.material_load_employee_ids?.length"
            title="Geen lader aangewezen terwijl er materiaal geladen moet worden"
            icon="mdi-forklift"
            class="mr-1"
          />
          <template v-if="calendarEvent.type === 'blast'">
            Straaldag:
          </template>
          {{ project.name }}
        </v-toolbar-title>
        <v-toolbar-items
          v-if="calendarEvent.type === 'work'"
          class="mr-2"
        >
          <v-icon
            v-if="projectIsChanged(project)"
            title="Project is gewijzigd"
            icon="mdi-content-save"
            class="mr-1"
          />
          <v-icon
            icon="mdi-account"
            class="mr-1"
          />
          {{ calendarEvent.employees.length }}
          <template v-if="getFte(project, calendarEvent)">
            / {{ getFte(project, calendarEvent) }}
          </template>
        </v-toolbar-items>
      </v-toolbar>
      <v-list
        density="compact"
        slim
      >
        <v-list-item prepend-icon="mdi-clock-outline">
          {{ $filters.formatTime(calendarEvent.start) }} - {{ $filters.formatTime(calendarEvent.end) }}
        </v-list-item>
        <v-list-item prepend-icon="mdi-map-marker">
          {{ project.address }}
        </v-list-item>
        <v-list-item
          v-if="calendarEvent.remark"
          prepend-icon="mdi-note"
        >
          {{ calendarEvent.remark }}
        </v-list-item>
      </v-list>
    </v-card>
  </template>
  <project-view-dialog
    v-model="showViewProjectDialog"
    :project="projectToView"
    :project-is-changed="projectIsChanged"
    :has-unsaved-changes="hasUnsavedChanges"
    :mark-calendar-event-as-changed="markCalendarEventAsChanged"
    :readonly="readonly"
    @edit="openEdit"
    @save="showViewProjectDialog = false"
  />
  <project-edit-dialog
    v-model="showEditProjectDialog"
    :project-data="projectToEdit"
    :readonly="readonly"
    @saved="emit('saved')"
  />
</template>

<style scoped>
.dropzone {
  background-color: white;
}

.dropzone * {
  pointer-events: none;
}

.projectTitle {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 15px;
}

.project {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  box-sizing: border-box;
}

.not-plannable-internal {
  border: 1.5px solid purple !important;
}
.not-plannable-external {
  border: 1.5px solid red !important;
}
</style>
