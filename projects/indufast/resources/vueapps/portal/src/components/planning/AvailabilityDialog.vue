<script setup>
import { computed } from 'vue';
import { employeeAvailability } from '@/helpers/constants.js';
import {formatDate} from "date-fns";
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  availabilityData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

const closeDialog = () => {
  emit('update:modelValue', false);
};

// Calculate total available employees
const totalAvailable = computed(() => {
  if (!props.availabilityData.employee_details) return 0;
  return props.availabilityData.employee_details.filter(employee =>
    ['available', 'available_morning', 'available_afternoon', 'available_morning_afternoon'].includes(employee.availability)
  ).length;
});

// Group employees by their availability status
const groupedEmployees = computed(() => {
  if (!props.availabilityData.employee_details) return [];

  const groups = {};

  // Group employees by their availability
  props.availabilityData.employee_details.forEach(employee => {
    const availabilityType = employeeAvailability.find(type => type.value === employee.availability);

    if (availabilityType) {
      if (!groups[employee.availability]) {
        groups[employee.availability] = {
          ...availabilityType,
          employees: []
        };
      }
      groups[employee.availability].employees.push(employee);
    }
  });

  const sortOrder = [
    'available',
    'available_morning',
    'available_afternoon',
    'available_morning_afternoon',
    'not_available',
    'not_available_error',
    'not_available_non_working_day',
  ];

  // Return groups sorted by the defined order, only including groups that have employees
  return sortOrder
    .map(type => groups[type])
    .filter(group => group && group.employees.length > 0);
});

const openPlanningPage = () => {
  const date = new Date(props.availabilityData.date);
  router.push({
    name: '/planning', params: {
      date: formatDate(date, "yyyy-MM-dd"),
    }
  });
  emit('update:modelValue', false);
}
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    width="600px"
    scrollable
    @update:model-value="closeDialog"
  >
    <v-card>
      <v-toolbar color="indufastCyan">
        <v-toolbar-title>
          Beschikbaarheid {{ $filters.formatDate(props.availabilityData.date) }}
        </v-toolbar-title>
        <v-toolbar-items>
          <v-btn
            icon="mdi-close"
            @click="closeDialog"
          />
        </v-toolbar-items>
      </v-toolbar>

      <v-card-text class="pa-2">
        <v-list
          density="compact"
          class="pt-0"
        >
          <v-list-item
            prepend-icon="mdi-calendar-account"
            slim
          >
            <strong class="mr-2">{{ totalAvailable === 1 ? '1 medewerker beschikbaar' : totalAvailable + ' medewerkers beschikbaar' }}</strong>
            <v-icon
              icon="mdi-clipboard-account"
              color="indufastGreen"
              variant="plain"
              title="Open planning"
              @click="openPlanningPage()"
            />
          </v-list-item>
          <template
            v-for="group in groupedEmployees"
            :key="group.value"
          >
            <v-divider class="my-2" />
            <v-list-item slim>
              <template #prepend>
                <v-icon
                  :icon="group.icon"
                  :color="group.color"
                />
              </template>
              <v-list-item-title>
                <strong>
                  {{ group.title }} ({{ group.employees.length }})
                </strong>
                <p
                  v-if="group.value === 'not_available_non_working_day'"
                  class="text-caption text-medium-emphasis"
                >
                  Beschikbaarheid is niet gecontroleerd voor deze medewerkers
                </p>
              </v-list-item-title>
              <div class="d-flex flex-wrap ga-2 mt-2">
                <v-chip
                  v-for="employee in group.employees"
                  :key="employee.name"
                  size="small"
                  variant="outlined"
                >
                  {{ employee.name }}
                </v-chip>
              </div>
            </v-list-item>
          </template>
        </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<style scoped>
:deep(.v-list-item__prepend .v-icon) {
  opacity: 1 !important;
}
</style>
