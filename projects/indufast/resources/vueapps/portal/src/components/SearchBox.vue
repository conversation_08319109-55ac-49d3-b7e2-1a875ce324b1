<script setup>
import {defineEmits, defineProps, ref, toRefs, watch} from "vue";
import {useMagicKeys} from "@vueuse/core";

const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  class: {
    type: [String, Array],
    default: "",
  },
});

const keys = useMagicKeys();
const keySearch = keys['Alt+z'];
const emit = defineEmits(["update:modelValue"]);
const searchClosed = ref(true);
const {modelValue} = toRefs(props);

watch(keySearch, (pressed) => {
  if (pressed) {
    if (searchClosed.value) {
      searchClosed.value = false;
      document.getElementsByClassName('expanding-search')[0].getElementsByTagName('input')[0].focus();
    }
    else {
      clearInput();
    }
  }
});

const clearInput = () => {
  document.getElementsByClassName('expanding-search')[0].getElementsByTagName('input')[0].blur();
  emit("update:modelValue", "");
  searchClosed.value = true;
};

watch(() => modelValue, (newValue) => {
  emit("update:modelValue", newValue);
});
</script>

<template>
  <span
    title="Alt + Z"
    class="expanding-search"
    :class="{'search-closed' : searchClosed}"
  >
    <v-text-field
      :model-value="modelValue"
      prepend-inner-icon="mdi-magnify"
      :label="searchClosed ? '' : 'Zoeken'"
      hide-details
      density="compact"
      :class="props.class"
      clearable
      autocomplete="off"
      :variant="searchClosed ? 'outlined' : 'outlined'"
      @focus="searchClosed = false"
      @blur="searchClosed = !modelValue || !modelValue.length"
      @change="searchClosed = !modelValue || !modelValue.length"
      @keydown.esc="clearInput"
      @update:model-value="val => emit('update:modelValue', val)"
    />
  </span>
</template>

<style>
.expanding-search {
  display: inline-block;
  width: 250px;
  max-width: 250px;
  transition: max-width 0.3s ease-in-out;

  &.search-closed {
    max-width: 32px;

    .v-field__prepend-inner > .v-icon {
      opacity: 1;
    }

    * {
      cursor: pointer !important;
      border: none !important;
    }
  }

}

</style>
