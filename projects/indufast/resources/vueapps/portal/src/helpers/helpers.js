import {
  toRaw,
  isRef,
  isReactive,
  isProxy,
} from 'vue';
import {isBefore, startOfDay} from "date-fns";

export function deepToRaw(sourceObj) {
  const objectIterator = (input) => {
    if (Array.isArray(input)) {
      return input.map((item) => objectIterator(item));
    }
    if (isRef(input) || isReactive(input) || isProxy(input)) {
      return objectIterator(toRaw(input));
    }
    if (input && typeof input === 'object') {
      return Object.keys(input).reduce((acc, key) => {
        acc[key] = objectIterator(input[key]);
        return acc;
      }, {});
    }
    return input;
  };

  return objectIterator(sourceObj);
}

export function sortNullLast(a, b) {
  if (a === null && b === null) return 0;
  if (a === null) return 1;
  if (b === null) return -1;
  return (typeof a !== 'number') ? a.localeCompare(b) : a - b;
}
function getEventConflicts(event) {
  return event.employees.filter(employee => employee.conflict);
}
export function eventHasInternalConflicts(event) {
  const conflicts = getEventConflicts(event);
  return conflicts.length > 0 && conflicts.every(conflict => conflict.conflict.internal);
}
export function eventHasExternalConflicts(event) {
  const conflicts = getEventConflicts(event);
  return conflicts.length > 0 && conflicts.every(conflict => !conflict.conflict.internal);
}
export function eventHasDeclines(event) {
  return event.employees.some(employee => employee.responseStatus === 'declined');
}
export function isEventInPast (event) {
  const eventDate = startOfDay(new Date(event.start));
  const today = startOfDay(new Date());
  return isBefore(eventDate, today);
};
export function projectHasFTEError(project, event) {
  return (event.fte || project.fte) > event.employees.length;
}
export function needsLoading(project, event) {
  return event.material_load || project.material_load;
}

export function isValidTime(timeString) {
  const timeRegex = /^(?:2[0-3]|[01]?[0-9]):[0-5][0-9]$/;
  return timeRegex.test(timeString);
}

export function timeInMinutes(timeString) {
  if (!timeString || !timeString.includes(':')) return null;

  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
  return Number(timeString);
}

export function isNextDay(timeString) {
  if (!timeString || !timeString.includes(':')) return false;
  const [hours] = timeString.split(':').map(Number);
  return hours >= 24;
}

export function castTimeForDisplay(timeString) {
  if (!timeString || !timeString.includes(':')) return timeString;
  const [hours, minutes, seconds] = timeString.split(':').map(Number);

  // Normalize hours to 24-hour format (26:00:00 becomes 02:00:00)
  const normalizedHours = hours % 24;

  // Pad with leading zeros to maintain HH:MM:SS format
  const paddedHours = normalizedHours.toString().padStart(2, '0');
  const paddedMinutes = minutes.toString().padStart(2, '0');
  const paddedSeconds = (seconds) ? seconds.toString().padStart(2, '0') : '00';

  return `${paddedHours}:${paddedMinutes}:${paddedSeconds}`;
}

export function castTimeToNextDay(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);

  // Add 24 hours to shift time to next day (02:00:00 becomes 26:00:00)
  const nextDayHours = hours + 24;

  // Pad with leading zeros to maintain HH:MM:SS format
  const paddedHours = nextDayHours.toString().padStart(2, '0');
  const paddedMinutes = minutes.toString().padStart(2, '0');

  return `${paddedHours}:${paddedMinutes}`;
}

/**
 * Creates a debounced function that delays invoking `func` until after `wait`
 * milliseconds have elapsed since the last time the debounced function was invoked.
 * The debounced function comes with a `cancel` method to cancel delayed `func` invocations.
 *
 * @param {Function} func The function to debounce.
 * @param {number} wait The number of milliseconds to wait.
 * @param {boolean} [immediate=false] Specify whether `func` should be invoked on the leading edge of the timeout.
 * @returns {Function} Returns the new debounced function.
 */
 export function debounce(func, wait, immediate = false) {
  let timeout;
  let result;

  const debounced = function(...args) {
    const context = this;
    const later = function() {
      timeout = null;
      if (!immediate) {
        result = func.apply(context, args);
      }
    };

    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) {
      result = func.apply(context, args);
    }

    return result;
  };

  debounced.cancel = function() {
    clearTimeout(timeout);
    timeout = null;
  };

  return debounced;
}
