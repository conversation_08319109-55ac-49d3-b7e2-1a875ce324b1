import {format} from "date-fns";
import nl from "date-fns/locale/nl";

const filters = {
  formatDate(value) {
    return (value) ? format(new Date(value), 'EEEE d LLLL yyyy', {locale: nl}) : '';
  },

  formatDateTime(value) {
    return (value) ? format(new Date(value), 'EEEE d LLLL yyyy H:mm:ss', { locale: nl }) : '';
  },

  formatTime(value, showSeconds = false) {
    return format(new Date(value), (showSeconds ? 'HH:mm:ss': 'HH:mm'), { locale: nl });
  },

  ucFirst(val) {
    return String(val).charAt(0).toUpperCase() + String(val).slice(1);
  },

  formatDistance(value) {
    if (parseFloat(value) === 0.0) {
      return '';
    }

    if (parseFloat(value) < 1) {
      return (parseFloat(value) * 1000).toFixed(0) + ' m';
    }

    value = String(value);
    return value.replace('.', ',') + ' km';
  },
}
export default filters;
