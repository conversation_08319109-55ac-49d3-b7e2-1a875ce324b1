<?php
  if (str_starts_with($_SERVER['REQUEST_URI'], '/nl/api/')) {
    include(DIR_INCLUDES . "headercode_backend.inc.php");
  }

  if (ENVIRONMENT == 'LOCAL') {
    $html = file_get_contents(DIR_PROJECT_FOLDER . 'resources/vueapps/portal/index.html');
    $html = str_replace('"/src/main.js"', '"https://portal-indufast.node.localhost/src/main.js"', $html);
  }
  else {
    $html = file_get_contents(DIR_PROJECT_FOLDER . 'templates/portal/dist/index.html');
    $html = str_replace('"./assets/', '"/projects/indufast/templates/portal/dist/assets/', $html);
  }

  echo $html;