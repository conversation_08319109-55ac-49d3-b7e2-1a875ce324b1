<?php
class BaseIndufastProject extends AppModel {

  const DB_NAME = '';
  const TABLE_NAME = 'indufast_project';
  const OM_CLASS_NAME = 'IndufastProject';
  const columns = ['id', 'name', 'status', 'remark', 'fte', 'project_number', 'project_number_exact', 'customer_name', 'contact_name', 'contact_email', 'contact_number', 'address', 'material_load', 'void', 'insertTS', 'updateTS'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'status'                      => ['type' => 'enum', 'length' => '6', 'null' => false, 'enums' => ['blast_sand','planning','incomplete','weather_dependent','complete','unplanned']],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
    'fte'                         => ['type' => 'int', 'length' => '10', 'null' => true],
    'project_number'              => ['type' => 'varchar', 'length' => '32', 'null' => false],
    'project_number_exact'        => ['type' => 'varchar', 'length' => '32', 'null' => false],
    'customer_name'               => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'contact_name'                => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'contact_email'               => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'contact_number'              => ['type' => 'varchar', 'length' => '32', 'null' => true],
    'address'                     => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'material_load'               => ['type' => 'enum', 'length' => '2', 'null' => true, 'enums' => ['bladel','alblasserdam']],
    'void'                        => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'updateTS'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
  ];

  protected static array $primary_key = ['id'];
  protected string $auto_increment = 'id';

  public $id, $name, $status, $remark, $fte, $project_number, $project_number_exact, $customer_name, $contact_name, $contact_email, $contact_number, $address, $material_load, $void, $insertTS, $updateTS;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  */
  public function setDefaults() {
    $this->void = 0;
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastProject[]
   */
  public static function find_all_like(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastProject[]
   */
  public static function find_all_by(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return IndufastProject[]
   */
  public static function find_all(string $raw_sql = ''): array {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastProject|false
   */
  public static function find_by(?array $conditions, string $raw_sql = ''): IndufastProject|false {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param int|string|null $id (required)
   * @param string $raw_sql (optional)
   * @return IndufastProject|false
   */
  public static function find_by_id($id, string $raw_sql = ''): IndufastProject|false {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   */
  public static function count_all_by(?array $conditions, string $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   */
  public static function delete_by(?array $conditions, string $raw_sql = ''): bool {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}