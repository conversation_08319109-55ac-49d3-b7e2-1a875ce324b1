<?php
class BaseIndufastEmployee extends AppModel {

  const DB_NAME = '';
  const TABLE_NAME = 'indufast_employee';
  const OM_CLASS_NAME = 'IndufastEmployee';
  const columns = ['id', 'name', 'name_accredis', 'type', 'email', 'extra_email', 'rank', 'rank_number', 'industries', 'private', 'team_lead', 'active', 'usergroup', 'monthly_percentage', 'non_working_days_even_weeks', 'non_working_days_uneven_weeks', 'may_login', 'insertTS', 'updateTS'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'name_accredis'               => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'type'                        => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['staff','subcontractor']],
    'email'                       => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'extra_email'                 => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'rank'                        => ['type' => 'varchar', 'length' => '1', 'null' => true],
    'rank_number'                 => ['type' => 'int', 'length' => '11', 'null' => true],
    'industries'                  => ['type' => 'text', 'length' => '', 'null' => true],
    'private'                     => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'team_lead'                   => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'active'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'usergroup'                   => ['type' => 'varchar', 'length' => '20', 'null' => true],
    'monthly_percentage'          => ['type' => 'int', 'length' => '10', 'null' => false],
    'non_working_days_even_weeks' => ['type' => 'text', 'length' => '', 'null' => true],
    'non_working_days_uneven_weeks'=> ['type' => 'text', 'length' => '', 'null' => true],
    'may_login'                   => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'updateTS'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
  ];

  protected static array $primary_key = ['id'];
  protected string $auto_increment = 'id';

  public $id, $name, $name_accredis, $type, $email, $extra_email, $rank, $rank_number, $industries, $private, $team_lead, $active, $usergroup, $monthly_percentage, $non_working_days_even_weeks, $non_working_days_uneven_weeks, $may_login, $insertTS, $updateTS;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  */
  public function setDefaults() {
    $this->monthly_percentage = 100;
    $this->may_login = 0;
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastEmployee[]
   */
  public static function find_all_like(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastEmployee[]
   */
  public static function find_all_by(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return IndufastEmployee[]
   */
  public static function find_all(string $raw_sql = ''): array {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastEmployee|false
   */
  public static function find_by(?array $conditions, string $raw_sql = ''): IndufastEmployee|false {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param int|string|null $id (required)
   * @param string $raw_sql (optional)
   * @return IndufastEmployee|false
   */
  public static function find_by_id($id, string $raw_sql = ''): IndufastEmployee|false {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   */
  public static function count_all_by(?array $conditions, string $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   */
  public static function delete_by(?array $conditions, string $raw_sql = ''): bool {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}