<?php
class BaseIndufastCalendarEvent extends AppModel {

  const DB_NAME = '';
  const TABLE_NAME = 'indufast_calendar_event';
  const OM_CLASS_NAME = 'IndufastCalendarEvent';
  const columns = ['id', 'project_id', 'google_calendar_event_id', 'start', 'end', 'material_load', 'confirmed', 'remark', 'material_load_employee_ids', 'team_lead_employee_id', 'fte', 'type'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'project_id'                  => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'google_calendar_event_id'    => ['type' => 'varchar', 'length' => '32', 'null' => false],
    'start'                       => ['type' => 'datetime', 'length' => '', 'null' => false],
    'end'                         => ['type' => 'datetime', 'length' => '', 'null' => false],
    'material_load'               => ['type' => 'enum', 'length' => '2', 'null' => true, 'enums' => ['bladel','alblasserdam']],
    'confirmed'                   => ['type' => 'boolean', 'length' => '1', 'null' => true],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
    'material_load_employee_ids'  => ['type' => 'longtext', 'length' => '', 'null' => true],
    'team_lead_employee_id'       => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'fte'                         => ['type' => 'int', 'length' => '10', 'null' => true],
    'type'                        => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['work','blast']],
  ];

  protected static array $primary_key = ['id'];
  protected string $auto_increment = 'id';

  public $id, $project_id, $google_calendar_event_id, $start, $end, $material_load, $confirmed, $remark, $material_load_employee_ids, $team_lead_employee_id, $fte, $type;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  */
  public function setDefaults() {
    $this->type = 'work';
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastCalendarEvent[]
   */
  public static function find_all_like(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastCalendarEvent[]
   */
  public static function find_all_by(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return IndufastCalendarEvent[]
   */
  public static function find_all(string $raw_sql = ''): array {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastCalendarEvent|false
   */
  public static function find_by(?array $conditions, string $raw_sql = ''): IndufastCalendarEvent|false {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param int|string|null $id (required)
   * @param string $raw_sql (optional)
   * @return IndufastCalendarEvent|false
   */
  public static function find_by_id($id, string $raw_sql = ''): IndufastCalendarEvent|false {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   */
  public static function count_all_by(?array $conditions, string $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   */
  public static function delete_by(?array $conditions, string $raw_sql = ''): bool {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}