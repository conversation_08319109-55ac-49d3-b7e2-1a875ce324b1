<?php

  namespace classes;

  trait PreventFindTrait {
    public static function find_by(?array $conditions, string $raw_sql = ''): static|false {
      throw new \Exception("Find operations are not allowed for " . static::class);
    }

    public static function find_all(string $raw_sql = ''): array {
      throw new \Exception("Find operations are not allowed for " . static::class);
    }

    public static function find_all_by(?array $conditions, string $raw_sql = ''): array {
      throw new \Exception("Find operations are not allowed for " . static::class);
    }

    public static function find_by_id($id, string $raw_sql = ''): static|false {
      throw new \Exception("Find operations are not allowed for " . static::class);
    }

    public static function find_all_like(?array $conditions, string $raw_sql = ''): array {
      throw new \Exception("Find operations are not allowed for " . static::class);
    }
  }
