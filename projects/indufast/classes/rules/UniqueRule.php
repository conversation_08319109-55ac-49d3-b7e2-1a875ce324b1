<?php declare(strict_types=1);

  namespace classes\rules;

  use DBConn;
  use GsdException;
  use Somnambulist\Components\Validation\Exceptions\ParameterException;
  use Somnambulist\Components\Validation\Rule;

  class UniqueRule extends Rule {

    protected string $message = ":attribute must be unique";
    protected array $fillableParams = ['table', 'column', 'exclude_column', 'exclude_value', 'column2', 'value2'];

    public function __construct() {
    }

    /**
     * @throws ParameterException
     * @throws GsdException
     */
    public function check($value): bool {
      $this->assertHasRequiredParameters(['table', 'column']);

      $column = $this->parameter('column');
      $table = $this->parameter('table');
      $exclude_column = $this->parameter('exclude_column');
      $exclude_value = $this->parameter('exclude_value');
      $column2 = $this->parameter('column2');
      $value2 = $this->parameter('value2');

      $query = sprintf("SELECT count(*) AS count FROM %s WHERE %s = '%s'", $table, $column, $value);
      if ($exclude_column && $exclude_value) {
        $query .= sprintf(" AND %s != '%s'", $exclude_column, $exclude_value);
      }
      if ($column2 && $value2) {
        $query .= sprintf(" AND %s = '%s'", $column2, $value2);
      }

      $data = (DBConn::db_link()->query($query))->fetch_assoc();

      return intval($data['count']) === 0;
    }

  }