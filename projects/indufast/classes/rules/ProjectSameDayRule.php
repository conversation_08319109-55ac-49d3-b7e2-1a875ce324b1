<?php declare(strict_types=1);

  namespace classes\rules;

  use DBConn;
  use GsdException;
  use Somnambulist\Components\Validation\Exceptions\ParameterException;
  use Somnambulist\Components\Validation\Rule;

  class ProjectSameDayRule extends Rule {

    protected string $message = "Two events of the same project cannot be scheduled on the same day";
    protected array $fillableParams = ['exclude_id'];

    public function __construct() {
    }

    /**
     * @throws ParameterException
     * @throws GsdException
     */
    public function check($value): bool {
      // Get the project_id from the current validation context
      $project_id = $this->attribute()->value('project_id');
      $type = $this->attribute()->value('type');
      $exclude_id = $this->parameter('exclude_id');
      
      // Only apply this rule for 'work' type events
      if ($type !== 'work') {
        return true;
      }
      
      $event_date = date('Y-m-d', strtotime($value));
      
      // Query to check if there are other work events of the same project on the same day
      $query = sprintf(
        "SELECT count(*) AS count FROM indufast_calendar_event 
         WHERE project_id = '%s' 
         AND type = 'work' 
         AND DATE(start) = '%s'",
        $project_id,
        $event_date
      );
      
      // If we're updating an existing event, exclude it from the check
      if ($exclude_id) {
        $query .= sprintf(" AND id != '%s'", $exclude_id);
      }
      
      $data = (DBConn::db_link()->query($query))->fetch_assoc();
      
      return intval($data['count']) === 0;
    }

  }
