<?php declare(strict_types=1);

  namespace classes\rules;

  use DBConn;
  use GsdException;
  use IndufastWorkday;
  use Somnambulist\Components\Validation\Exceptions\ParameterException;
  use Somnambulist\Components\Validation\Rule;

  class LockedMonthRule extends Rule {

    protected string $message = "Cannot edit workdays for a locked month";
    protected array $fillableParams = ['workday_id'];

    public function __construct() {
    }

    /**
     * @throws ParameterException
     * @throws GsdException
     */
    public function check($value): bool {
      $workday_id = $this->parameter('workday_id');

      if ($workday_id) {
        $workday = IndufastWorkday::find_by_id($workday_id);
        if (!$workday) return true;

        $date = $workday->date;
        $employee_id = $workday->employee_id;
      } else {
        $date = $value;
        $employee_id = $this->attribute()->value('employee_id');
      }

      if (!$employee_id || !$date) return true;

      $month = date('m', strtotime($date));
      $year = date('Y', strtotime($date));

      // Query to check if the monthly summary for this employee/month/year is locked
      $query = sprintf(
        "SELECT locked FROM indufast_workday_summary
         WHERE employee_id = '%s'
         AND month = '%s'
         AND year = '%s'",
        $employee_id,
        $month,
        $year
      );

      $result = DBConn::db_link()->query($query);

      if (!$result) return true;

      $data = $result->fetch_assoc();

      if (!$data) return true;

      return intval($data['locked']) === 0;
    }

  }