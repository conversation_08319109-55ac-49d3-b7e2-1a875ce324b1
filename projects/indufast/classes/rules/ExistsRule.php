<?php declare(strict_types=1);

  namespace classes\rules;

  use DBConn;
  use GsdException;
  use Somnambulist\Components\Validation\Exceptions\ParameterException;
  use Somnambulist\Components\Validation\Rule;

  class ExistsRule extends Rule {

    protected string $message = ":attribute does not exist";
    protected array $fillableParams = ['table', 'column'];

    public function __construct() {
    }

    /**
     * @throws ParameterException
     * @throws GsdException
     */
    public function check($value): bool {
      $this->assertHasRequiredParameters(['table', 'column']);

      $column = $this->parameter('column');
      $table = $this->parameter('table');

      $query = sprintf("SELECT count(*) AS count FROM %s WHERE %s = '%s'", $table, $column, $value);

      $data = (DBConn::db_link()->query($query))->fetch_assoc();

      return intval($data['count']) !== 0;
    }

  }