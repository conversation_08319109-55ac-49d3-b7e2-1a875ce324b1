<?php declare(strict_types=1);

  namespace classes\rules;

  use Somnambulist\Components\Validation\Exceptions\ParameterException;
  use Somnambulist\Components\Validation\Rule;
  use DateTime;
  use Exception;

  class TodayOrLaterRule extends Rule {

    protected string $message = ":attribute must be today or a future date";
    protected array $fillableParams = [];

    /**
     * @throws ParameterException
     */
    public function check($value): bool {
      if (empty($value)) {
        return false;
      }

      try {
        $inputDate = new DateTime($value);
        $today = new DateTime('today');

        $inputDateOnly = new DateTime($inputDate->format('Y-m-d'));
        $todayDateOnly = new DateTime($today->format('Y-m-d'));

        return $inputDateOnly >= $todayDateOnly;

      } catch (Exception) {
        return false;
      }
    }
  }