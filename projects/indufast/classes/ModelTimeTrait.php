<?php

  trait ModelTimeTrait {
    public function validateProperty(string $property): bool {
      $property_structure = $this::field_structure[$property];
      $type = $property_structure["type"];
      if ($type === 'time') {
        return self::valid_time($this->{$property});
      }
      return parent::validateProperty($property);
    }
    public static function valid_time($input): bool {
      // remove minus sign if present
      $parts = explode(':', ltrim($input, '-'));

      // add seconds if not present
      if (count($parts) === 2) $parts[] = '00';
      if (count($parts) !== 3) return false;

      [$hours, $minutes, $seconds] = $parts;

      // check if all parts are numeric
      if (!is_numeric($hours) || !is_numeric($minutes) || !is_numeric($seconds)) return false;

      // check if all parts are within range of sql time datatype
      return ($hours <= 838 && $minutes <= 59 && $seconds <= 59);
    }
  }