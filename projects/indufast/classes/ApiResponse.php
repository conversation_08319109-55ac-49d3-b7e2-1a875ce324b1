<?php

  namespace classes;

  use DBConn;
  use GsdExceptionHandler;
  use RestUtils;

  class ApiResponse extends RestUtils {
    use \PropertyCastTrait;

    const HTTP_OK = 200;
    const HTTP_BAD_REQUEST = 400;
    const HTTP_NOT_AUTHORIZED = 401;
    const HTTP_ACCESS_DENIED = 403;
    const HTTP_NOT_FOUND = 404;
    const HTTP_INTERNAL_SERVER_ERROR = 500;
    const MESSAGE_OK = "OK";

    /**
     * @param array $data
     */
    public static function sendResponseOK(string $message, $data = []): void {
      try {
        self::castAllModels($data);

        RestUtils::sendResponse(self::HTTP_OK, json_encode(self::addQueryData([
          'message' => $message,
          'data'    => $data,
        ])));
      }
      catch (\Exception $e) {
        self::sendResponseError('Error processing response data', [], $e);
      }
    }

    protected static function addQueryData(array $data): array {
      if (ENVIRONMENT === 'LOCAL' && DBConn::$lastqueries) {
        $queries = [];
        foreach (DBConn::$lastqueries as $query) {
          $key = md5($query['query']);
          if (!isset($queries[$key])) {
            $queries[$key] = $query;
            $queries[$key]['duplicates'] = 0;
          }
          else {
            $queries[$key]['duplicates']++;
            $queries[$key]['duration'] += $query['duration'];
          }
        }
        usort($queries, function ($a, $b) {
          return $b['duplicates'] <=> $a['duplicates'];
        });

        $data['_query_duration'] = round(array_sum(array_column(DBConn::$lastqueries, 'duration')), 2);
        $data['_query_duplicates'] = count(array_column(DBConn::$lastqueries, 'duplicate'));
        $data['_query_count'] = count(DBConn::$lastqueries);
        $data['_queries'] = array_values($queries);
      }

      return $data;
    }

    /**
     * @param array $data
     */
    public static function sendResponseBadRequest(string $message, $data = []): void {
      RestUtils::sendResponse(self::HTTP_BAD_REQUEST, json_encode(self::addQueryData([
        'message' => $message,
        'data'    => $data,
      ])));
    }

    public static function sendResponseError(string $message, $data = [], $exception = null): void {
      if ($exception instanceof \Throwable) {
        $url = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        $errorDetails = sprintf(
          "Msg: %s in %s:%d\nStack trace:\n%s\nthrown\nFile: %s\nLine: %d\nLevel: %d\nURL: <a href=\"%s\">%s</a>",
          $exception->getMessage(),
          $exception->getFile(),
          $exception->getLine(),
          $exception->getTraceAsString(),
          $exception->getFile(),
          $exception->getLine(),
          $exception->getCode(),
          $url,
          $url
        );
        new GsdExceptionHandler()->sendMail(PROJECT . ' - [error]', nl2br($errorDetails));
        logToFile(__CLASS__, var_export($exception->getMessage(), true));
      }

      RestUtils::sendResponse(self::HTTP_INTERNAL_SERVER_ERROR, json_encode([
        'message' => $message,
        'data'    => $data,
      ]));
    }

    public static function sendResponseNotFound(string $message): void {
      RestUtils::sendResponse(self::HTTP_NOT_FOUND, json_encode([
        'message' => $message,
      ]));
    }

    public static function sendResponseUnauthorized(): void {
      RestUtils::sendResponse(self::HTTP_NOT_AUTHORIZED, json_encode([
        'message' => '401 Unauthorized',
      ]));
    }

    /**
     * @return void
     */
    public static function sendAccessDeniedResponse(): void {
      RestUtils::sendResponse(self::HTTP_ACCESS_DENIED);
    }

  }
