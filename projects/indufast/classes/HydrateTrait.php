<?php

  namespace classes;

  trait HydrateTrait {

    public static function getModelColumnSelect(string $modelName, string $prefix): array {
      return array_map(fn($col) => "$prefix.$col AS `$prefix|$col`", $modelName::columns);
    }

    public function hydrateRow(array $data, string $prefix): static {
      foreach (static::columns as $column) {
        $this->{$column} = $data["$prefix|$column"];
      }

      return $this;
    }
  }
