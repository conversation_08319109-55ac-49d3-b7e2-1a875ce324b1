<?php

  namespace classes;
  trait TimesTrait {

    private function duration(string $start, string $end, int $offsetMinutes = 0): string {
      $startSeconds = $this->timeToSeconds($start);
      $endSeconds = $this->timeToSeconds($end);

      $startSeconds += ($offsetMinutes * 60);

      $diffSeconds = $endSeconds - $startSeconds;

      if ($diffSeconds < 0) return '00:00:00';

      // Convert total second difference back to HH:MM:SS format.
      $hours = floor($diffSeconds / 3600);
      $minutes = floor(($diffSeconds % 3600) / 60);
      $seconds = $diffSeconds % 60;
      return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    function timeToSeconds(string $time): int {
      sscanf($time, "%d:%d:%d", $hours, $minutes, $seconds);
      return ($hours * 3600) + ($minutes * 60) + $seconds;
    }

    function addTimes(array $times): string {
      $totalSeconds = 0;

      foreach ($times as $time) {
        $isNegative = str_starts_with($time, '-');
        $time = ltrim($time, '-');

        [$hours, $minutes, $seconds] = array_map('intval', explode(':', $time));

        $timeInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
        if ($isNegative) $timeInSeconds = -$timeInSeconds;

        $totalSeconds += $timeInSeconds;
      }
      $sign = $totalSeconds < 0 ? '-' : '';
      $totalSeconds = abs($totalSeconds);
      $hours = floor($totalSeconds / 3600);
      $minutes = floor($totalSeconds / 60) % 60;
      $seconds = $totalSeconds % 60;

      return sprintf('%s%02d:%02d:%02d', $sign, $hours, $minutes, $seconds);
    }

    function subtractTimes(string $time1, string $time2, bool $allowNegative = true): string {
      [$hours1, $minutes1, $seconds1] = array_map('intval', explode(':', $time1));
      [$hours2, $minutes2, $seconds2] = array_map('intval', explode(':', $time2));

      $totalSeconds1 = ($hours1 * 3600) + ($minutes1 * 60) + $seconds1;
      $totalSeconds2 = ($hours2 * 3600) + ($minutes2 * 60) + $seconds2;

      if ($allowNegative) {
        $resultSeconds = $totalSeconds1 - $totalSeconds2;
        $negative = $resultSeconds < 0;
        $resultSeconds = abs($resultSeconds);
      }
      else {
        $resultSeconds = max(0, $totalSeconds1 - $totalSeconds2);
        $negative = false;
      }

      $hours = floor($resultSeconds / 3600);
      $minutes = floor(($resultSeconds % 3600) / 60);
      $seconds = $resultSeconds % 60;

      return ($negative ? '-' : '') . sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    function formatHoursAsString(float $hours): string {
      $hours = round($hours, 2);
      $formattedHours = floor($hours);
      $formattedMinutes = round(($hours - $formattedHours) * 60);
      return sprintf('%02d:%02d:00', $formattedHours, $formattedMinutes);
    }

    public static function validTime($input): bool {
      $vals = explode(':', $input, 3);
      if (count($vals) == 2) {
        $vals[] = '00';
      }
      [$hours, $minutes, $seconds] = $vals;

      return ((intval($hours) == $hours) && (intval($minutes) == $minutes) && (intval($seconds) == $seconds) && ($hours <= 23 && $hours >= 0) && ($minutes <= 59 && $minutes >= 0) && ($seconds <= 59 && $seconds >= 0));
    }

    public static function valid_line_time($input): bool {
      $vals = explode(':', $input, 3);
      if (count($vals) == 2) {
        $vals[] = '00';
      }
      [$hours, $minutes, $seconds] = $vals;

      return ((intval($hours) == $hours) && (intval($minutes) == $minutes) && (intval($seconds) == $seconds) && ($hours <= 47 && $hours >= 0) && ($minutes <= 59 && $minutes >= 0) && ($seconds <= 59 && $seconds >= 0));
    }
  }