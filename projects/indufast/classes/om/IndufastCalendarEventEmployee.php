<?php

  use classes\HydrateTrait;
  use classes\LogTrait;

  AppModel::loadModelClass('IndufastCalendarEventEmployeeModel');

  class IndufastCalendarEventEmployee extends IndufastCalendarEventEmployeeModel {

    use PropertyCastTrait;
    use ModelFillTrait;
    use ValidationTrait;
    use LogTrait;
    use HydrateTrait;

    protected array $fillable = [
      'calendar_event_id' => 'required|integer|exists:indufast_calendar_event,id',
      'employee_id'       => 'required|integer|exists:indufast_employee,id',
    ];

    const array CAST_PROPERTIES = [
      'id'                => 'int',
      'calendar_event_id' => 'int',
      'employee_id'       => 'int',
      'conflict'         =>  'object',
      'from_db'           => 'hidden',
    ];

    public ?IndufastEmployee $employee = null;
    public ?IndufastCalendarEvent $calendar_event = null;
    public int $parent_id;

    public function parent_name(): string {
      return IndufastProject::class;
    }

    public function parent_id(): int {
      return $this->parent_id ?? $this->parent_id = $this->event()->project_id;
    }

    public function employee(): IndufastEmployee {
      return ($this->employee) ?: $this->employee = IndufastEmployee::find_by_id($this->employee_id);
    }

    public function event(): IndufastCalendarEvent {
      return ($this->calendar_event) ?: $this->calendar_event = IndufastCalendarEvent::find_by_id($this->calendar_event_id);
    }

    public function conflict(): object|null {
      if (is_string($this->conflict)) {
        $this->conflict = json_decode($this->conflict);
      }
      return $this->conflict;
    }
  }