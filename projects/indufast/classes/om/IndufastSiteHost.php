<?php

  AppModel::loadModelClass('SiteHostModel');

  class IndufastSiteHost extends SiteHostModel {
    public function getDomainSmart(bool $withProt = false): string {
      $domain = $this->getDomain();
      $project = PROJECT;

      return match (ENVIRONMENT) {
        'PRODUCTION' => $domain,
        'LOCAL' => "$domain.$project.localhost",
        'STAGING' => "staging.$domain",
        default => throw new GsdException("Uknown ENVIRONMENT? ".ENVIRONMENT),
      };
    }

    public static function getBackendDeveloperUrl(): string {
      $siteHost = SiteHost::find_by(['site_id' => INDUFAST_BACKEND_SITE_ID]);
      $domain = $siteHost->getDomainSmart();
      $lang = $_SESSION['lang'];
      return "https://$domain/$lang/developer";
    }
  }