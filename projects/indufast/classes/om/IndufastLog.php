<?php

  AppModel::loadModelClass('IndufastLogModel');

  class IndufastLog extends IndufastLogModel {

    use PropertyCastTrait;

    public const array CAST_PROPERTIES = [
      'id'          => 'int',
      'entity_id'   => 'int',
      'parent_id'   => 'hidden',
      'parent_name' => 'hidden',
      'employee_id' => 'int',
      'from_db'     => 'hidden',
      'change_data' => 'hidden',
    ];

    public array $default_relations = [
      'employee',
    ];

    public array $diff = [];

    public function employee(): ?IndufastEmployee {
      if ($employee = IndufastEmployee::find_by_id($this->employee_id)) {
        return $employee;
      }
      return null;
    }

    public static function diff(IndufastLog $old, IndufastLog $new): array {
      $new_data = json_decode($new->change_data, true);
      $old_data = json_decode($old->change_data, true);

      return array_replace_recursive(
        static::arrayDiffRecursive($new_data, $old_data),
        static::arrayDiffRecursive($new_data, $old_data, true)
      );
    }

    /**
     * @see: https://stackoverflow.com/questions/3876435/recursive-array-diff#answer-33993246
     */
    protected static function arrayDiffRecursive($firstArray, $secondArray, $reverseKey = false): array {
      $oldKey = ($reverseKey) ? 'new' : 'old';
      $newKey = ($reverseKey) ? 'old' : 'new';
      $difference = [];

      foreach ($firstArray as $firstKey => $firstValue) {
        if (is_array($firstValue)) {
          if (!array_key_exists($firstKey, $secondArray) || !is_array($secondArray[$firstKey])) {
            $difference[$oldKey][$firstKey] = $firstValue;
            $difference[$newKey][$firstKey] = '';
          }
          else {
            $newDiff = self::arrayDiffRecursive($firstValue, $secondArray[$firstKey], $reverseKey);
            if ($newDiff) {
              $difference[$oldKey][$firstKey] = $newDiff[$oldKey];
              $difference[$newKey][$firstKey] = $newDiff[$newKey];
            }
          }
        }
        elseif (!array_key_exists($firstKey, $secondArray) || $secondArray[$firstKey] != $firstValue) {
          $difference[$oldKey][$firstKey] = $firstValue;
          $difference[$newKey][$firstKey] = array_key_exists($firstKey, $secondArray) ? $secondArray[$firstKey] : null;
        }
      }

      return $difference;
    }

  }