<?php

  use classes\LogTrait;

  AppModel::loadModelClass('IndufastWorkdaySummaryModel');

class IndufastWorkdaySummary extends IndufastWorkdaySummaryModel {

  use ModelFillTrait;
  use ModelTimeTrait;
  use ValidationTrait;
  use PropertyCastTrait;
  use LogTrait;

  const array CAST_PROPERTIES = [
    'id'          => 'int',
    'year'        => 'int',
    'month'       => 'int',
    'employee_id' => 'int',
    'from_db'     => 'hidden',
    'locked'      => 'boolean',
    'data'        => 'array',
  ];

  public IndufastEmployee $employee;

  protected array $fillable = [
    'year' => 'required|integer',
    'month' => 'required|integer|min:1|max:12',
    'employee_id' => 'required|integer|exists:indufast_employee,id',
    'locked' => 'boolean',
    'data' => 'required_if:locked,1',
  ];

  public function parent_name() {
    return self::class;
  }

  public function parent_id() {
    return $this->id;
  }

  public static function setMonthlyBalance(int $year, int $month, int $employeeId, string $monthlyBalance): void {
    $summary = self::find_by(['year' => $year, 'month' => $month, 'employee_id' => $employeeId]);
    if (!$summary) {
      $summary = new self();
      $summary->year = $year;
      $summary->month = $month;
      $summary->employee_id = $employeeId;
    }
    $summary->monthly_balance = $monthlyBalance;
    $summary->save();
  }

  public function save(array &$errors = []): mysqli_result|bool {
    $result = parent::save($errors);
    if ($this->locked) {
      $this->logOperation('lock_month');
    }
    return $result;
  }
}