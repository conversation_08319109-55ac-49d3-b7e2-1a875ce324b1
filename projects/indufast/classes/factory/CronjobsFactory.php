<?php

  use domain\accredis\service\AccredisImportService;
  use domain\project\service\ProjectUpdater;
  use domain\workday\service\WorkdayMailer;

  class CronjobsFactory extends Cronjobs {

    const int UPDATE_PROJECT_VALIDITY_INTERVAL_MINUTES = 5;
    const int ACCREDIS_IMPORT_INTERVAL_MINUTES = 15;

    public static function execute() {
      CronjobsFactory::backupMysql();
      JobQueue::executeJobs();

      // custom
      CronjobsFactory::updateProjectValidity();
      CronjobsFactory::cleanupPastEvents();
      CronjobsFactory::importAccredis();
      CronjobsFactory::sendWorkdayReport();

      CronjobsFactory::executeCleanDirTemp();
      CronjobsFactory::cleanLogDir();
    }

    /**
     * Check if there are planning conflicts.
     *
     * @throws \GsdDbException
     * @throws \GsdException
     * @throws \Exception
     */
    public static function updateProjectValidity(): void {
      if (ENVIRONMENT == 'LOCAL' || date('i') % self::UPDATE_PROJECT_VALIDITY_INTERVAL_MINUTES == 0) {
        logToFile("cron", sprintf('%s started', __FUNCTION__));
        ProjectUpdater::updateProjectValidity();
        logToFile("cron", sprintf('%s done', __FUNCTION__));
      }
    }

    /**
     * Cleans up past events in the project.
     * This method is scheduled to run at midnight every day.
     */
    public static function cleanupPastEvents(): void {
      if (ENVIRONMENT == 'LOCAL' || (date('H') == 0 && date('i') == 0)) { // Runs at midnight every day
        logToFile("cron", sprintf('%s started', __FUNCTION__));
        ProjectUpdater::cleanupPastEvents();
        logToFile("cron", sprintf('%s done', __FUNCTION__));
      }
    }

    /**
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function importAccredis(): void {
      if (ENVIRONMENT == 'LOCAL' || date('i') % self::ACCREDIS_IMPORT_INTERVAL_MINUTES == 0) {
        logToFile("cron", sprintf('%s started', __FUNCTION__));
        if ($result = new AccredisImportService()->checkUploadedFiles()) {
          logToFile("cron", var_export($result, true));
        }
        logToFile("cron", sprintf('%s done', __FUNCTION__));
      }
    }

    /**
     * Send weekly workday report to employees
     * Runs every tuesday at 08:00
     */
    public static function sendWorkdayReport(): void {
      if (ENVIRONMENT == 'LOCAL' || (date('H') == 8 && date('i') == 0 && date('w') == 2)) {
        logToFile("cron", sprintf('%s started', __FUNCTION__));
        new WorkdayMailer()->send();
        logToFile("cron", sprintf('%s done', __FUNCTION__));
      }
    }
  }