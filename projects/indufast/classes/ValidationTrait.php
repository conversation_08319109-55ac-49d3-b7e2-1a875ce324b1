<?php

  use classes\ApiResponse;
  use classes\rules\ExistsRule;
  use classes\rules\LockedMonthRule;
  use classes\rules\ProjectSameDayRule;
  use classes\rules\RangeRule;
  use classes\rules\UniqueRule;
  use classes\rules\TodayOrLaterRule;
  use Somnambulist\Components\Validation\ErrorBag;
  use Somnambulist\Components\Validation\Factory;
  use Somnambulist\Components\Validation\Validation;

  trait ValidationTrait {

    private function getFactory(): Factory {
      $factory = new Factory();
      $factory->addRule('unique', new UniqueRule());
      $factory->addRule('unique2', new UniqueRule());
      $factory->addRule('exists', new ExistsRule());
      $factory->addRule('project_same_day', new ProjectSameDayRule());
      $factory->addRule('range', new RangeRule());
      $factory->addRule('today_or_later', new TodayOrLaterRule());
      $factory->addRule('locked_month', new LockedMonthRule());

      return $factory;
    }

    protected function replacePlaceholdersWithProperties(string $string): string {
      return preg_replace_callback('/{(\w+)}/', function($matches) {
        return property_exists($this, $matches[1]) ? $this->{$matches[1]} : $matches[0];
      }, $string);
    }

    protected function setPropertyValues($fillable): array {
      return array_map(function($item) {
        if (is_string($item)) {
          return $this->replacePlaceholdersWithProperties($item);
        } elseif (is_array($item)) {
          return array_map(function ($value) {
            return is_string($value) ? $this->replacePlaceholdersWithProperties($value) : $value;
          }, $item);
        }
        return $item;
      }, $fillable);
    }

    /**
     * Validate the model properties against the fillable field definitions.
     */
    public function validateFillable(): void {
      $validation = $this->getFactory()->make(get_object_vars($this), $this->setPropertyValues($this->getFillable()));
      $validation->validate();

      if ($validation->fails()) {
        ApiResponse::sendResponseBadRequest('errors', $this->parseFillableErrors($validation->errors()));
      }
    }

    public function getValidationErrors(): array {
      $validation = $this->getFactory()->make(get_object_vars($this), $this->setPropertyValues($this->getFillable()));
      $validation->validate();
      return $this->parseFillableErrors($validation->errors());
    }

    protected function validateData(array $data, array $rules): Validation {
      $validation = $this->getFactory()->make($data, $rules);
      $validation->validate();

      if ($validation->fails()) {
        ApiResponse::sendResponseBadRequest('Invalid request', $validation->errors()->all());
      }

      return $validation;
    }

    public function getFillable(): array {
      return $this->fillable ?? [];
    }

    /**
     * Parse the errors from the validation into a keyed array.
     */
    private function parseFillableErrors(ErrorBag $errors): array {
      $results = [];

      foreach (array_keys($this->getFillable()) as $key) {
        $results[$key] = array_values($errors->get($key));
      }

      return array_filter($results);
    }

  }

