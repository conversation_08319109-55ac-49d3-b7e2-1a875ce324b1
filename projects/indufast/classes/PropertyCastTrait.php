<?php

  trait PropertyCastTrait {
    protected array $loaded = [];

    public function castProperties(): void {
      $this->load();
      unset($this->default_relations);
      $this->castPropertiesToTypes();
    }

    public function castPropertiesToTypes(): void {
      foreach (self::CAST_PROPERTIES as $property => $type) {
        $rp = new ReflectionProperty(get_class($this), $property);
        if ($rp->isInitialized($this) && is_null($this->{$property}) && $type !== 'hidden') {
          continue;
        }

        switch ($type) {
          case 'float':
            $this->{$property} = (float)$this->{$property};
            break;

          case 'int':
            $this->{$property} = (int)$this->{$property};
            break;

          case 'hidden':
            unset($this->{$property});
            break;

          case 'boolean':
            $this->{$property} = (bool)$this->{$property};
            break;

          case 'array':
            if (!is_array($this->{$property})) {
              $this->{$property} = json_decode($this->{$property}, true);
            }
            break;

          case 'object':
            if (is_string($this->{$property})) {
              $this->{$property} = json_decode($this->{$property});
            }
            break;

          default:
            throw new Exception(sprintf("Property type '%s' is not supported.", $type));
        }
      }
    }

    public function load(?array $relations = null): self {
      if (is_null($relations)) {
        $relations = $this->default_relations ?? [];
      }

      foreach ($relations as $relation) {
        if (!in_array($relation, $this->loaded)) {
          $this->{$relation}();
          $this->loaded[] = $relation;
        }
      }

      return $this;
    }

    protected static function castAllModels(array|object $data): void {
      $items = is_array($data) ? $data : [$data];

      foreach ($items as $value) {
        if (is_object($value)) {
          if (method_exists($value, 'castProperties')) {
            $value->castProperties();
          }

          foreach (array_keys(get_object_vars($value)) as $object_var) {
            if (is_array($value->$object_var)) {
              self::castAllModels($value->$object_var);
            } elseif (is_object($value->$object_var)) {
              if (method_exists($value->$object_var, 'castProperties')) {
                $value->$object_var->castProperties();
              }
            }
          }
        } elseif (is_array($value)) {
          self::castAllModels($value);
        }
      }
    }

    /**
     * @deprecated.
     */
    public function castAllProperties(array $objects): array {
      foreach ($objects as $object) {
        $object->castProperties();
      }

      return $objects;
    }

    /**
     * @throws Exception
     */
    public function castPropertiesForSave(): void {
      foreach (self::CAST_PROPERTIES as $property => $type) {
        if (!in_array($property, self::columns)) {
          continue;
        }

        switch ($type) {
          case 'float':
          case 'int':
          case 'hidden':
            // No cast needed for these types.
            break;

          case 'boolean':
            $this->{$property} = $this->{$property} ? 1 : 0;
            break;

          case 'array':
            $this->{$property} = ($this->{$property}) ? json_encode((array)$this->{$property}) : null;
            break;

          default:
            throw new Exception(sprintf("Property type '%s' is not supported.", $type));
        }
      }

      if ($this->from_db) {
        $this->updateTS = date("Y-m-d H:i:s");
      }
      else {
        $this->insertTS = date("Y-m-d H:i:s");
      }
    }


  }