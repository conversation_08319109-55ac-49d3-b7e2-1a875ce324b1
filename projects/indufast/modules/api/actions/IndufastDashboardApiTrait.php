<?php

  use classes\ApiResponse;
  use domain\accredis\service\AccredisImportService;

  trait IndufastDashboardApiTrait {

    public function executeDashboard(): void {
      $lastAccredisImport = \Setting::getValueByCode(AccredisImportService::LAST_ACCREDIS_IMPORT_CODE);
      $lastAccredisImportResult = \Setting::getValueByCode(AccredisImportService::LAST_ACCREDIS_IMPORT_RESULT_CODE);
      $open_workdays = \IndufastWorkday::count_all_by(['status' => \IndufastWorkday::STATUS_NEW]);
      $events = IndufastCalendarEvent::count_all_by([], sprintf("WHERE start >= '%s' AND end <= '%s'",
        date('Y-m-d') . ' 00:00:00',
        date('Y-m-d') . ' 23:59:59',
      ));


      $data = [
        'last_accredis_import' => $lastAccredisImport ?: null,
        'last_accredis_import_result' => $lastAccredisImportResult ? json_decode($lastAccredisImportResult, true) : null,
        'open_workdays' => $open_workdays,
        'events_today' => $events,
      ];

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $data);
    }

  }