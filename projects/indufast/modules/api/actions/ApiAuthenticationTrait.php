<?php

  use classes\ApiResponse;
  use domain\google\service\GoogleAuthService;

  trait ApiAuthenticationTrait {

    protected function authenticate(): void {
      if (empty($_SESSION['loggedIn']) || empty($_SESSION['userObject'])) {
        ApiResponse::sendResponseUnauthorized();
      }
    }

    public function executeLogin(): void {
      $requestData = $this->data;

      $loginEmail = trim($requestData['email'] ?? '');
      $loginPassword = trim($requestData['password'] ?? '');
      $loginResult = $this->loginByEmail($loginEmail, $loginPassword, Site::getLoginUsergroups());

      if (is_a($loginResult, User::class)) {
        $user = (object)[ // only return needed fields
          'email'     => $loginResult->email,
          'firstName' => $loginResult->firstname,
          'insertion' => $loginResult->insertion,
          'lastName'  => $loginResult->lastname,
          'usergroup' => $loginResult->usergroup
        ];
        $service = new GoogleAuthService();
        $config = $service->getGooglePickerConfig();

        ApiResponse::sendResponseOK('Je bent succesvol ingelogd', [
          'user' => $user,
          'config' => $config,
          'developer_url' => IndufastSiteHost::getBackendDeveloperUrl(),
          'environment' => ENVIRONMENT
        ]);
      }

      ApiResponse::sendResponseBadRequest($loginResult[0]);
    }

    public function executeLogout(): void {
      GsdSession::stopSession(true);
      ApiResponse::sendResponseOK('Je bent succesvol uitgelogd');
    }

    public function executeGoogleAuthUrl(): void {
      try {
        $googleAuth = new GoogleAuthService();
        $authUrl = $googleAuth->getAuthUrl();
        ApiResponse::sendResponseOK('Google auth URL generated', ['url' => $authUrl]);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError('Failed to generate Google auth URL: ' . $e->getMessage(), [], $e);
      }
    }

    public function executeGoogleAuthCallback(): void {
      try {
        $code = $_GET['code'] ?? null;
        if (!$code) {
          throw new \Exception('Er is iets misgegaan tijdens de authenticatie. Probeer het opnieuw.');
        }

        $googleAuth = new GoogleAuthService();
        $oauthUser = $googleAuth->getOAuthUser($code);

        $employee = IndufastEmployee::find_by(['email' => $oauthUser->email]);
        if (!$employee) {
          throw new \Exception('Je account is niet geautoriseerd om in te loggen.');
        }
        $employee->castProperties();
        if (!$employee->may_login) {
          throw new \Exception('Je account is niet geautoriseerd om in te loggen.');
        }

        $user = User::find_by(['email' => $oauthUser->email]);
        $needsSave = false;
        if (!$user) {
          $user = new User();
          $user->email = $oauthUser->email;
          $user->firstname = $oauthUser->givenName;
          $user->lastname = $oauthUser->familyName;
          $user->organisation_id = INDUFAST_ORGANISATION_ID;
          $needsSave = true;
        }
        if ($user->usergroup !== $employee->usergroup && $user->usergroup !== User::USERGROUP_SUPERADMIN) {
          $user->usergroup = $employee->usergroup;
          $needsSave = true;
        }
        if ($needsSave) $user->save();

        GsdSession::startSession($user);

        $userInfo = [
          'email'     => $user->email,
          'firstName' => $user->firstname,
          'insertion' => $user->insertion,
          'lastName'  => $user->lastname,
          'usergroup' => $user->usergroup
        ];
        $token = $googleAuth->getToken();
        $config = $googleAuth->getGooglePickerConfig();

        $params = [
          'google_login_success' => 'true',
          'user'                 => json_encode($userInfo),
          'token'                => json_encode($token),
          'config'               => json_encode($config),
          'developer_url'        => IndufastSiteHost::getBackendDeveloperUrl(),
          'environment'          => ENVIRONMENT
        ];

        // We use a different frontend URL for development to use Google auth
        $host = DEVELOPMENT ? 'http://localhost:3000' : '';
        ResponseHelper::redirect("$host/login?" . http_build_query($params));
      }
      catch (\Exception $e) {
        $params = [
          'google_login_error' => $e->getMessage()
        ];
        ResponseHelper::redirect('/login?' . http_build_query($params));
      }
    }
  }