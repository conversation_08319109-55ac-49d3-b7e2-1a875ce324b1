<?php

  use classes\ApiResponse;

  trait IndufastCalendarEventApiTrait {

    public function executeEventCreate(): void {
      $event = new IndufastCalendarEvent();
      $event->fill($this->data)->validateFillable();

      try {
        IndufastCalendarEvent::batchSave([$event]);
        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while saving.', [], $e);
      }
    }

    public function executeEventUpdate(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }
      $event->fill($this->data)->validateFillable();

      try {
        IndufastCalendarEvent::batchSave([$event]);
        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while saving.', (DEVELOPMENT) ? $e->getMessage() : '', $e);
      }
    }

    public function executeEventDelete(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }

      try {
        $event->destroy();
        ApiResponse::sendResponseOK('Event deleted', $event);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while deleting.', [], $e);
      }
    }

    public function executeEventBatchSave(): void {
      try {
        $events = [];
        foreach ($this->data as &$eventData) {
          $eventId = $eventData['id'] ?? null;
          $event = $eventId ? IndufastCalendarEvent::find_by_id($eventId) : new IndufastCalendarEvent();
          $errors = $event->fill($eventData)->getValidationErrors();
          $eventData['errors'] = $errors ?: [];
          if ($errors) continue;

          $events[] = $event;
        }

        if (array_any($this->data, fn($event) => !empty($event['errors']))) {
          ApiResponse::sendResponseError('Events have errors', $this->data);
        } else {
          IndufastCalendarEvent::batchSave($events);
          ApiResponse::sendResponseOK('Events saved', $this->data);
        }
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.');
      }
    }

    public function executeEventBatchDelete(): void {
      try {
        $inClause = DbHelper::getSqlIn('id', $this->data);
        $events = IndufastCalendarEvent::find_all("WHERE $inClause");
        IndufastCalendarEvent::batchDestroy($events);

        ApiResponse::sendResponseOK('Events deleted', $this->data);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while deleting.');
      }
    }

    public function executeEventGetGoogleFiles(): void {
      $rules = [
        'ids' => 'required|array',
        'ids.*' => 'integer|min:1',
      ];
      $validation = $this->validateData($_GET, $rules);
      $data = $validation->getValidatedData();

      $eventIds = $data['ids'];
      $inClause = DbHelper::getSqlIn('id', $eventIds);
      $events = IndufastCalendarEvent::find_all("WHERE $inClause");

      try {
        $filesByEvent = IndufastCalendarEvent::getGoogleFilesBatch($events);
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $filesByEvent);
      } catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while retrieving files.');
      }
    }
  }