<?php

  use classes\ApiResponse;
  use PhpOffice\PhpSpreadsheet\IOFactory;
  use PhpOffice\PhpSpreadsheet\Shared\Date;
  use PhpOffice\PhpSpreadsheet\Spreadsheet;
  use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

  trait IndufastWorkdayApiTrait {

    public function executeWorkdayList(): void {
      $rules = [
        'employee_id' => 'required|integer|exists:indufast_employee,id',
        'year' => 'required|date:Y',
        'month' => 'required|date:m',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      $workdays = IndufastWorkday::find_all_by_data($data);

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workdays);
    }

    public function executeGetHolidays(): void {
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, IndufastCalendarEvent::holidays());
    }

    public function executeWorkdayExport(): void {
      $rules = [
        'employee_id' => 'integer|exists:indufast_employee,id',
        'start' => 'required|date:Y-m-d|range:end,asc,true',
        'end' => 'required|date:Y-m-d|range:start,desc,true',
      ];
      $rows = $this->validateData($_GET, $rules)->getValidatedData();

      $filters = [];
      $filters[] = 'date >= "' . $rows['start'] . '"';
      $filters[] = 'date <= "' . $rows['end'] . '"';
      $filters[] = 'status = "' . IndufastWorkday::STATUS_PROCESSED . '"';
      if (isset($rows['employee_id'])) {
        $filters[] = 'employee_id = ' . $rows['employee_id'];
      }

      if (!$workdays = IndufastWorkday::find_all("WHERE " . implode(' AND ', $filters) . " ORDER BY date ASC")) {
        ApiResponse::sendResponseNotFound('No workdays found for the given criteria');
      }
      $employees = AppModel::mapObjectIds(IndufastEmployee::find_all_by(['id' => array_unique(array_column($workdays, 'employee_id'))]));

      $query = sprintf(
        "SELECT DATE(ice.start) AS date, ip.name FROM indufast_project AS ip
            JOIN indufast_calendar_event AS ice ON ice.project_id = ip.id
            JOIN indufast_calendar_event_employee AS icee ON icee.calendar_event_id = ice.id AND icee.employee_id IN (%s)
            WHERE ice.start BETWEEN '%s' AND '%s'",
        implode(', ', array_keys($employees)),
        $rows['start'] . ' 00:00:00',
        $rows['end'] . ' 23:59:59',
      );
      $result = DBConn::db_link()->query($query);

      $projects = [];
      while ($row = $result->fetch_assoc()) {
        $projects[$row['date']][] = $row['name'];
      }

      $rows = [];
      foreach ($workdays as $workday) {
        $workday->calculate();

        $rows[] = [
          $workday->date,
          $employees[$workday->employee_id]->name,
          implode(', ', $projects[$workday->date] ?? []),
          $workday->travelToDuration,
          $workday->travelToDurationNet,
          $workday->travelToDistance,
          $workday->workdayDuration,
          $workday->workdayPause,
          $workday->overtimeBelow,
          $workday->overtimeAbove,
          $workday->workdayDurationNetClipped,
          $workday->travelFromDuration,
          $workday->travelFromDurationNet,
          $workday->travelFromDistance,
        ];
      }

      // Generate the spreadsheet.
      $spreadsheet = new Spreadsheet();
      $spreadsheet->setActiveSheetIndex(0);
      $sheet = $spreadsheet->getActiveSheet();

      // Add the header.
      $header = [
        'Datum' => 'date',
        'Medewerker' => 'string',
        'Project(en)' => 'string',
        'Reistijd heen bruto' => 'time',
        'Reistijd heen netto' => 'time',
        'Reisafstand heen' => 'distance',
        'Werktijd bruto' => 'time',
        'Pauze' => 'time',
        'Overwerk +25%' => 'time',
        'Overwerk +50%' => 'time',
        'Werktijd netto' => 'time',
        'Reistijd terug bruto' => 'time',
        'Reistijd terug netto' => 'time',
        'Reisafstand terug' => 'distance',
      ];

      $bold = ['font' => [ 'bold' => true ]];

      $x = 1;
      foreach (array_keys($header) as $name) {
        $sheet->setCellValue([$x, 1], $name);
        $style = $sheet->getStyle([$x, 1]);
        $style->applyFromArray($bold);
        $x++;
      }

      // Add the workday rows.
      $y = 2;
      foreach ($rows as $row) {
        foreach (array_values($header) as $x => $type) {
          $style = $sheet->getStyle([$x + 1, $y]);

          switch ($type) {
            case 'date':
              $sheet->setCellValue([$x + 1, $y], Date::PHPToExcel($row[$x]));
              $style->getNumberFormat()->setFormatCode('d-m-yyyy');
              break;

            case 'time':
              if ($row[$x]) {
                $sheet->setCellValue([$x + 1, $y], Date::PHPToExcel('1900-01-01 ' . $row[$x]) - 1);
              }
              $style->getNumberFormat()->setFormatCode('[h]:mm:ss');
              break;

            case 'distance':
              if ($row[$x]) {
                $sheet->setCellValue([$x + 1, $y], $row[$x]);
              }
              $style->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_0);
              break;

            default:
              $sheet->setCellValue([$x + 1, $y], $row[$x]);
          }
        }
        $y++;
      }

      // Add the totals row.
      foreach (array_values($header) as $x => $type) {
        $start = $sheet->getCell([$x + 1, 2])->getCoordinate();
        $end = $sheet->getCell([$x + 1, $y - 1])->getCoordinate();
        $style = $sheet->getStyle([$x + 1, $y]);

        switch ($type) {
          case 'time':
            $sheet->setCellValue([$x + 1, $y], "=SUM($start:$end)");
            $style->getNumberFormat()->setFormatCode('[h]:mm:ss');
            $style->applyFromArray($bold);
            break;

          case 'distance':
            $sheet->setCellValue([$x + 1, $y], "=SUM($start:$end)");
            $style->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_0);
            $style->applyFromArray($bold);
            break;
        }
      }

      // General sheet settings.
      $sheet->freezePane('A2');
      foreach ($sheet->getColumnIterator() as $column) {
        $sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
      }

      // Download the file.
      header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      header('Content-Disposition: attachment;filename="indufast_workday_export_' . date("dmY") . '.xlsx"');
      header('Cache-Control: max-age=0');
      $objWriter = IOFactory::createWriter($spreadsheet, 'Xlsx');
      $objWriter->save('php://output');

      $this->template = null;
    }

    /**
     * @throws Exception
     */
    public function executeWorkdayCalculate(): void {
      if (!$workday = IndufastWorkday::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Workday not found');
      }
      $workday->fill($this->data)->validateFillable();

      $rules = [
        'workdayLinesData' => 'array',
        'workdayLinesData.*.void' => 'boolean',
      ];
      $data = $this->validateData($this->data, $rules)->getValidatedData();

      $workdayLinesData = $data['workdayLinesData'] ?? [];
      foreach ($workday->lines() as $workdayLine) {
        $workdayLine->setWorkday($workday);
        $workdayLine->void = ($workdayLinesData[$workdayLine->id]['void'] ?? false);
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

    public function executeWorkdaySummary(): void {
      $rules = [
        'employee_id' => 'required|integer|exists:indufast_employee,id',
        'year' => 'required|date:Y',
        'month' => 'required|date:m',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();
      $data['status'] = IndufastWorkday::STATUS_PROCESSED;

      // Check if the month is locked and return stored data if it is
      $lockedSummary = IndufastWorkdaySummary::find_by([
        'year' => $data['year'],
        'month' => $data['month'],
        'employee_id' => $data['employee_id'],
        'locked' => 1
      ]);

      if ($lockedSummary && $lockedSummary->data) {
        $storedData = json_decode($lockedSummary->data, true);
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $storedData);
      }

      try {
        // If not locked, calculate the values
        $workdays = IndufastWorkday::findAllByMonth($data['employee_id'], $data['year'], $data['month'], true);
        foreach ($workdays as $workday) {
          $workday->calculate();
        }

        $employee = IndufastEmployee::find_by_id($data['employee_id']);
        $totalNet = $this->addTimes(array_filter(array_column($workdays, 'workdayDurationNet')));
        $totalSpecialHours = $this->addTimes(array_filter(array_column($workdays, 'specialHoursDuration')));
        $totalForBalance = $this->addTimes([$totalNet, $totalSpecialHours]);
        $hoursPerMonth = $this->formatHoursAsString(IndufastWorkday::HOURS_PER_MONTH * $employee->monthly_percentage / 100);

        // Set balance to 0 if no workdays are found.
        $monthlyBalance = $workdays ? $this->subtractTimes($totalForBalance, $hoursPerMonth) : '00:00:00';
        IndufastWorkdaySummary::setMonthlyBalance($data['year'], $data['month'], $employee->id, $monthlyBalance);

        $summaries = IndufastWorkdaySummary::find_all_by(['employee_id' => $employee->id]);
        $totalBalance = $this->addTimes(array_filter(array_column($summaries, 'monthly_balance')));

        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, [
          'count'                    => count($workdays),
          'total'                    => $totalNet,
          'hoursPerMonth'            => $hoursPerMonth,
          'monthlyBalance'           => $monthlyBalance,
          'totalBalance'             => $totalBalance,
          'totalSaturday'            => $this->addTimes(array_filter(array_column($workdays, 'workdayDurationSaturdayNet'))),
          'totalSundayOrHoliday'     => $this->addTimes(array_filter(array_column($workdays, 'workdayDurationSundayOrHolidayNet'))),
          'totalOvertimeBelow'       => $this->addTimes(array_filter(array_column($workdays, 'overtimeBelow'))),
          'totalOvertimeAbove'       => $this->addTimes(array_filter(array_column($workdays, 'overtimeAbove'))),
          'totalOutsideWorkingHours' => $this->addTimes(array_filter(array_column($workdays, 'workdayOutsideWorkingHours'))),
          'totalTravel'              => $this->addTimes(array_filter(array_column($workdays, 'travelDurationNet'))),
          'totalLeave'               => $this->addTimes(array_filter(array_column($workdays, 'specialHoursLeave'))),
          'totalSpecialLeave'        => $this->addTimes(array_filter(array_column($workdays, 'specialHoursSpecialLeave'))),
          'totalSick'                => $this->addTimes(array_filter(array_column($workdays, 'specialHoursSick'))),
          'totalUnexcusedLeave'      => $this->addTimes(array_filter(array_column($workdays, 'specialHoursUnexcusedLeave'))),
        ]);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while calculating.', [], $e);
      }
    }

    public function executeWorkdaySummaryLock(): void {
      $rules = [
        'employee_id' => 'required|integer',
        'year' => 'required|integer',
        'month' => 'required|integer',
        'data' => 'required|array',
      ];
      $data = $this->validateData($this->data, $rules)->getValidatedData();

      try {
        $summary = IndufastWorkdaySummary::find_by(['year' => $data['year'], 'month' => $data['month'], 'employee_id' => $data['employee_id']]);
        if (!$summary) {
          $summary = new IndufastWorkdaySummary();
        }

        $data['locked'] = true;

        $summary->fill($data)->validateFillable();
        $summary->castPropertiesForSave();
        $summary->save();

        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, []);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while locking.', [], $e);
      }
    }

    public function executeWorkdayCreate(): void {
      $workday = new IndufastWorkday();
      $workday->fill($this->data)->validateFillable();

      try {
        $workday->save();
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while saving.', [], $e);
      }
    }

    public function executeWorkdayUpdate(): void {
      if (!$workday = IndufastWorkday::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Workday not found');
      }

      $workday->fill($this->data)->validateFillable();

      $rules = [
        'workdayLinesData' => 'array',
        'workdayLinesData.*.void' => 'boolean',
      ];
      $data = $this->validateData($this->data, $rules)->getValidatedData();

      $workdayLinesData = $data['workdayLinesData'] ?? [];
      foreach ($workday->lines() as $workdayLine) {
        if (!empty($workdayLinesData[$workdayLine->id])) {
          $newVoid = $workdayLinesData[$workdayLine->id]['void'] ?? false;
          if ((bool)$workdayLine->void !== $newVoid) {
            $workdayLine->void = ($newVoid) ? 1 : 0;
            $workdayLine->updateTS = date("Y-m-d H:i:s");
            $workdayLine->save();
          }
        }
      }

      // Save and return workday.
      $workday->updateTS = date("Y-m-d H:i:s");
      $workday->save();
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

    public function executeWorkdaySpecialHoursList(): void {
      $rules = [
        'workday_id' => 'required|integer|exists:indufast_workday,id',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      $specialHours = IndufastWorkdaySpecialHours::find_all_by(['workday_id' => $data['workday_id']]);
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $specialHours);
    }

    public function executeWorkdaySpecialHoursCreate(): void {
      $specialHours = new IndufastWorkdaySpecialHours();
      $specialHours->fill($this->data)->validateFillable();

      try {
        $specialHours->save();

        // Return the updated workday with special hours
        $workday = IndufastWorkday::find_by_id($specialHours->workday_id);
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while saving.', [], $e);
      }
    }

    public function executeWorkdaySpecialHoursUpdate(): void {
      if (!$specialHours = IndufastWorkdaySpecialHours::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Special hours not found');
      }

      $specialHours->fill($this->data)->validateFillable();

      try {
        $specialHours->save();

        // Return the updated workday with special hours
        $workday = IndufastWorkday::find_by_id($specialHours->workday_id);
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while saving.', [], $e);
      }
    }

    public function executeWorkdaySpecialHoursDelete(): void {
      if (!$specialHours = IndufastWorkdaySpecialHours::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Special hours not found');
      }

      try {
        $workdayId = $specialHours->workday_id;
        $specialHours->validateFillable();
        $specialHours->destroy();

        // Return the updated workday with special hours
        $workday = IndufastWorkday::find_by_id($workdayId);
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while deleting.', [], $e);
      }
    }
  }
