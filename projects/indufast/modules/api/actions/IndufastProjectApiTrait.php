<?php

  use classes\ApiResponse;

  trait IndufastProjectApiTrait {
    use classes\HydrateTrait;

    public function executeProjectCreate(): void {
      $project = new IndufastProject();
      $project->fill($this->data)->validateFillable();

      try {
        $project->save();

        ApiResponse::sendResponseOK('Project saved', $project);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while saving.', [], $e);
      }
    }

    /**
     * @throws Exception
     */
    public function executeProjectList(): void {
      $rules = [
        'date'        => 'prohibited_unless:year,|date:Y-m-d',
        'archive'     => 'string|in:true,false',
        'year'        => 'required_with:month|date:Y',
        'month'       => 'date:m',
        'employee_id' => 'integer|nullable|exists:indufast_employee,id',

        'google_calendar_event_ids' => 'array|nullable',
        'google_calendar_event_ids.*' => 'string',
      ];
      $validation = $this->validateData($_GET, $rules);
      $data = $validation->getValidatedData();

      $projects = IndufastProject::find_by_data($data);

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, array_values($projects));
    }

    public function executeProjectUpdate(): void {
      if (!$project = IndufastProject::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Project not found');
      }

      $project->fill($this->data)->validateFillable();

      try {
        $project->save();
        ApiResponse::sendResponseOK('Project saved', $project);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while saving.', (DEVELOPMENT) ? $e->getMessage() : '', $e);
      }
    }

    public function executeProjectDelete(): void {
      if (!$project = IndufastProject::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Project not found');
      }

      try {
        $project->void = true;
        $project->save();

        ApiResponse::sendResponseOK('Project deleted', $project);
      }
      catch (\Exception $e) {
        ApiResponse::sendResponseError('Unknown error occured while deleting.', [], $e);
      }
    }


  }