<?php

  use classes\ApiResponse;

  trait IndufastLogApiTrait {

    public function executeLogList(): void {
      $rules = [
        'entity_name' => 'required|string|in:IndufastProject,IndufastEmployee,IndufastWorkday',
        'entity_id'   => 'required|integer',
      ];
      $validation = $this->validateData($_GET, $rules);
      $data = $validation->getValidatedData();

      // @TODO: Check if GROUP BY is needed here to prevent "empty changes".
      $logs = IndufastLog::find_all_by(
        ['parent_name' => $data['entity_name'], 'parent_id' => $data['entity_id']],
        "GROUP BY action, entity_name, entity_id, change_data ORDER BY action, entity_name, entity_id, id ASC"
      );

      $old_log = null;
      foreach ($logs as $log) {
        if ($log->action === 'create_entity') {
          $log->diff = ['new' => json_decode($log->change_data, true)];
          $old_log = null;
          continue;
        }

        if ($log->action === 'destroy_entity') {
          $log->diff = ['old' => json_decode($log->change_data, true)];
          $old_log = null;
          continue;
        }

        if ($old_log) {
          if ($old_log->entity_name === $log->entity_name && $old_log->entity_id === $log->entity_id) {
            $log->diff = IndufastLog::diff($old_log, $log);
          }
          else {
            $old_log = null;
            continue;
          }
        }

        $old_log = $log;
      }

      usort($logs, fn($a, $b) => $b->id <=> $a->id);

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $logs);
    }

  }