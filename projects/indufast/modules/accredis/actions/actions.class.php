<?php

  use domain\accredis\service\AccredisImportService;

  class accredisIndufastActions extends gsdActions {

    public array $errors;
    public array $warnings;

    public function executeAccredis(): void {
      $result = [
        'filename' => null,
        'time' => date('Y-m-d H:i:s'),
        'messages' => [],
        'errors' => [],
        'warnings' => [],
      ];
      $service = new AccredisImportService();

      if (isset($_FILES['bestand_accredis_xml_export'])) {
        try {
          $filename = $this->handleAccredisXMLUpload();
          $result['filename'] = basename($filename);
          $trips = $service->parseAccredisXMLUpload($filename);
          $result = $service->saveTrips($trips, $filename);
        }
        catch (Exception $e) {
          $result['errors'][] = $e->getMessage();
        }
      }

      $service->logResults([$result]);
      MessageFlashCoordinator::addMessages($result['messages']);
      $this->errors = array_filter($result['errors']);
      $this->warnings = array_filter($result['warnings']);
    }

    /**
     * @throws Exception
     */
    private function handleAccredisXMLUpload(): string {
      $file_uploader = new Uploader("accredis_xml_export", reconstructQuery(), DIR_TEMP);
      $file_uploader->setCreateUniqueFilenameBool(true);
      $file_uploader->setShowuploadbut(true);
      $file_uploader->setAllowed(['text/xml' => 'xml']);
      $file_uploader->setMaxfilesizeByMb(10);
      $result = $file_uploader->parseUpload('', false);

      if ($file_uploader->hasErrors()) {
        throw new Exception('Fout bij het uploaden: ' . $file_uploader->getErrorsFormatted());
      }
      if ($result) {
        $file_uploader->moveUploadedFile();
        return $file_uploader->getFilepath();
      }

      throw new Exception('Fout bij het uploaden.');
    }
  }