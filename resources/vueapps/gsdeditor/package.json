{"name": "webappv2", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build-dev": "vite build --mode development", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@tinymce/tinymce-vue": "^5.0.1", "tinymce": "^7.1.2", "tinymce-i18n": "^24.6.10", "vite": "^4.0.0", "vue-sweetalert2": "^5.0.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "vite": "^4.0.0"}}