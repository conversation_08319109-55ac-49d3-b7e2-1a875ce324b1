<template>
  <div class="block-topbar">
    Vrije editor
    <div class="toolbox">
      <a v-html="IconHelper.ICON_EDIT" href="" @click.prevent="toolboxShow = !toolboxShow"  title="Bewerk eigenschappen" class="gsd-svg-icon-a"></a>
      <div class="toolbox-wrapper" v-show="toolboxShow" :ref="setToolboxEl">
        <div class="toolbox-head">
          Widget eigenschappen
          <a href="" v-html="IconHelper.ICON_CROSS" @click.prevent="toolboxShow = !toolboxShow"/>
        </div>
        <div class="toolbox-content">
          <div class="toolbox-content-row" v-if="classesWidget.length > 0">
            <div>Classes</div>
            <div>
              <div v-for="item in classesWidget" :key="item.class">
                <label :title="item.class">
                  <input type="checkbox" :value="item.class" v-model="wysiwygBlock.attributes.classes"/>
                  {{ item.description }}
                </label>
              </div>
            </div>
          </div>
          <div class="toolbox-content-row">
            <div>Class</div>
            <input type="text" v-model="wysiwygBlock.attributes.class" placeholder="Css class..."/>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="wysiwyg" :id="'container_'+this.editorId">
    <Editor
        :id="editorId"
        :key="editorId"
        api-key="5vtccjleslypobjwkogi5jhpgxkhw3pyvnaogvcdv4w697zu"
        v-model="wysiwygBlock.attributes.content"
        @keyup="onBlockChange"
        @change="onBlockChange"
        @init="onInit"
        :init="config"
    />
  </div>
</template>

<script>

import "tinymce/tinymce";
import "tinymce/themes/silver";
import "tinymce/skins/ui/oxide/skin.css";

import "tinymce/icons/default";
import "tinymce/models/dom/model";
import "tinymce/plugins/lists";
import "tinymce/plugins/link";
import "tinymce/plugins/image";
import "tinymce/plugins/table";
import "tinymce/plugins/code";
// import "tinymce/plugins/help";
import "tinymce/plugins/wordcount";
import "tinymce-i18n/langs7/nl";

import Editor from '@tinymce/tinymce-vue'
import IconHelper from "@gsdvuefw/helpers/IconHelper";
import WysiwygHelper from "../helpers/WysiwygHelper";
import projectClasses from '@/composables/projectClasses';

export default {
  name: 'Wysiwyg',
  emits: ["change"],
  props: {
    id: String|Number,
    order: {
      type: Number,
      default: 0,
    },
    wysiwygBlock: {}
  },
  components: {
    Editor,
  },
  data() {
    return {
      IconHelper,
      WysiwygHelper,
      editorId: this.id,
      config: WysiwygHelper.defaultConfig,
      toolboxShow: false,
      classesWidget: projectClasses().widget,
    }
  },
  created() {
    // console.log("wysiwygBlock() CREATED "+this.id);
    this.init();
    document.addEventListener('mouseup', this.clickOutside);
  },
  watch: {
    wysiwygBlock() { //wijziging van wysiwygBlock, change content
      // console.log("wysiwygBlock() CHANGED "+this.id);
      this.init();
    },
    order(newValue) {

      if(tinymce.get(this.id)==undefined) {
        // console.log("NOT FOUND: "+this.id);
        return;
      }
      // console.log("ORDER: "+this.id);

      let app = this;
      //container order has changed, update wyswig
      tinymce.init({});
      // tinymce.activeEditor.init();
      // tinymce.get("editor_0").setContent("ROBERT");
      tinymce.get(this.id).remove();
      // tinymce.get(this.id).set("leo");

      //have to clone the object
      let newConfig = {...WysiwygHelper.defaultConfig};
      newConfig.id = this.id;
      //newConfig["api-key"] = "5vtccjleslypobjwkogi5jhpgxkhw3pyvnaogvcdv4w697zu";
      newConfig.selector = '#' + this.id;
      newConfig.setup = function (editor) {
        // when typing keyup event
        editor.on('keyup', function () {
          app.changeContent(editor.getContent());
        });
        editor.on('change', function () {
          app.changeContent(editor.getContent());
        });
      }
      tinymce.init(newConfig);

    }
  },
  methods: {
    init() {
      this.editorId = this.id;
      if(this.wysiwygBlock.attributes.classes === undefined) {
        this.wysiwygBlock.attributes.classes = [];
      }
      //validate classes
      this.wysiwygBlock.attributes.classes = this.wysiwygBlock.attributes.classes.filter(cls => {
        for (let key in this.classesWidget) {
          if (this.classesWidget[key].class === cls) return true;
        }
        return false;
      });

    },
    onInit(payload) {
    },
    onBlockChange(payload) {
      this.$emit("change");
    },
    changeContent(content) {
      this.wysiwygBlock.attributes.content = content;
      this.onBlockChange();
    },
    setToolboxEl(el) {
      if (el) {
        this.toolboxEl = el;
      }
    },
    clickOutside(e) {
      var el = this.toolboxEl;
      if (!(el === e.target || el.contains(e.target))) {
        this.toolboxShow = false;
      }
    },
  }
}
</script>

<style scoped>
.image-wysiswg {
  display: flex;
  border: 1px solid #e6e6e666;
}

.image-wysiswg > div {
  width: 50%;
}

.wysiwyg {
  width: 100%;
}
</style>
