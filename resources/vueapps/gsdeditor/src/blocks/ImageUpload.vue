<template>
    <input type="file" class="image-file-uploader" name="" id="" ref="inputFile" @change="handleFileChange($event)"/>
</template>

<script>

//based on https://github.com/ogurinkaben/OR-file-upload-component

import IconHelper from "@gsdvuefw/helpers/IconHelper";

export default {
  name: "ImageUpload",
  props: {
    imageBlock: {},
    maxSize: {
      type: Number,
      default: 20, //max filesize in MB
    },
    accept: {
      type: String,
      default: "image/*",
    },
  },
  data() {
    return {
      IconHelper,
      errors: [],
      uploadReady: true,
      toolboxEl: null,
      file: {
        name: "",
        size: 0,
        type: "",
        fileExtention: "",
      },
    };
  },
  created() {
    // this.id = Math.floor(Math.random() * (1000000 - 1 + 1)) + 1;
    // console.log("ROB "+this.id);
  },
  methods: {

    handleFileChange(e) {
      this.errors = [];
      // Check if file is selected
      if (e.target.files && e.target.files[0]) {
        // Check if file is valid
        if (this.isFileValid(e.target.files[0])) {
          // Get uploaded file
          const file = e.target.files[0],
              // Get file size in MB
              fileSize = Math.round((file.size / 1024 / 1024) * 100) / 100,
              // Get file extention
              fileExtention = file.name.split(".").pop(),
              // Get file name
              fileName = file.name,
              // Check if file is an image
              isImage = ["jpg", "jpeg", "png", "gif", "svg"].includes(fileExtention);
          // Print to console

          //console.log(fileSize, fileExtention, fileName, isImage);

          // Load the FileReader API
          let reader = new FileReader();
          reader.addEventListener(
              "load",
              () => {
                // Set file data
                this.file = {
                  name: fileName,
                  size: fileSize,
                  type: file.type,
                  fileExtention: fileExtention,
                };
                if(isImage) {
                  this.$emit("uploadSuccess", {
                    'url': reader.result,
                    'filename': fileName
                  })
                  //e.target.value = ""; //option to clear
                }
              },
              false
          );
          // Read uploaded file
          reader.readAsDataURL(file);

        }
        else {
          this.$emit("uploadError", this.errors);
        }
      }
    },
    isFileSizeValid(fileSize) {
      if (fileSize > this.maxSize) {
        this.errors.push(`File size should be less than ${this.maxSize} MB`);
      }
    },
    isFileTypeValid(fileExtention) {
      if (!this.accept.split(",").includes(fileExtention)) {
        this.errors.push(`File type should be ${this.accept}`);
      }
    },
    isFileValid(file) {
      this.isFileSizeValid(Math.round((file.size / 1024 / 1024) * 100) / 100);
      this.isFileTypeValid(file.name.split(".").pop());
      return this.errors.length === 0;
    },
  },
};
</script>

<style scoped>
  .image-file-uploader {
    padding: 0;
  }
</style>