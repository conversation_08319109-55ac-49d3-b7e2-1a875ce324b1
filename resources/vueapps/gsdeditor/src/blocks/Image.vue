<template>
  <div class="file-upload">
    <div class="block-topbar">
      Afbeelding
      <image-upload :accept="accept" @uploadError="uploadError" @uploadSuccess="uploadSuccess"></image-upload>
      <div class="toolbox">
        <a href="" v-html="IconHelper.ICON_REMOVE" @click.prevent="clearImage" v-if="showImage()" title="Verwijder afbeelding" class="gsd-svg-icon-a"></a>
        <a v-html="IconHelper.ICON_EDIT" href="" @click.prevent="toolboxShow = !toolboxShow" title="Bewerk eigenschappen" class="gsd-svg-icon-a"></a>
        <div class="toolbox-wrapper" v-show="toolboxShow" :ref="setToolboxEl">
          <div class="toolbox-head">
            Widget eigenschappen
            <a href="" v-html="IconHelper.ICON_CROSS" @click.prevent="toolboxShow = !toolboxShow"/>
          </div>
          <div class="toolbox-content">
            <div class="toolbox-content-row">
              <div>URL</div>
              <input type="text" v-model="imageBlock.attributes.url" placeholder="URL..." v-if="showImageUrl()"/>
              <span v-if="!showImageUrl()">Afbeelding geupload</span>
            </div>
            <div class="toolbox-content-row">
              <div>Alt</div>
              <input type="text" v-model="imageBlock.attributes.alt" placeholder="Alt..."/>
            </div>
            <div class="toolbox-content-row">
              <div>Titel</div>
              <input type="text" v-model="imageBlock.attributes.title" placeholder="Titel..."/>
            </div>
            <div class="toolbox-content-row">
              <div>Omschrijving</div>
              <textarea v-model="imageBlock.attributes.description" placeholder="Omschrijving..."></textarea>
            </div>
            <div class="toolbox-content-row" v-if="classesWidget.length > 0">
              <div>Classes</div>
              <div>
                <div v-for="item in classesWidget" :key="item.class">
                  <label :title="item.class">
                    <input type="checkbox" :value="item.class" v-model="imageBlock.attributes.classes"/>
                    {{ item.description }}
                  </label>
                </div>
              </div>
            </div>
            <div class="toolbox-content-row">
              <div>Class</div>
              <input type="text" v-model="imageBlock.attributes.class" placeholder="Css class..."/>
            </div>
            <div class="toolbox-content-row">
              <div>Achtergrond</div>
              <label>
                <input type="checkbox" v-model="imageBlock.attributes.backgroundimage"
                       true-value="1"
                       false-value="0"
                />
                Afbeelding als achtergrond
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="errors.length > 0">
      <div class="file-upload__error"
           v-for="(error, index) in errors"
           :key="index">
        <span>{{ error }}</span>
      </div>
    </div>
    <div class="upload-preview">
      <img v-if="showImage()" :src="imageBlock.attributes.url" class="file-image" alt=""/>
    </div>
  </div>
</template>

<script>

//based on https://github.com/ogurinkaben/OR-file-upload-component

import IconHelper from "@gsdvuefw/helpers/IconHelper";
import ImageUpload from "./ImageUpload.vue";
import projectClasses from '@/composables/projectClasses';

export default {
  name: "Image",
  components: {ImageUpload},
  props: {
    imageBlock: {},
    maxSize: {
      type: Number,
      default: 20, //max filesize in MB
    },
    accept: {
      type: String,
      default: "image/*",
    },
  },
  data() {
    return {
      IconHelper,
      toolboxShow: false,
      imageShow: false,
      errors: [],
      isLoading: false,
      uploadReady: true,
      toolboxEl: null,
      classesWidget: projectClasses().widget,
      file: {
        name: "",
        size: 0,
        type: "",
        fileExtention: "",
      },
    };
  },
  created() {
    //console.log(this.imageBlock);
    if(this.imageBlock.attributes.classes === undefined) {
      this.imageBlock.attributes.classes = [];
    }
    //validate classes
    this.imageBlock.attributes.classes = this.imageBlock.attributes.classes.filter(cls => {
      for (let key in this.classesWidget) {
        if (this.classesWidget[key].class === cls) return true;
      }
      return false;
    });
    document.addEventListener('mouseup', this.clickOutside);
  },
  methods: {
    showImage() {
      return this.imageBlock.attributes.url!=''
    },
    showImageUrl() {
      return this.imageBlock.attributes.url.substring(0,5)!="data:"
    },

    uploadError(errors) {
      this.errors = errors;
    },

    uploadSuccess(data) {
      this.imageBlock.attributes.url = data.url;
      this.imageBlock.attributes.filename = data.filename;
    },

    clearImage() {
      this.uploadReady = false;
      this.$nextTick(() => {
        this.uploadReady = true;
        this.file = {
          name: "",
          size: 0,
          type: "",
          data: "",
          fileExtention: "",
        };
      });
      this.imageBlock.attributes.url = "";
      this.imageBlock.attributes.filename = "";
      this.imageBlock.attributes.backgroundimage = 1;
      //@todo filename leegmaken. Werkt niet meer omdat het compponent is geworden.
      // this.$refs.inputFile.value = null;
    },
    onChange() {
      // this.$emit("change");
    },
    setToolboxEl(el) {
      if (el) {
        this.toolboxEl = el;
      }
    },
    clickOutside(e) {
      var el = this.toolboxEl;
      if (!(el === e.target || el.contains(e.target))) {
        this.toolboxShow = false;
      }
    },


  },
};
</script>

<style scoped>

.file-upload {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-right: 1px solid #f5f8fd;
}

.file-upload .file-upload__area {
  width: 600px;
  height: 100%;
  display: flex;
  padding: 3px;
}

.file-upload .file-upload__error {
  margin-top: 10px;
  color: white;
  background-color: #F44336;
  padding: 10px 15px;
}

.file-upload .upload-preview {
  text-align: center;
}
.file-upload .upload-preview {
  height: 396px;
}
.file-upload .upload-preview .file-image {
  width: 100%;
  height: 396px;
  object-fit: contain;
}

.file-upload .upload-preview .file-extention {
  height: 100px;
  width: 100px;
  border-radius: 8px;
  background: #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0.5em auto;
  font-size: 1.2em;
  padding: 1em;
  text-transform: uppercase;
  font-weight: 500;
}

.file-upload .upload-preview .file-name {
  font-size: 1.2em;
  font-weight: 500;
  color: #000;
  opacity: 0.5;
}
</style>