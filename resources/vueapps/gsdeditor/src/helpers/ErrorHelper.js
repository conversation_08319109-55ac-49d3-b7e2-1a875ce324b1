export default class ErrorHelper {

  static handleError(app, response) {
    if ("error" in response) {
      if ("redirect" in response.error) {
        location.href = response.error.redirect;
      }
      app.$swal("Foutmelding", response.error.message, "error").then(function () {
        if ("redirect" in response.error) {
          location.href = response.error.redirect;
        }
      });
      return true;
    }
    return false;
  }

}