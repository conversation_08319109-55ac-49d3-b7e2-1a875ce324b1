//import the default content style
import contentUiCss from "tinymce/skins/ui/oxide/content.css?inline";

export default class WysiwygHelper {

  static defaultPlugins = ['lists', 'link', 'image', 'table', 'code', 'wordcount'
    // 'help',
  ];
  static defaultToolbar = 'undo redo | bold italic underline |  blocks fontsize | image media link table | alignleft aligncenter alignright alignjustify | outdent indent | ' +
    ' numlist bullist | forecolor backcolor removeformat | ' +
    'print code ';
  //styles: for style select element based

  static imageUploadHandler = (blobInfo, progress) => new Promise((resolve, reject) => {

    var queryString = location.search;
    let params = new URLSearchParams(queryString);
    let pageId = parseInt(params.get("id"));

    let langEl = document.getElementById("current-locale");
    if(langEl==null || langEl.innerHTML=="") {
      alert("Werktaal niet kunnen vaststellen.");
      return;
    }
    let lang = langEl.innerHTML;

    const xhr = new XMLHttpRequest();
    xhr.withCredentials = false;
    xhr.open('POST', '?action=tinymceimagepost&id=' + pageId + "&currentlocale="+lang);

    xhr.upload.onprogress = (e) => {
      progress(e.loaded / e.total * 100);
    };

    xhr.onload = () => {
      if (xhr.status === 403) {
        reject({message: 'HTTP Error: ' + xhr.status, remove: true});
        return;
      }

      if (xhr.status < 200 || xhr.status >= 300) {
        reject('HTTP Error: ' + xhr.status);
        return;
      }

      const json = JSON.parse(xhr.responseText);

      if (!json || typeof json.location != 'string') {
        reject('Invalid JSON: ' + xhr.responseText);
        return;
      }

      resolve(json.location);
    };

    xhr.onerror = () => {
      reject('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
    };

    const formData = new FormData();
    formData.append('file', blobInfo.blob(), blobInfo.filename());

    xhr.send(formData);
  });

  static fileUploadHandler = (callback, value, meta) => {
    var input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.onchange = function () {
      var file = this.files[0];
      var reader = new FileReader();

      // FormData
      var fd = new FormData();
      var files = file;
      fd.append('filetype', meta.filetype);
      fd.append("file", files);

      var filename = "";

      var queryString = location.search;
      let params = new URLSearchParams(queryString);
      let pageId = parseInt(params.get("id"));

      let langEl = document.getElementById("current-locale");
      if(langEl==null || langEl.innerHTML=="") {
        alert("Werktaal niet kunnen vaststellen.");
        return;
      }
      let lang = langEl.innerHTML;


      // AJAX
      var xhr, formData;
      xhr = new XMLHttpRequest();
      xhr.withCredentials = false;
      xhr.open('POST', '?action=tinymcefilepost&id=' + pageId + "&currentlocale=" + lang);


      xhr.onload = function () {
        var json;
        if (xhr.status != 200) {
          alert('HTTP Error: ' + xhr.status);
          return;
        }
        json = JSON.parse(xhr.responseText);
        if (!json || typeof json.location != 'string') {
          alert('Invalid JSON: ' + xhr.responseText);
          return;
        }
        filename = json.location;
        reader.onload = function (e) {
          callback(filename);
        };
        reader.readAsDataURL(file);
      };
      xhr.send(fd);
      return
    };

    input.click();
  };

  //must be defined last
  static defaultConfig = {
    license_key: 'gpl',
    // skin: 'oxide',
    plugins: WysiwygHelper.defaultPlugins,
    menubar: false,
    paste_as_text: true,
    toolbar: WysiwygHelper.defaultToolbar,
    language: 'nl',
    file_picker_callback: WysiwygHelper.fileUploadHandler,
    images_upload_handler: WysiwygHelper.imageUploadHandler,
    image_advtab: true,
    skin: false, //added to prevent loading of default skin
    content_css: WysiwygHelper.getCss(),
    content_style: contentUiCss.toString() + '\n' + WysiwygHelper.getStyle(),
    relative_urls: false, //use relative image url's
    // style_formats: [
    //   {title: 'Image Left', selector: 'img', styles: {
    //       'float' : 'left',
    //       'margin': '0 10px 0 10px'
    //     }},
    //   {title: 'Image Right', selector: 'img', styles: {
    //       'float' : 'right',
    //       'margin': '0 10px 0 10px'
    //     }}
    // ]
  };

  static getCss() {
    if (window.gsdEditorConfig.tinymceStylesheets != undefined) {
      //this should be url's to css files
      return window.gsdEditorConfig.tinymceStylesheets.join(",");
    }
    return false;
  }

  static getStyle() {
    if (window.gsdEditorConfig.tinymceContentStyle != undefined) {
      //this should be strings with css content, not url's
      return window.gsdEditorConfig.tinymceContentStyle;
    }
    return "";
  }

}