<template>
  <div class="container-toolbox">
    <div class="toolbox">
      <a v-html="IconHelper.ICON_EDIT" class="gsd-svg-icon-a" href="" @click.prevent="toggleToolbox" title="Aanpassen container eigenschappen"></a>
      <div class="toolbox-wrapper" v-show="toolboxShow" :ref="setToolboxEl">
        <div class="toolbox-head">
          Container eigenschappen
          <a href="" v-html="IconHelper.ICON_CROSS" @click.prevent="toolboxShow = !toolboxShow"/>
        </div>
        <div class="toolbox-content">
          <div class="toolbox-content-row" v-if="classesContainer.length > 0">
            <div>Classes</div>
            <div>
              <div v-for="item in classesContainer" :key="item.class">
                <label :title="item.class">
                  <input type="checkbox" :value="item.class" v-model="container.attributes.classes"/>
                  {{ item.description }}
                </label>
              </div>
            </div>
          </div>
          <div class="toolbox-content-row">
            <div>Class</div>
            <input type="text" v-model="container.attributes.class" placeholder="Container css class..."/>
          </div>
          <div class="toolbox-content-row">
            <div>Achtergrond afbeelding</div>
            <image-upload :accept="accept" @uploadError="uploadError" @uploadSuccess="uploadSuccess"></image-upload>
          </div>
          <div class="" v-if="container.attributes.backgroundImageUrl != ''">
            <img :src="container.attributes.backgroundImageUrl" class="background-image-preview" alt=""/>
            <a v-html="IconHelper.ICON_REMOVE" class="gsd-svg-icon-a" href="" @click.prevent="removeBackgroundImage" title="Verwijder afbeelding"></a>
            <a @click.prevent="removeBackgroundImage" style="padding-left: 5px;">Verwijder afbeelding</a>
          </div>
        </div>
      </div>
    </div>
    <a v-html="IconHelper.ICON_MOVE" class="gsd-svg-icon-a handle" href="" @click.prevent="" title="Verplaats container"></a>
    <a v-html="IconHelper.ICON_REMOVE" class="gsd-svg-icon-a" href="" @click.prevent="removeContainer" title="Verwijderen container"></a>
    <a v-html="IconHelper.ICON_SAVE" class="gsd-svg-icon-a" href="" @click.prevent="this.$emit('save')" title="Blokken opslaan"></a>
  </div>
</template>

<script>

import IconHelper from "@gsdvuefw/helpers/IconHelper";
import Image from "../blocks/Image.vue";
import ImageUpload from "../blocks/ImageUpload.vue";
import projectClasses from '@/composables/projectClasses';

export default {
  name: 'ContainerToolbox',
  props: {
    container: {}
  },
  components: {
    ImageUpload,
    Image
  },
  data() {
    return {
      IconHelper,
      toolboxEl: null,
      toolboxShow: false,
      upload_errors: [],
      accept: "png,jpg,jpeg,gif",
      classesContainer: projectClasses().container,
    }
  },
  created() {
    this.init();
    document.addEventListener('mouseup', this.clickOutside);
  },
  watch: {
    container() {
      //wijziging van content, change containers
      // console.log("container()");
      // console.log(this.container);
      this.init();
    },
  },
  methods: {
    init() {
      if(this.container.attributes === undefined) {
        this.container.attributes = {
          class: "",
          classes: [],
          backgroundImageUrl: "",
          backgroundImageFilename: ""
        };
      }
      else if(this.container.attributes.classes === undefined) {
        this.container.attributes.classes = [];
      }
      else if(this.container.attributes.backgroundImageFilename === undefined) {
        this.container.attributes.backgroundImageUrl = '';
        this.container.attributes.backgroundImageFilename = '';
      }
      this.container.attributes.classes = this.container.attributes.classes.filter(cls => {
        for (let key in this.classesContainer) {
          if (this.classesContainer[key].class === cls) return true;
        }
        return false;
      });
    },
    removeContainer() {
      this.$swal({
        title: 'Verwijder container',
        text: "Wilt u deze container verwijderen?",
        icon: 'warning',
        showCancelButton: true,
      }).then((result) => {
        if (result.isConfirmed) {
          this.$emit("removeContainer");
        }
      })
    },

    setToolboxEl(el) {
      if (el) {
        this.toolboxEl = el;
      }
    },

    clickOutside(e) {
      var el = this.toolboxEl;
      if (!(el === e.target || el.contains(e.target))) {
        this.toolboxShow = false;
      }
    },

    removeBackgroundImage() {
      this.container.attributes.backgroundImageUrl = '';
      this.container.attributes.backgroundImageFilename = '';
    },

    uploadError(errors) {
      this.upload_errors = errors;
    },

    uploadSuccess(data) {
      this.container.attributes.backgroundImageUrl = data.url;
      this.container.attributes.backgroundImageFilename = data.filename;
    },
    toggleToolbox() {
      this.toolboxShow = !this.toolboxShow;
    }
  }
}
</script>

<style scoped>
.background-image-preview {
  max-width: 200px;
  max-height: 200px;
}
</style>