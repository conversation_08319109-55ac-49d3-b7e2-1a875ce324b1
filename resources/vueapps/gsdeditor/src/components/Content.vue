<template>
  <div>
    <draggable
        :list="containers"
        :move="checkMove"
        item-key="id"
        @start="dragging = true"
        @end="onEnd"
        handle=".handle"
        class="list-group"
        ghost-class="ghost"
    >
      <template #item="{element, index}">
        <div class="list-group-item">
          <component :is="element.type"
                     :container="element"
                     :order="element.order"
                     :id="element.id"
                     :lang="this.content.lang"
                     :editable="superadmin"
                     @change="onComponentChange"
                     @changeContent="changeContent"
                     @removeContainer="removeContainer(index)"
                     @save="this.$emit('save')"/>
        </div>
      </template>

    </draggable>

    <div class="addContainer">
      <select v-model="containerToAdd">
        <option value="Wysiwyg">1 kolom: Vrije editor</option>
        <option value="Image">1 kolom: 1 Afbeelding</option>
        <option value="ImageWysiwyg">2 koloms: Afbeelding - Vrije editor</option>
        <option value="WysiwygImage">2 koloms: Vrije editor - Afbeelding</option>
        <option value="WysiwygWysiwyg">2 koloms: Vrije editor - Vrije editor</option>
        <option value="WysiwygWysiwygWysiwyg">3 koloms: Vrije editor - Vrije editor - Vrije editor</option>
        <option value="ImageImage">2 koloms: 2 Afbeeldingen</option>
        <option value="ImageImageImage">3 koloms: 3 Afbeeldingen</option>
        <option value="Partial" v-if="superadmin">Partial toevoegen (superadmin only)</option>
      </select>
      <button @click="addSelectedContainer" type="button" class="gsd-btn gsd-btn-primary">+ Container toevoegen</button>
    </div>

  </div>
</template>

<script>
import {h} from 'vue'
import draggable from "vuedraggable";
import ModuleNotFound from './ModuleNotFound.vue';
import ImageWysiwyg from '../containers/ImageWysiwyg.vue';
import WysiwygImage from '../containers/WysiwygImage.vue';
import Image from '../containers/Image.vue';
import ImageImage from '../containers/ImageImage.vue';
import ImageImageImage from '../containers/ImageImageImage.vue';
import Wysiwyg from '../containers/Wysiwyg.vue';
import WysiwygWysiwyg from '../containers/WysiwygWysiwyg.vue';
import WysiwygWysiwygWysiwyg from '../containers/WysiwygWysiwygWysiwyg.vue';
import Partial from '../containers/Partial.vue';

import DefaultJson from '../data/DefaultJson.json'

const pageId = document.getElementById('app').dataset.pageid;

let id = 1;
export default {
  name: "Content",
  props: {
    content: {},
    superadmin: false,
  },
  order: 0,
  components: {
    draggable,
    ModuleNotFound,
    //blocks
    ImageWysiwyg,
    WysiwygImage,
    Partial,
    Image,
    ImageImage,
    ImageImageImage,
    Wysiwyg,
    WysiwygWysiwyg,
    WysiwygWysiwygWysiwyg,
  },
  data() {
    return {
      enabled: true,
      containers: [],
      dragging: false,
      containerToAdd: "Wysiwyg",
    };
  },
  created() {
    // console.log("content created()");
    // this.containers = this.content.containers;
  },
  watch: {
    content() { //wijziging van content, change containers
      if (this.content === undefined) {
        return;
      }
      // console.log("content()");
      // console.log(this.content.lang);
      this.containers = this.content.containers;
      // console.log(this.containers);
    },
  },
  methods: {
    checkMove: function (e) {
      // window.console.log("Future index: " + e.draggedContext.futureIndex);
    },
    onComponentChange (payload) {
      // console.log("onComponentChange:");
      // console.log(payload);
      // this.$emit("change", this.content);
    },
    changeContent () {
      // console.log("changeContent:");
      this.$emit("change", this.content);
    },
    removeContainer(index) {
      this.containers.splice(index, 1);
      this.setOrder();
    },
    addSelectedContainer () {
      //add default json structure of this container type
      let json = JSON.parse(JSON.stringify(DefaultJson[this.containerToAdd]))
      // let json = { ...DefaultJson[this.containerToAdd]};
      let highestId = 0;
      for (var key in this.containers) {
        if(this.containers[key].id>highestId) {
          highestId = this.containers[key].id;
        }
      }
      json.id = highestId + 1;
      this.containers.push(json);
      this.setOrder();
      //console.log("Container added");
    },
    onEnd(e) {
      this.setOrder();
    },
    setOrder() {
      //set order on order prop
      let order = 0;
      for (var key in this.containers) {
        this.containers[key].order = order;
        order++;
      }
      this.dragging = false;
    }

  }
};
</script>
