<template>
  <div class="gsdeditor" v-cloak>
    <div v-if="loading" id="loader">
      <img :src="public + '/images/loader.gif'">
    </div>

    <modal :open="showLanguageCopy">
      <template v-slot:modal-title>
        TAAL KOPIËREN
      </template>
      <template v-slot:modal-content>
        <div v-if="modal_message!=''" class="alert alert-danger">
          {{modal_message}}
        </div>
        <span id="copy_txt">Van</span>
        <select v-model="source_language">
          <option v-for="(localeName, locale) in locales" :key="locale" :value="locale">{{ localeName }}</option>
        </select>
        <span id="copy_txt">naar</span>
        <select v-model="target_language">
          <option v-for="(localeName, locale) in locales" :key="locale" :value="locale">{{ localeName }}</option>
        </select>
        <div class="buttons">
          <button @click.prevent="copyLanguage" class="gsd-btn gsd-btn-primary">Ko<PERSON>ëren</button>
          <button @click.prevent="showLanguageCopy = false" class="gsd-btn gsd-btn-link">Annuleren</button>
        </div>
        <br/>
        U kunt de blokken van de brontaal naar de doeltaal kopiëren.<br/>
        Let op: de gegevens in de doeltaal worden overgeschreven.
      </template>
    </modal>

    <div>
      <div id="gsdeditor-header">
        GSD Blokken editor
        <div>
          <div id="current-locale-wrap" v-if="Object.keys(locales).length > 1">
            <a v-for="(localeName, lLocale) in locales" class="gsd-btn toggle-locale gsd-btn-small" :key="lLocale" v-if="isActive" @click.prevent="toggleLocale(lLocale)" :id="'gsdeditor-locale-'+lLocale" href="" :class="{'gsd-btn-primary': lLocale===curentLocale}">
              {{localeName}}
            </a>
            <img :src="'/gsdfw/images/flags/'+curentLocale+'.png'"/>
          </div>
          <span id="current-locale">{{curentLocale}}</span>
          <a  v-if="Object.keys(locales).length > 1" v-html="IconHelper.ICON_COPY" class="gsd-svg-icon-a copy-lang" href="" @click.prevent="openLanguageCopy()" title="Kopieër taal"></a>
          <button @click.prevent="save(false)" class="gsd-btn gsd-btn-small gsd-editor-save" v-if="isActive">Blokken opslaan</button>
          <button @click.prevent="save(true)" id="saveGsdeditorHidden" style="display:none;">GsdEditor Opslaan verborgen</button>
        </div>
      </div>
        <Content
          :content="content"
          v-if="isActive"
          :superadmin="superadmin"
          @change="onContentChange"
          @save="save(false)"
        ></Content>
      <div class="newpage" v-if="showNewPageMessage">
        U dient de pagina eerst op te slaan voordat u de blokken editor kunt gebruiken.
      </div>
    </div>
    <div id="message-float" :class="{'message-float-hide':messageHideClass}" v-show="messageVisible">
      {{message}}
    </div>
  </div>
</template>

<script>

import Content from './Content.vue';
import ErrorHelper from "../helpers/ErrorHelper";
import IconHelper from "@gsdvuefw/helpers/IconHelper";
import Modal from "@gsdvuefw/components/general/Modal.vue";

const config = window.gsdEditorConfig; //get config from windows variable

let id = 1;
export default {
  name: "GsdEditor",
  components: {
    Modal,
    Content,
  },
  data() {
    return {
      IconHelper,
      loading: true,
      enabled: true,
      isActive: true,
      showNewPageMessage: false,
      content: Content, //load with dummy
      contents: [],
      locales: config.locales,
      curentLocale: config.localeDefault,
      superadmin: config.superadmin,
      messageVisible: false,
      messageHideClass: false,
      message: '',
      public: public_dir, //mag niet hetzelfde heten, anders pikt iet t niet in productie build...
      showLanguageCopy: false,
      source_language: "nl",
      target_language: "nl",
      modal_message: "",
    };
  },
  created() {
    if(config.pageId==null) {
      this.loading = false;
      this.isActive = false;
      this.showNewPageMessage = true;
      return;
    }
    this.fetchData();

    //even de 2e taal selecteren als doeltaal standaard. handigheidje
    let tel = 1;
    for(let locale in this.locales) {
      if(tel==2) {
        this.target_language = locale;
        break;
      }
      tel++;
    }

  },
  computed: {
  },
  methods: {
    fetchData() {
      let url = '';
      //let url = 'http://beheer.gsd.nl.gsd.localhost/nl/pagina-bewerk';
      fetch(url+"?action=gsdEditorGet&id="+config.pageId, {
        headers: {'Content-type': 'application/json'},
      }).then(res => res.json()).then((response) => {

        // if(ErrorHelper.handleError(this, response)) return;
        //console.log(response);
        this.contents = response.contents;
        this.loading = false;

        this.content = this.contents[config.localeDefault];


      }).catch((error) => {
        this.errors = [];
        this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
      });
    },
    save(goPost) {
      if(config.pageId==null) {
        document.getElementById(window.preventPagePostSource).click();
        return;
      }
      this.loading = true;
      fetch('?action=gsdEditorSet&id='+config.pageId, {
        method: "POST",
        headers: {
          // 'Content-type': 'application/json',
          "Accept": "application/json",   // expected data sent back
        },
        body: JSON.stringify(this.contents)
      }).then(res => res.json()).then((response) => {

        this.loading = false;

        if (ErrorHelper.handleError(this, response)) return;

        if(goPost) {
          window.preventPagePost = false;
          document.getElementById(window.preventPagePostSource).click();
        }
        else {
          this.message = "✓ GSD Blokkeneditor content opgeslagen";
          this.messageVisible = true;
          this.messageHideClass = false;
          setTimeout(() => {
            this.messageHideClass = true;
            setTimeout(() => {
              this.messageVisible = false;
              this.messageHideClass = false;
            }, 500);
          }, 1000);
        }

      }).catch((error) => {
        // this.errors = [];
        // this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
      });
    },
    onContentChange(content) {
      // console.log("onContentChange in GsdEditor:");

      //console.log(this.contents["nl"].containers[0].blocks[0].attributes.alt);
      // console.log(this.contents["nl"].containers[0].blocks[1].attributes.content);
      // if(content.containers!=undefined) {
      //   console.log(content.containers[0].blocks[0].attributes.alt);
        //console.log(content.containers[0].blocks[1].attributes.content);
      // }
    },
    toggleLocale(locale) {

      //select als right items in page html template
      document.getElementById(locale).click();

      this.curentLocale = locale;
      this.content = this.contents[locale];
    },
    openLanguageCopy() {
      this.showLanguageCopy = true;
    },
    copyLanguage() {
      //copy source to target language
      var app = this; //trukje. misschien kan dit anders.
      this.modal_message = "";
      if(this.source_language==this.target_language) {
        this.modal_message = "Bron en doeltaal kunnen niet hetzelfde zijn."
      }
      this.showLanguageCopy = false;
      this.$swal({
        title: 'Kopieren taal',
        text: "Weet u zeker dat u de bron taal wilt kopiëren naar de doeltaal? De doeltaal word overschreven.",
        icon: 'warning',
        showCancelButton: true,
      }).then((result) => {
        if (result.isConfirmed) {
          this.loading = true;

          fetch('?action=gsdEditorCopy&id='+config.pageId+"&source="+this.source_language+"&target="+this.target_language, {
            method: "POST",
            headers: {
              // 'Content-type': 'application/json',
              "Accept": "application/json",   // expected data sent back
            },
            body: JSON.stringify(this.contents)
          }).then(res => res.json()).then((response) => {
            //will retrun the updated content in all languages

            // if(ErrorHelper.handleError(this, response)) return;

            this.contents = response.contents;
            this.loading = false;
            this.content = this.contents[app.curentLocale];

            app.$swal({
              title: 'Taal gekopieerd',
              text: "Doel taal is overschreven met brontaal",
              icon: 'success',
            });

          }).catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });

        }
      })
    }

  }
};
</script>
<style>
.gsdeditor {
  font-size: 12px;
}
.gsdeditor input {
  font-size: 12px;
}
#gsdeditor-header {
  padding: 10px 15px;
  background-color: #B9B5C5;
  color: white;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.block-topbar {
  padding: 8px 15px;
  background: #F5F8FD;
  display: block;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.block-topbar .gsd-svg-icon-a {
}

.block-topbar .gsd-svg-icon {
  height: 20px;
  color: gray;
}
.toolbox {
  position: relative;
}
.toolbox-wrapper {
  border: 1px solid #e7e7e9;
  position: absolute;
  z-index: 1000000;
  background-color: white;
  border-radius: 5px;
  right: 0;
  margin-top: 5px;
  box-shadow: 0 1px 2px hsla(0, 0%, 0%, 0.2);
}
.toolbox-head {
  background-color: #F5F8FD;
  padding: 5px 15px;
  border-radius: 5px 5px 0 0;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
}
.toolbox-content {
  padding: 10px 15px 0 15px;
  border-radius:  0 0 5px 5px;
}
.toolbox-content-row {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
}
.toolbox-content-row > div:first-child {
  width: 90px;
  font-weight: bold;
}

.container-toolbox {
  width: 41px !important;
  display: flex;
  flex-direction: column;
  padding: 5px;
}
.container-toolbox a {
  margin-bottom: 5px;
}

.addContainer {
  display: flex;
  padding: 15px;
}
.addContainer > select {
  margin-right: 15px;
}

.buttons {
  margin-top: 35px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.not-draggable {
  cursor: no-drop;
}
.newpage {
  padding: 15px;
  color: #e91e63;
  background-color: #f0f0f0;
}
.toggle-locale {
  margin-right: 15px;
}
#loader {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
  box-shadow: 0 1px 2px hsla(0, 0%, 0%, 0.2);
  z-index: 1000000000;
  transition: opacity 0.5s;
  opacity: 1;
  display: flex;
  justify-content: center;
}
#loader img {
  max-height: 200px;
  border: 1px solid #b3a4a4;
  box-shadow: 0 1px 5px #ddd;
}
.swal2-popup {
  /* overrule default 1rem font-size, which is too small **/
  font-size: inherit;
}
.gsdeditor input[type=file]::file-selector-button {
  padding: 6px;
  font-size: 12px;
}
#message-float {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid #666;
  background-color: #b9faac;
  color: white;
  font-size: 15px;
  position: fixed;
  padding: 15px 30px;
  box-shadow: 0 1px 2px hsla(0, 0%, 0%, 0.2);
  background: hsl(200, 25%, 55%);
  border-radius: 5px;
  z-index: 1000000000;
  transition: opacity 0.5s;
  opacity: 1;
}
#message-float.message-float-hide {
  opacity: 0;
}
#current-locale {
  display: none;
}
#current-locale-wrap {
  align-items: center;
  font-weight: bold;
  margin-right: 15px;
  display: inline-flex;
  text-transform: uppercase;
}
#current-locale-wrap img {
  height: 15px;
  border: 1px solid #656565;
}
.gsd-btn.active-lang {
  background-color: var(--gsd-primary-color);
}
.copy-lang {
  margin-right: 15px;
}
.gsd-btn {
  background-color: #f5f8fd;
}

.modal-content h2 {
  margin-bottom: 20px;
}
.modal-content .checkbox-options {
  display: flex;
  flex-direction: column;
  row-gap: 8px;
  margin-bottom: 20px;
}
.modal-content .buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
}
#copy_txt {
  padding: 0 15px;
}
.gsdeditor .gsd-svg-icon-a {
  vertical-align: middle;
  background-color: #F5F8FD;
  padding: 5px;
  border-radius: 0.475rem;
}
</style>