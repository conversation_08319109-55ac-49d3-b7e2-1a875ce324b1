<template>
  <div class="wysiswg-image col2">
    <div class="editor" v-for="(wysiwygBlock, index) in blocks" :key="index" >
      <WysiwygBlock
          :id="this.editorId+'_'+index"
          :key="this.editorId+'_'+index"
          :wysiwygBlock="wysiwygBlock"
          :order="order"
          @change="onBlockChange"
      />
    </div>

    <ContainerToolbox
      :container="container"
      @removeContainer="this.$emit('removeContainer')"
      @save="this.$emit('save')"
    />

  </div>
</template>

<script>

import IconHelper from "@gsdvuefw/helpers/IconHelper";
import WysiwygBlock from "../blocks/Wysiwyg.vue";
import ContainerToolbox from "@/components/ContainerToolbox.vue"; //other name to prevent conflicts

export default {
  name: 'WysiwygWysiwygWysiwyg',
  props: {
    id: Number,
    order: {
      type: Number,
      default: 0,
    },
    container: {},
    lang: "",
  },
  components: {
    ContainerToolbox,
    WysiwygBlock,
  },
  data() {
    return {
      blocks: [],
      wysiwygBlocks: {},
      IconHelper,
      editorId: "",
    }
  },
  created() {
    this.init();
  },
  watch: {
    container() {
      //container, wijzigen input
      this.init();
    },
  },
  methods: {
    init() {
      this.blocks = this.container.blocks;
      this.editorId = "editor_" + this.id + "_" + this.lang + "_" + (new Date()).getTime();
    },
    onBlockChange() {
      this.$emit("changeContent", this.blocks);
    },
    changeContent(content) {
      this.wysiwygBlock.attributes.content = content;
      this.onBlockChange();
    },

  }
}
</script>

<style scoped>
.wysiswg-image {
  display: flex;
  border: 1px solid #e6e6e666;
}

.wysiswg-image > div {
  width: 50%;
}

.wysiwyg {
  width: 100%;
}
</style>
