<template>
  <div class="wysiswg-image col2">
    <div class="editor">
      <Wysiwyg
          :id="editorId"
          :key="editorId"
          :wysiwygBlock="wysiwygBlock"
          :order="order"
          @change="onBlockChange"
      />
    </div>
    <div class="image">
      <div>
        <Image
            :key="'image_'+editorId"
            accept="png,jpg,jpeg,gif"
            :imageBlock="imageBlock"
            @change="onBlockChange"
        />
      </div>
    </div>
    <ContainerToolbox
      :container="container"
      @removeContainer="this.$emit('removeContainer')"
      @save="this.$emit('save')"
    />
  </div>
</template>

<script>

import IconHelper from "@gsdvuefw/helpers/IconHelper";
import Image from "@/blocks/Image.vue";
import Wysiwyg from "@/blocks/Wysiwyg.vue";
import ContainerToolbox from "@/components/ContainerToolbox.vue";
import WysiwygBlock from "../blocks/Wysiwyg.vue";


export default {
  name: 'WysiwygImage',
  props: {
    id: Number,
    order: {
      type: Number,
      default: 0,
    },
    container: {},
    lang: "",
  },
  components: {
    WysiwygBlock,
    ContainerToolbox,
    Wysiwyg,
    Image,
  },
  data() {
    return {
      blocks: [],
      wysiwygBlock: {},
      imageBlock: {},
      IconHelper,
      editorId: "",
    }
  },
  created() {
    this.init();
  },
  watch: {
    container() {
      //container, wijzigen input
      this.init();
    },
  },
  methods: {
    init() {
      this.blocks = this.container.blocks;
      this.editorId = "editor_" + this.id + "_" + this.lang + "_" + (new Date()).getTime();
      for (var key in this.blocks) {
        let block = this.blocks[key];
        if (block.type == "WysiwygBlock") {
          this.wysiwygBlock = block;
        }
        if (block.type == "ImageBlock") {
          this.imageBlock = block;
        }
      }
    },
    onBlockChange() {
      // console.log("onBlockChange:");
      // console.log(this.blocks);
      this.$emit("changeContent", this.blocks);
    },
    changeContent(content) {
      this.wysiwygBlock.attributes.content = content;
      this.onBlockChange();
    },

  }
}
</script>

<style scoped>
.wysiswg-image {
  display: flex;
  border: 1px solid #e6e6e666;
}

.wysiswg-image > div {
  width: 50%;
}

.wysiwyg {
  width: 100%;
}
</style>
