u<template>
  <div class="partial-container col2">
    <div class="partial">
      <div class="block-topbar">
        <div>
          Maatwerk container (niet aan<PERSON><PERSON>) <i v-if="partialBlock.attributes.visiblename!=''"> - {{partialBlock.attributes.visiblename}}</i>
        </div>
        <div class="toolbox"  v-if="editable" >
          <a v-html="IconHelper.ICON_EDIT" href="" @click.prevent="toolboxShow = !toolboxShow"  title="Bewerk eigenschappen" class="gsd-svg-icon-a"></a>
          <div class="toolbox-wrapper" v-show="toolboxShow" :ref="setToolboxEl">
            <div class="toolbox-head">
              Widget eigenschappen
              <a href="" v-html="IconHelper.ICON_CROSS" @click.prevent="toolboxShow = !toolboxShow"/>
            </div>
            <div class="toolbox-content">
              <div class="toolbox-content-row" v-if="classesWidget.length > 0">
                <div>Classes</div>
                <div>
                  <div v-for="item in classesWidget" :key="item.class">
                    <label :title="item.class">
                      <input type="checkbox" :value="item.class" v-model="partialBlock.attributes.classes"/>
                      {{ item.description }}
                    </label>
                  </div>
                </div>
              </div>
              <div class="toolbox-content-row">
                <div>Class</div>
                <input type="text" v-model="partialBlock.attributes.class" placeholder="Css class..."/>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="partial-content"  v-if="editable" >
        <table>
          <tr>
            <td>Action naam</td>
            <td><input type="text" v-model="partialBlock.attributes.action" placeholder="Action naam..."/></td>
          </tr>
          <tr>
            <td>Component naam</td>
            <td><input type="text" v-model="partialBlock.attributes.name" placeholder="Component naam..."/></td>
          </tr>
          <tr>
            <td>Zichtbare naam</td>
            <td><input type="text" v-model="partialBlock.attributes.visiblename" placeholder="Zichtbare naam voor klant..."/></td>
          </tr>
        </table>
        <div style="padding: 15px 5px 5px 5px;">
          Let op: dit is een component. Je dient een component class met method aan te maken in de action.
        </div>
      </div>
    </div>
    <ContainerToolbox
      :container="container"
      @removeContainer="this.$emit('removeContainer')"
      @save="this.$emit('save')"
    />
  </div>
</template>

<script>

import IconHelper from "@gsdvuefw/helpers/IconHelper";
import ContainerToolbox from "@/components/ContainerToolbox.vue";
import projectClasses from '@/composables/projectClasses';

export default {
  name: 'Partial',
  components: {ContainerToolbox},
  props: {
    id: Number,
    order: {
      type: Number,
      default: 0,
    },
    container: {},
    editable: false,
  },
  data() {
    return {
      blocks: [],
      partialBlock: {},
      IconHelper,
      toolboxShow: false,
      classesWidget: projectClasses().widget,
    }
  },
  created() {
    this.init();
    document.addEventListener('mouseup', this.clickOutside);
  },

  watch: {
    container() {
      //container, wijzigen input
      this.init();
    },
  },

  methods: {
    init() {
      this.blocks = this.container.blocks;
      for (var key in this.blocks) {
        let block = this.blocks[key];
        if (block.type == "PartialBlock") {
          this.partialBlock = block;
          if(this.partialBlock.attributes.classes === undefined) {
            this.partialBlock.attributes.classes = [];
          }
          //validate classes
          this.partialBlock.attributes.classes = this.partialBlock.attributes.classes.filter(cls => {
            for (let key in this.classesWidget) {
              if (this.classesWidget[key].class === cls) return true;
            }
            return false;
          });
        }
      }
    },
    onBlockChange(payload) {
      this.$emit("changeContent", this.blocks);
    },
    setToolboxEl(el) {
      if (el) {
        this.toolboxEl = el;
      }
    },
    clickOutside(e) {
      var el = this.toolboxEl;
      if (!(el === e.target || el.contains(e.target))) {
        this.toolboxShow = false;
      }
    },

  }
}

</script>

<style scoped>
.partial-container {
  display: flex;
  border: 1px solid #e6e6e666;
}

.partial {
  width: 100%;
}
.partial-content {
  padding: 15px;
}
.partial-content label {
  padding: 5px;
}
.partial-content table td {
  vertical-align: middle;
}

</style>
