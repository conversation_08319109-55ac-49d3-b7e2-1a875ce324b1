<template>
  <div class="wysiswg-container col2">
    <div class="editor">
      <WysiwygBlock
          :id="editorId"
          :key="editorId"
          :wysiwygBlock="wysiwygBlock"
          :order="order"
          @change="onBlockChange"
      />
    </div>
    <ContainerToolbox
      :container="container"
      @removeContainer="this.$emit('removeContainer')"
      @save="this.$emit('save')"
    />
  </div>
</template>

<script>
//:key="editorId" dit zorg voor een recreate van het component

import IconHelper from "@gsdvuefw/helpers/IconHelper";
import WysiwygBlock from "../blocks/Wysiwyg.vue";
import ContainerToolbox from "@/components/ContainerToolbox.vue"; //other name to prevent conflicts

export default {
  name: 'Wysiwyg',
  props: {
    id: Number,
    order: {
      type: Number,
      default: 0,
    },
    container: {},
    lang: "",
  },
  components: {
    ContainerToolbox,
    WysiwygBlock,
  },
  data() {
    return {
      blocks: [],
      wysiwygBlock: {},
      Icon<PERSON>el<PERSON>,
      editorId: "",
    }
  },
  created() {
    this.init();
  },

  watch: {
    container() {
      //container, wijzigen input
      this.init();
    },
  },

  methods: {
    init() {
      this.blocks = this.container.blocks;
      this.editorId = "editor_" + this.id + "_" + this.lang + "_" + (new Date()).getTime();
      for (var key in this.blocks) {
        let block = this.blocks[key];
        if (block.type == "WysiwygBlock") {
          this.wysiwygBlock = block;
        }
      }
    },
    onBlockChange() {
      this.$emit("changeContent", this.blocks);
    },
    changeContent(content) {
      this.wysiwygBlock.attributes.content = content;
      this.onBlockChange();
    },

  }
}

</script>

<style scoped>
.wysiswg-container {
  display: flex;
  border: 1px solid #e6e6e666;
}

.wysiwyg, .editor {
  width: 100%;
}
</style>
