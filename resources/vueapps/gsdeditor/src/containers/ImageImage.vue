<template>
  <div class="image-container">
    <div class="image" v-for="(imageBlock, index) in blocks" :key="index" >
      <div>
        <ImageBlock accept="png,jpg,jpeg,gif"
                    :key="'image_'+id+'_'+index"
               @change="onBlockChange"
               :imageBlock="imageBlock"
        />
      </div>
    </div>
    <ContainerToolbox
      :container="container"
      @removeContainer="this.$emit('removeContainer')"
      @save="this.$emit('save')"
    />
  </div>
</template>

<script>

import IconHelper from "@gsdvuefw/helpers/IconHelper";
import ImageBlock from "../blocks/Image.vue";
import ContainerToolbox from "@/components/ContainerToolbox.vue"; //other name to prevent conflicts

export default {
  name: 'ImageImage',
  props: {
    id: Number,
    order: {
      type: Number,
      default: 0,
    },
    container: {}
  },
  components: {
    ContainerToolbox,
    ImageBlock,
  },
  data() {
    return {
      blocks: [],
      Icon<PERSON>elper,
    }
  },
  created() {
    this.init();
  },

  watch: {
    container() {
      //container, wijzigen input
      this.init();
    },
  },

  methods: {
    init() {
      this.blocks = this.container.blocks;
    },
    onBlockChange() {
      this.$emit("changeContent", this.blocks);
    },
    changeContent(content) {
      this.onBlockChange();
    },

  }
}

</script>

<style scoped>
.image-container {
  display: flex;
  border: 1px solid #e6e6e666;
}

.image {
  width: 100%;
}
</style>
