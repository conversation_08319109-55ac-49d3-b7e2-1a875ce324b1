import {fileURLToPath, URL} from 'node:url'

import {resolve} from 'path'

import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@gsdvuefw': fileURLToPath(new URL('./../gsdvuefw', import.meta.url)),
    }
  },
  define: {
    public_dir: JSON.stringify('/gsdfw/projects/default/templates/gsdeditor')
  },
  server: {
    fs: {
      // Allow serving files from one level up to the project root
      allow: ['..'],
    },
  },
  // required for correct path of images
  base: '/gsdfw/projects/default/templates/gsdeditor',
  build: {
    // output folder for build files
    outDir: __dirname + '/../../../projects/default/templates/gsdeditor',
    // assetsDir is relative to outDir
    assetsDir: '/assets',
    // minify based on environment
    minify: ((process.env.NODE_ENV === 'development') ? false : true),
    rollupOptions: {
      output: {
        // create .min files only in production mode
        assetFileNames: (assetInfo) => {
          // only create .min files for index.css, not for other assets
          if (assetInfo.name !== 'index.css') return 'assets/[name].[ext]';
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].[ext]' : 'assets/[name].min.[ext]';
        },
        chunkFileNames: () => {
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].js' : 'assets/[name].min.js';
        },
        entryFileNames: () => {
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].js' : 'assets/[name].min.js';
        },
      },
    },
  },
})
