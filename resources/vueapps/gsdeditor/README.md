Gsd Blokken Editor
--------------------------
Dit is de SP<PERSON> van de blokkeneditor welke in pagina beheer word gebruikt.

Installatie instructies
----------------------
* Draai `npm ci` in de /vueapps/gsdeditor/ map

Algemene informatie
----------------------
Om de app te bekijken op pagina beheer, moet je deze activeren m.b.v. config GSDEDITOR.
Hiermee kun je de de editor configuren per project.

    Config::set("GSDEDITOR", [
        "active"                => true, //is blockeditor active
        "toggle"                => true, //may toggle between old and new editor
        "developer"             => true, //set true if you want to load in dev mode
        "tinymce_stylesheets"   => [
          '/projects/rde/templates/frontend/style/ckeditor_style.css?version=' . VERSION,
        ], //stylesheets to use in tinymce
        "tinymce_content_style" => 'https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700,900&display=swap', //extra style to use in tinymce
    ]);  //24-02-2022 - ROBERT - blockeditor


Er zijn 2 type builds: 
* Draai `npm run dev` om de development versie te draaien
* Draai `npm run build` om de productie versie te builden

Te bekijken op URL:
Je kunt de app bekijken als je een pagina bewerkt en het vinkje 'Gebruik de GSD blokken editor' aan te vinken.

PHP code:
De editor praat direct tegen de pagesAction, o.a. tegen executeGsdEditorGet() en executeGsdEditorSet()

