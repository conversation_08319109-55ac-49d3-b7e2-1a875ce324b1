{"name": "backend2", "version": "1", "private": true, "scripts": {"watch": "cd ../../../tools && npm run backend2-watch-development -- --config ../resources/default/backend2/webpack.config.js", "build-development": "cd ../../../tools && npm run backend2-build-development -- --config ../resources/default/backend2/webpack.config.js", "build-production": "cd ../../../tools && npm run backend2-build-production -- --config ../resources/default/backend2/webpack.config.js", "build-libraries-production": "cd ../../../tools && npm run backend2-build-libraries-production -- --config ../resources/default/backend2/webpack.libraries.config.js", "build-libraries-development": "cd ../../../tools && npm run backend2-build-libraries-development -- --config ../resources/default/backend2/webpack.libraries.config.js"}}