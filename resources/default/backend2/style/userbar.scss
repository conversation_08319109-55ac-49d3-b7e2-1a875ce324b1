
.userbar {
  display: flex;
  justify-content: space-between;
  padding: 10px 30px 10px 30px;
  height: 60px;
  background: var(--main-background-color);
  border: 1px solid var(--gray-200);

  .logo {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .logo-link {
      width: 150px;
      height: 100%;
      background-size: contain;
      background-repeat: no-repeat;
    }
  }

  .user-actions {
    display: flex;
    color: var(--gray-900);
    text-align: right;
    align-items: center;
    justify-content: flex-end;
    font-size: 1.4rem;

    & > * {
      margin-left: 18px;
    }

    .popout-box {

      summary {
        font-weight: 600;
        display: flex;
        align-items: center;

        &:hover {
          color: var(--primary-active-color);
        }

        i.user-icon {
          margin-right: 8px;
          font-size: 2rem;
        }

        .user-icon:before {
          content: "\eaa5";
        }


        i.caret {
          margin-left: 6px;
        }
      }

      .popout-content {
        padding: 10px 8px;
        text-align: left;

        a {
          display: block;
          padding: 4px 12px;

          &:hover {
            background-color: var(--gray-100);
          }

        }
      }
    }

    .companyname {
      font-style: italic;
    }

    a#return2admin {
      color: var(--primary-active-color);
    }

    .notification {
      position: relative;

      summary {
        padding-right: 6px;

        .notifications-amount {
          position: absolute;
          background-color: #ed1c24;
          z-index: 1101;
          right: -8px;
          top: -10px;
          color: white;
          text-align: center;
          padding: 2px 6px;
          font-weight: normal;
          border-radius: 100%;
          font-size: 1.2rem;
        }
      }

      a.notification-item {
        display: block;
        padding: 6px;
        //border-bottom: 1px solid var(--gray-200);

        span {
          text-decoration: underline;
        }
      }

    }
  }
}
.logo-image {
  background-image: url(../images/gsd_logo_rand.svg);
}

@media (min-width: 1600px) {
  /* only change when horizontal menu is active */
  .horizontal-menu-active .userbar {
    width: 1600px;
    margin: 0 auto;
    border: 0;
  }
}
