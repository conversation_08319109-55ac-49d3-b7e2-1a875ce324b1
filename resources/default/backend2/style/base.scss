/***********
  BASE is for all default html elements (no classes/ids)
************/

/* Google Fonts Import Link */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/*
  This is used as base, by setting the font-size this way, 1 rem becomes 10 px, so 1.6rem = 16px
  see: https://stackoverflow.com/a/43131958
 */
:root {
  //font-size: 53.6%; /* 1.4rem wordt nu 12px base font-size  */
  font-size: 62.5%; /* 10/16 (16px is default browser font-size) which is 0.625 = 62.5% */
  //font-size: 71.4%; /* 1.4rem wordt nu 16px base font-size  */
}

html, body {
  font-size: 1.4rem;
  font-family: 'Poppins', sans-serif;
  width: 100%;
  height: 100%;
  background: var(--main-background-color);
}

h1 {
  font-size: 3.2rem;
  font-weight: 600;
  margin: 0;
  color: var(--gray-900);
}
@media (max-width: 600px) {
  h1 {
    font-size: 2.5rem;
  }
}

h2 {
  font-size: 2.2rem;
  font-weight: 500;
  color: var(--gray-900);
}

h3 {
  font-size: 1.8rem;
  font-weight: 500;
  color: var(--gray-900);
}

h4 {
  font-size: 1.6rem;
  font-weight: 500;
  color: var(--gray-900);
}

td {
  vertical-align: top;
  text-align: left;
}

img {
  border: 0;
}

form {
  display: inline;
}

a {
  color: var(--gray-900);
  text-decoration: none;
}

a:hover {
  color: var(--primary-active-color);
}

/* move position slightly to make the click visual **/
a:active {
  position: relative;
  top: 1px;
  left: 1px;
}