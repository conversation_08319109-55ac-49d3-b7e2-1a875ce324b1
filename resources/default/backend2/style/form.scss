
/*** BASE ***/

input:focus,textarea:focus,select:focus, button:focus {
  outline: none;
}

input, select, textarea {
  &.disabled, &.readonly, &[disabled], &[readonly]  {
    background-color: var(--gray-100);
    border: 1px solid var(--gray-400);
    color: var(--gray-900);
    opacity: 0.7;
  }
}

::placeholder, [disabled]::placeholder, [readonly]::placeholder {
  color: rgb(170, 170, 170);
}

input[type=button][disabled], input[type=submit][disabled], input[type=button].disabled, input[type=submit].disabled {
  color: var(--gray-900);
}

%default-input-style {
  font-family: inherit;
  font-size: 1.4rem;
  border-radius: 6px;
  border: 1px solid var(--gray-200);
  padding: 12px 16px;
}

input, select, textarea {
  @extend %default-input-style;
}

textarea {
  width: 30rem;
}

input[type="text"], input[type="password"] {
  width: 30rem;
}

label input[type=checkbox] {
  vertical-align: middle;
}

input[type=checkbox], input[type=radio] {
  margin: 3px 3px 3px 4px;
  height: auto;
}

input[type=text]:focus, input[type=password]:focus, input[type=date]:focus, input[type=datetime]:focus, input[type=time]:focus, input[type=number]:focus, textarea:focus {
  border-color: var(--gray-400);
}

select {
  padding: 0.6rem 1rem;
}

option:disabled {
  color: #0091BD;
}

/**** SPECIFIC STYLING *****/
.inputerror {
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 4px -2px #DE0000;
  border: 1px solid #DE0000;
}

.inputerror:focus {
  box-shadow: 0 4px 8px -4px #DE0000;
}


/** making sure all input/select/a.buttons are same height **/
input, select {
  box-sizing: border-box;
  /*height: calc(1.5em + .5rem + 1.5px);*/
}

input[type="submit"][name="cancel"] {
  @extend %button-link;
  color: var(--gray-400);

  &:not([disabled]):hover {
    color: var(--primary-active-color);
    background: none;
    text-decoration: underline;
  }
}

input.datesel, input.datepicker, input.datepicker_week {
  width: 13rem;
}

input.datetimesel, input.datetimepicker {
  width: 16rem;
}

/* used for price inputs */
input.size {
  width: 8rem;
}
input.price, input.percentage {
  text-align: right;
  width: 10rem;
}

/** used for address fields */
input.address_street {
  width: 28rem;
}

input.address_nr {
  width: 6rem;
}

input.address_zip {
  width: 10rem;
}

input.address_city {
  width: 24rem;
}

.form-btn-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  row-gap: 16px;
  column-gap: 8px;
  padding: 24px 16px 8px 16px;
  margin: 12px 0;
}

.edit-form {
  display: block;
  @extend %default-white-block;

  /* content block met white space links/rechts */
  &.with-padding {
    padding: 16px;
  }

  .default_table {
    background: none;
    border: none;
    border-radius: 0;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--gray-200);
  }
}

form.edit-form > input[type="submit"],
form.edit-form > button,
.default_table > input[type="submit"],
.default_table > button {
  margin: 16px 0 16px 8px;
}

/** FILE UPLOAD VELDEN */
input[type=file] {
  border: 0;
}
input[type=file]::file-selector-button {
  @extend %button;
  @extend %button-secondary;
}