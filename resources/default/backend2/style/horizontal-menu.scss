.horizontal-menu-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--sidebar-background-color);
  color: var(--sidebar-color);

  nav {
    /* Reset */
    ul, li {
      margin: 0;
      padding: 0;
    }

    /* ROOT MENU */
    & > ul {
      display: flex;
      align-items: center;
      flex-wrap: wrap; /* wrap when the screen size is smaller as the root menu */
      margin: 0;
      padding: 0;
      list-style: none;
      position: relative;
      z-index: 1000;
      width: 100%;

      /* SUBMENUS */
      ul {
        width: 250px; /* Sub Menu Width */
        margin: 0;
        list-style: none;
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;

        li {
          width: 100%;
          display: block;
        }

        ul, ul ul {
          top: 0;
          left: 100%;
        }

        .has-sub-items.iehover > a {
          background-color: #f8f8f8;
        }
      }

      /* END SUBMENUS */

      li {
        position: relative;
        transition: 0.15s ease-in-out;

        &:hover {
          background: var(--sidebar-submenu-background-color);
        }
      }

      a {
        display: flex;
        align-items: center;
        gap: 8px;
        border-top: 0;
        padding: 20px 15px 20px 15px;
        color: var(--sidebar-color);
        font-size: 1.6rem;
        text-decoration: none;
        background: none;

        &.active {
          color: var(--sidebar-active-color);
        }

        &:hover {
          color: var(--sidebar-active-color);
        }
      }

      & > li.has-sub-items > a::after {
        content: "\f0d7";
        font-family: 'FontAwesome';
        margin-top: 3px;
        font-size: 13px;
        color: #d4d4d4;
      }

      /* 2nd Menu */
      li:hover li a, li.iehover li a {
        padding: 10px 20px;
        border-right: 0;
        background: var(--sidebar-submenu-background-color);
        color: var(--sidebar-color);
        font-size: 1.5rem;
        transition: all 0.3s ease;
      }

      li:hover li:last-child a {
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
      }


      /* 2nd Menu Hover Persistence */
      li:hover li a:hover, li:hover li:hover a, li.iehover li a:hover, li.iehover li.iehover a {
        color: var(--sidebar-active-color);
      }

      /* 3rd Menu */
      li:hover li:hover li a, li.iehover li.iehover li a {
        color: var(--sidebar-active-color);
      }

      /* 3rd Menu Hover Persistence */
      li:hover li:hover li a:hover, li:hover li:hover li:hover a, li.iehover li.iehover li a:hover, li.iehover li.iehover li.iehover a {
        color: var(--sidebar-active-color);
      }

      /* 4th Menu */
      li:hover li:hover li:hover li a, li.iehover li.iehover li.iehover li a {
        color: var(--sidebar-active-color);
      }

      /* 4th Menu Hover */
      li:hover li:hover li:hover li a:hover, li.iehover li.iehover li.iehover li a:hover {
        color: var(--sidebar-active-color);
      }

      /* Hover Function - Do Not Move */
      li:hover ul ul, li:hover ul ul ul, li.iehover ul ul, li.iehover ul ul ul {
        display: none;
      }

      li:hover ul, ul li:hover ul, ul ul li:hover ul, li.iehover ul, ul li.iehover ul, ul ul li.iehover ul {
        display: block;
      }
    }
  }

}

@media (max-width: 768px) {

  .horizontal-menu-bar {
    padding: 8px 0;

    nav {
      & > ul {
        a {
          padding: 8px 12px;
          font-size: 1.5rem;
        }
      }
    }
  }

}