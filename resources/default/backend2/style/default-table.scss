.default_table {
  @extend %default-white-block;
  width: 100%;
  border-spacing: 0;
  font-size: 1.4rem;
  margin-top: 10px;

  a {
    text-decoration: none;
  }

  .no-border-radius td, .no-border-radius th  {
    border-radius: 0 !important;
  }

  tr.dataTableRow {

    /* als de tr een td.sort-td (onzichtbare sorteer kolom) heeft, zet dan padding op de 2de td */
    &:has(.sort-td) td:nth-child(2) {
      padding-left: 15px !important;
    }

    /* border-radius op de onderste linker en rechter cellen van de tabel */
    &:last-child {
      td:first-child {
        border-bottom-left-radius: 12px;
        padding-left: 15px;
      }

      td:last-child {
        border-bottom-right-radius: 12px;
        padding-right: 15px;
      }
    }

    td {
      padding: 8px 4px 8px 8px;
      border-bottom: 1px solid var(--default-table-border-color);
      vertical-align: middle;

      &:first-child {
        padding-left: 15px;
      }

      &:last-child {
        padding-right: 15px;
      }

      /* gsd-btn in de tabel td niet zo groot
      a.gsd-btn {
        padding: 6px 12px;
      }*/

      a:not(.gsd-btn) {
        font-weight: 500;
        color: var(--gray-900);
        text-decoration: none;
      }

      a:not(.gsd-btn):hover {
        text-decoration: underline;
      }

      &.actions {
        text-align: right;
        width: 60px;

        /* has 2 children */
        &:has(> :nth-child(2):last-child) {
          width: 80px;
        }

        /* has 3 children */
        &:has(> :nth-child(3):last-child) {
          width: 100px;
        }

        /* has 4 children */
        &:has(> :nth-child(4):last-child) {
          width: 120px;
        }

        /* At least 6 (6 or more) children */
        &:has(> :nth-child(6)) {
          width: 150px;
        }

        .gsd-svg-icon-a {
          height: 24px;
          margin: 0 1px;
        }
      }
    }

    &.trhover:hover {
      background-color: var(--default-table-hover);
    }
  }

  tr.rowhighlight {
    background-color: #ffc107;
  }

  tr.rowhighlightalert td {
    background-color: #f44336;
    color: white;

    a {
      color: white;
    }
  }

  .dataTableHeadingRow {

    /* als de tr een td.sort-td (onzichtbare sorteer kolom) heeft, zet dan padding op de 2de td */
    &:has(.sort-td) td:nth-child(2) {
      padding-left: 15px !important;
    }

    &.nobottomborder td {
      border-bottom: 0;
    }

    &.notopborder td {
      border-top: 0;
      border-radius: 0 !important;
    }

    td {
      padding: 15px 7px;
      font-weight: 600;
      vertical-align: top;
      background-color: #fafafc;
      color: var(--gray-900);
      border-top: none;
      border-top: 1px solid var(--default-table-border-color);
      border-bottom: 1px solid var(--default-table-border-color);
      font-size: 1.3rem;

      &:first-child {
        border-top-left-radius: 12px;
        padding-left: 15px !important;
      }

      &:last-child {
        border-top-right-radius: 12px;
        padding-right: 15px;
      }

      a {
        text-decoration: none;

        &.order {

          &:first-child {
            margin-left: 4px;
          }

          .gsd-svg-icon svg {
            width: auto;
            height: auto;
            color: var(--gray-400);

            &:hover {
              color: var(--gray-700);
            }
          }

          &.orderactive .gsd-svg-icon svg {
            color: var(--gray-800);
          }
        }
      }

      &.actions {
        text-align: right;

        &.buttons-3 {
          min-width: 75px;
        }
      }
    }

    &.dataTableHeadingRowLow td {
      padding-top: 5px;
      padding-bottom: 5px;
    }

    .qtipa.fa_info.fa.fa-info-circle,
    .qtipa.fa_alert.fa.fa-exclamation-circle,
    .fa.fa_help {
      font-size: 14px;
    }

  }

  &.notopborder {
    border-top: 0;
    border-radius: 0 !important;
  }

  &.nobottomborder {
    border-bottom: 0;
  }

  label {
    /* voornamelijk styling voor labels met radio/checkbox erin */
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }

  a.fa.fa-sort-down, a.fa.fa-sort-up {
    text-decoration: none;
  }

}

.default_table_scroller {
  overflow-x: auto;
}

.default_table .material-icons, .default-data-table .material-icons {
  font-size: 17px;
}

.default_table_div {
  border-left: 1px solid #e6e6e666;
  border-right: 1px solid #e6e6e666;
  background-color: #F8F8F8;
}

.dataTableRow {
  vertical-align: top;
}

.dataTableHeadingTd {
  font-weight: bold;
}

.dataTableRowOver, .trhover:hover {
  background-color: #F5F8FD;
  height: 20px;
}

.dataTableHeadingRow td a.popuplink,
.dataTableHeadingRow td a.popuplink:hover,
.dataTableHeadingRow td a.popuplink:visited,
.dataTableHeadingRow td a.popuplink:active {
  text-decoration: none;
  color: white;
  font-weight: bold;
}

/* button to open child rows */
table.default-data-table.dataTable td.dt-control:before {
  font-weight: 500;
  line-height: 1.5;
  color: #181C32;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  font-size: 1.1rem;
  border-radius: 0.475rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: #F5F8FD;
  height: calc(1.5em + 0.2rem + 2px);
  width: calc(1.5em + 0.2rem + 2px);
  /* resets from original styling */
  margin-top: 0;
  box-shadow: none;
  font-family: inherit;
}

/* button to close child rows */
table.default-data-table.dataTable tr.dt-hasChild td.dt-control:before {
  background-color: hsl(218deg 67% 90%);
}


/** Formulier gerelateerde styling **/
/* zodat tekst achter input veldjes mooi in het midden staat */
.table_td_middle tr.dataTableRow td {
  line-height: 20px;
}

.head {
  height: 23px;
  font-weight: 500;
}

.tablehead {
  font-weight: 500;
}

.edit-form .default_table {
  margin-top: 0;

  .dataTableHeadingRow {
    td, th {
      font-size: 1.6rem;
      font-weight: 500;
      background: var(--gray-100);
      border-top: 1px solid var(--default-table-border-color);
      border-bottom: 1px solid var(--default-table-border-color);
    }

    &.topborder td, &.topborder th {
      border-radius: 0;
    }

    &.nobottomborder td, &.nobottomborder th {
      border-bottom: 0;
    }

    /** used for a headerrow not in top of the table **/
    &.notopborder td, &.notopborder th {
      border-top: 0;
      border-radius: 0 !important;
    }


  }

  .dataTableRow {
    td {
      border: none;
    }

  }
}

/* overzichten waar geen items getoond worden */
.empty-list-state {
  padding: 8px 12px;
}

/* soms staan er 2 tabelen en een edit-form met bijvoorbeeld begeleiden tekst. deze tekst heeft standaard spacing */
.edit-form-text {
  padding: 8px 12px;
}


@media (max-width: 600px) {

  .default_table {
    max-width: calc(100vw - var(--minimized-sidebar-width));
    overflow-x: scroll;

    select {
      /* forceer select breedte op mobiel */
      width: 100%;
    }
  }

  .edit-form .default_table {

    .dataTableRow {
      display: flex;
      height: auto;
      flex-wrap: wrap;
      margin-bottom: 12px;
      border-bottom: 1px solid var(--default-table-border-color);

      td {
        display: flex;
        height: auto;
        flex-wrap: wrap;
        gap: 12px;

        &.head {
          display: block;
          width: 100%;
          font-size: 1.6rem;
        }
      }
    }

  }
}