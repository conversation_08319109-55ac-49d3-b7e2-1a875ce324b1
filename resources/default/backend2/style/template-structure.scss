/** structure elements for all template pages (...Success.php) */

.content-container {
  margin: 5px auto 15px;
  max-width: 1600px;
  padding: 0 15px;
}

@media (max-width: 600px) {
  .content-container {
    padding: 0;
  }
}

.content {
  padding: 0 0 25px 0;
  vertical-align: top;
  min-height: 400px;
}

.title-bar {
  display: flex;
  flex-wrap: wrap; /* put tabs below page title if there are too many tabs */
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  margin-top: 16px;

  .title {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
  }

  /** ik heb deze tijdelijk uit gezet. Gaf veel geneuzel. Ook met submenu welke meerdere items bevatten **/
  .title-bar h1, .title-bar h2 {
    //display: none;
  }
}


.module-description {
  display: block;
  width: 100%;
  font-size: 1.5rem;
  /* for better readability of long lines */
  max-width: 1000px;
  line-height: 2rem;
  padding: 8px 0px;

  h1, h2, h3 {
    margin: 16px 0 24px 0;
  }

  p {
    color: var(--gray-600);
  }
}

%default-white-block {
  background: white;
  border-radius: 12px;
  border: 1px solid hsl(60, 9%, 93%);
}

.content-block {
  @extend %default-white-block;
  padding-top: 12px;
  padding-bottom: 12px;
  margin-bottom: 16px;
  font-size: 1.5rem;
  line-height: 2rem;

  /* content block met white space links/rechts */
  &.with-padding {
    padding-left: 16px;
    padding-right: 16px;
  }

  &.home {
    margin: 36px auto;
    max-width: 600px;
    padding: 24px;

    img {
      margin: 24px 0;
    }
  }
}

@media (max-width: 600px) {
  section.main-grid.vertical-menu-active main {
    max-width: calc(100vw - var(--minimized-sidebar-width));
  }

  .content-block {
    overflow: scroll;
  }
}