:root {
  --big-circle-right: -128%;
  --small-circle-left: -92%;
  --big-circle-position: -876px;
  --small-circle-position: -757px
}

@media (min-width: 480px) {
  :root {
    --big-circle-right: -229%;
    --small-circle-left: -82%;
    --big-circle-position: -2947px;
    --small-circle-position: -1173px
  }
}

@media (min-width: 728px) {
  :root {
    --big-circle-right: -203%;
    --small-circle-left: -102%
  }
}

@media (min-width: 992px) {
  :root {
    --big-circle-right: -143%;
    --small-circle-left: -68%
  }
}

@media (min-width: 1200px) {
  :root {
    --big-circle-right: -102%;
    --small-circle-left: -58%
  }
}

@media (min-width: 1600px) {
  :root {
    --big-circle-right: -41%;
    --small-circle-left: -15%
  }
}

@keyframes bigCircleOut {
  0% {
    right: var(--big-circle-right)
  }
  to {
    right: var(--big-circle-position)
  }
}

@keyframes bigCircleIn {
  0% {
    right: var(--big-circle-position)
  }
  to {
    right: var(--big-circle-right)
  }
}

@keyframes smallCircleOut {
  0% {
    left: var(--small-circle-left)
  }
  to {
    left: var(--small-circle-position)
  }
}

@keyframes smallCircleIn {
  0% {
    left: var(--small-circle-position)
  }
  to {
    left: var(--small-circle-left)
  }
}

body {
  .circles {
    &.closing {
      .bigCircle {
        animation: bigCircleIn 1s 0s;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
        animation-timing-function: cubic-bezier(.86, 0, .07, 1)
      }

      .smallCircle {
        animation: smallCircleIn 1s 0s;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
        animation-timing-function: cubic-bezier(.86, 0, .07, 1)
      }
    }

    &.opened {
      .bigCircle {
        animation: bigCircleOut 1s 0s;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
        animation-timing-function: cubic-bezier(.86, 0, .07, 1)
      }

      .smallCircle {
        animation: smallCircleOut 1s 0s;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
        animation-timing-function: cubic-bezier(.86, 0, .07, 1)
      }
    }

    .bigCircle, .smallCircle {
      position: fixed
    }

    .bigCircle {
      top: -70px;
      width: 2947px;
      height: 2947px;
      border-radius: 2947px;
      text-align: center;
      background: linear-gradient(204deg, #fff 15.48%, hsla(0, 0%, 100%, 0) 62.97%);
      opacity: .12
    }

    .smallCircle {
      top: 50%;
      width: 1173px;
      height: 1173px;
      border-radius: 1173px;
      opacity: .06;
      background: linear-gradient(204deg, #fff 15.48%, hsla(0, 0%, 100%, 0) 50.01%)
    }

  }

}

@media (max-width: 480px) {
  body .circles .bigCircle {
    width: 876px;
    height: 876px;
    top: -3px
  }
}


@media (max-width: 480px) {
  body .circles .smallCircle {
    width: 757px;
    height: 757px;
    top: 40%
  }
}

body.body_login {
  background: linear-gradient(107deg, var(--gsd-primary-color), var(--gsd-btn-primary-bg-color-hover) 139.5%);
}
