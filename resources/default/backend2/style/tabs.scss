.tabbable {

}

#tabnav {
  margin: 5px 0 5px 8px;
  padding-left: 0;
  display: flex;
  flex-wrap: wrap;
  font-size: 1.5rem;

  li {
    position: relative;
    /** offset for border bottom line **/
    //top: 3px;
    margin: 5px 15px 5px 10px;
    padding: 0;
    display: inline-flex;
    list-style-type: none;
    //background: #f0f0f080;
    border-bottom: 2px solid transparent;
    flex-direction: column;
  }

  a {
    font-weight: 500;
    padding: 8px 0;
    text-decoration: none;
    color: var(--gray-900);
    display: inline-block;
    flex: 1;
  }

  a.here0,
  a.here1,
  a.here2,
  li.active,
  a.nu,
  a.active,
  a:hover {
    //border-bottom: 2px solid var(--primary-active-color);
    color: var(--primary-active-color);
  }

  a.active {
    color: var(--primary-active-color);
  }

  a:hover {
    color: var(--primary-active-color);
    transition: color .2s;
  }

  li::after {
    content: '';
    display: block;
    width: 0;
    height: 2px;
    background: var(--primary-active-color);
    transition: width .2s;
  }

  li.active::after, li:hover::after {
    width: 100%;
    //transition: width .3s;
  }

  #tabnav a:visited.here {
    background: var(--gsd-primary-color);
  }

  a:link.disabled, a:visited.disabled {
    color: #dbdbdb;
  }
}

#tabnav_wiz {
  span.here0,
  span.here1,
  span.here2,
  span.active {
    color: #000;
    font-weight: bold;
  }
}