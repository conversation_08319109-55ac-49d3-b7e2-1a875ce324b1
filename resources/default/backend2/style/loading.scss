@keyframes rotateSpinnerLoader {
  0% {
    transform: rotate(0deg)
  }

  to {
    transform: rotate(1turn)
  }
}

@keyframes fadeOutLoader {
  0% {
    opacity: 1;
    visibility: visible
  }

  to {
    opacity: 0;
    visibility: hidden
  }
}

@keyframes fadeInLoader {
  0% {
    opacity: 0;
    visibility: hidden
  }

  to {
    opacity: 1;
    visibility: visible
  }
}


#loading-spinner {

  position: fixed;
  background-color: rgba(51, 51, 51, .66);
  height: 100%;
  width: 100%;
  z-index: 1300;
  opacity: 0;
  visibility: hidden;

  .spinner {
    position: absolute;
    left: 50%;
    top: 50%;
    height: 50px;
    width: 50px;
    transform: translateX(-50%);

    &:after, &:before {
      content: "";
      position: absolute;
      border-radius: 100%;
      border: 2px solid #fff;
      border-top-color: transparent;
      width: 50px;
      height: 50px
    }

    &:before {
      z-index: 100;
      animation: rotateSpinnerLoader 1s 0s;
      animation-iteration-count: infinite;
      animation-fill-mode: forwards;
      animation-timing-function: ease-in-out
    }

    &:after {
      border: 2px solid transparent
    }

  }

  &.hide-spinner {
    animation: fadeOutLoader .4s 1;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
    animation-timing-function: ease-in-out
  }

  &.show-spinner {
    animation: fadeInLoader .4s 1;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
    animation-timing-function: ease-in-out
  }


}

