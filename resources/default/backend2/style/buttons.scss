/*
 gsd-btn is the standard class for buttons.
 this class can be set on:
 - a elements
 - input submit/button elements
 Also some helper
  css for buttons with icons from the IconHelper
*/

%button {
  display: inline-flex;
  padding: 12px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border: 1px solid transparent;
  cursor: pointer;
  border-radius: 50px;

  text-align: center;
  text-decoration: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: var(--gsd-btn-bg-color);
  color: var(--gsd-btn-text-color);
  font-size: 1.4rem;
}

%button-hover {
  background-color: var(--gsd-btn-bg-color-hover);
  color: var(--gsd-btn-text-color-hover);
}

%button-primary {
  background-color: var(--gsd-btn-primary-bg-color);
  color: var(--gsd-btn-primary-text-color);
}

%button-primary-hover {
  background-color: var(--gsd-btn-primary-bg-color-hover);
  color: var(--gsd-btn-primary-text-color-hover);
}

%button-secondary {
  border: 1.5px solid var(--gsd-btn-secondary-border-color);
  color: var(--gsd-btn-secondary-text-color);
  background: none;
}

%button-secondary-hover {
  background-color: var(--gsd-btn-secondary-bg-color-hover);
  color: var(--gsd-btn-secondary-text-color-hover);
}

%button-tertiary {
  border: 1.5px solid var(--gsd-btn-tertiary-border-color);
  color: var(--gsd-btn-tertiary-text-color);
  background: none;
}

%button-tertiary-hover {
  background-color: var(--gsd-btn-tertiary-bg-color-hover);
  color: var(--gsd-btn-tertiary-text-color-hover);
}

%button-disabled {
  background-color: var(--gray-100) !important;
  color: rgb(170, 170, 170) !important;
  opacity: 0.7 !important;
  box-shadow: none;
  border: 1px solid transparent !important;
}

%button-link {
  box-shadow: none;
  background: none;
  border: 1px solid transparent;
  padding: 12px;
}
%button-link-hover {
  box-shadow: none;
  background: none;
  border: 1px solid transparent;
}

.gsd-btn  {
  @extend %button;

  /** PRIMARY **/
  &.gsd-btn-primary {
    @extend %button-primary;

    &:hover:not(:disabled) {
      @extend %button-primary-hover;
    }

    &.gsd-btn-icon .gsd-svg-icon svg {
      color: var(--gsd-btn-primary-text-color);
    }
  }

  /** SECONDARY **/
  &.gsd-btn-secondary {
    @extend %button-secondary;

    &:hover:not(:disabled) {
      @extend %button-secondary-hover;
    }

    &.gsd-btn-icon .gsd-svg-icon svg {
      color: var(--gsd-btn-secondary-text-color);
    }
  }

  /** TERTIARY **/
  &.gsd-btn-tertiary {
    @extend %button-tertiary;

    &:hover:not(:disabled) {
      @extend %button-tertiary-hover;
    }
  }

  /** LINK **/
  &.gsd-btn-link {
    @extend %button-link;

    &:hover:not(:disabled) {
      color: var(--primary-active-color);
    }
  }

  /** DISABLED **/
  &[disabled], &[disabled]:hover, &.disabled, &.disabled:hover {
    @extend %button-disabled;
  }

  &[readonly], &[readonly]:hover, &.readonly, &.readonly:hover {
    border: 1px solid transparent !important;
  }


  /** WITH ICON **/
  &.gsd-btn-icon {
    display: inline-flex;
    align-items: center;
    padding: 8px 24px;

    span {
      margin-left: -0.25rem;
      padding-right: 0.25rem;
    }
  }

  &.gsd-btn-small {
    padding: 8px 12px;
  }
}

/** overwriting button styling of plugins **/
.dataTables_wrapper .dt-buttons button {
  @extend %button;
  @extend %button-secondary;
  /** default button has a background linear, which we cant overrule with background-color */
  background: var(--gray-100);
  padding: 3px 10px;
  line-height: initial;
}

/** all inputsubmit/button default have the gsd-btn style. makes life a lot easier **/
input[type=submit], input[type=button] {
  @extend %button;

  &:hover:not(:disabled) {
    @extend %button-hover;
  }
}

.qq-upload-button {
  @extend %button;
  @extend %button-primary;

  &:hover:not(:disabled) {
    @extend %button-secondary-hover;
  }
}

input[type=file]::file-selector-button {
  @extend %button;
  @extend %button-primary;
}
input[type=file]:hover:not(:disabled)::file-selector-button {
  @extend %button-secondary-hover;
}

