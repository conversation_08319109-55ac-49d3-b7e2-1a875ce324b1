/**** ERRORS/ALERTS START *****/

.asterisk, .error {
  color: #B94A48;
}

tr.alert td {
  color: #B94A48;
}

.alert {
  padding: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
  border-radius: 12px;
  border: 1px solid transparent;


  &.alert-danger {
    border: 1px solid #db351e;
    background: #f2e2e4;
    color: #db351e;
  }

  &.alert-warning {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc;
  }

  &.alert-success {
    color: var(--gsd-primary-color);
    background-color: var(--gsd-primary-color-100);
    border-color: var(--gsd-primary-color);
    font-weight: 500;
  }

  .alert-link, a {
    font-weight: 700;
  }

}

.content-block .alert {
  margin-left: 12px;
  margin-right: 12px;
}


.alert-dismissable, .alert-dismissible {
  padding-right: 35px;
}

.alert-danger .alert-link {
  color: #843534;
}

.alert-warning .alert-link {
  color: #66512c;
}

.alert-success .alert-link {
  color: #245269;
}

.alert .fa {
  font-size: 1.6rem;
  vertical-align: top;
  padding-right: 5px;
}

div.errorbox ul,
div.warningbox ul,
div.alert ul {
  padding: 0 20px;
  margin: 5px 0 0 0;
  list-style-type: disc;
}

.messagered {
  padding: 16px;
  margin: 5px 0;
  background-color: #F2E2E4;
  border: 1px solid #DB351E;
  border-radius: 12px;
  color: #DB351E;
}

.message {
  padding: 16px;
  margin: 5px 0;
  background-color: #E6F1F6;
  border: 1px solid #BCE8F1;
  border-radius: 12px;
  color: #29235C;
}
