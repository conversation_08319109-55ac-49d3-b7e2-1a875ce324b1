table.nextprev {
  @extend %default-white-block;
  border-collapse: initial;
  width: 100%;
  border-spacing: 0;
  padding: 12px 12px;
  font-size: 1.5rem;

  tr {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
  }

  td.nextprev {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;

    a, .nextprevactive {
      font-weight: normal;
      vertical-align: middle;
      box-sizing: border-box;
      display: inline-block;
      min-width: 1.5em;
      padding: 0.5em 1em;
      margin-left: 2px;
      text-align: center;
      text-decoration: none;
      cursor: pointer;
      color: inherit !important;
      border: 1px solid transparent;
      border-radius: 2px;
      transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

      &:first-child .gsd-svg-icon svg, &:last-child .gsd-svg-icon svg {
        width: auto;
        height: auto;
        vertical-align: baseline;
      }

      &:hover {
        background: var(--primary-active-color) !important;
        border-color: transparent !important;
        color: var(--gsd-btn-text-color) !important;
      }
      &:hover:not(:disabled) {
        background: var(--gsd-btn-bg-color-hover);
        text-decoration: none;
        border-color: transparent;
      }
    }

    .nextprevactive {
      color: var(--primary-active-color);
      border: 1px solid var(--primary-active-color);
    }

    /* pager met Bedrijfsnaam begint met .... */
    .pager_firstletter {
      font-size: 1.4rem;
      padding: 0.5em 0.5em;
    }

    a.pager_letterselected {
      color: var(--primary-active-color);
      border: 1px solid var(--primary-active-color);
      padding: 3px 7px;
      font-weight: 600;
    }
  }

  i.fa.fa-chevron-right {
    display: none;
  }

}
