/*********************
 * Algemene classes die overal gebruikt kunnen worden
 * en niet in een specifiek bestand horen
 *********************/

hr.divider {
  display: block;
  height: 1px;
  border: none;
  background: var(--gray-200);
  width: 100%;
}

.clear {
  clear: both;
}

.lockonsubmit_spinner {
  margin-left: 5px;
  margin-right: 5px;
  color: #f44336;
}

div.scroll {
  height: 300px;
  overflow: auto;
  background-color: White;
  padding: 5px 10px 10px 10px;
}

.hidden {
  display: none;
}

a.imagegallery {
  padding: 10px 0;
  display: inline-block;
}

.multi-select {
  width: 100%;
  height: 400px;
}

div.st {
  padding-top: 10px;
  font-size: 12px;
  padding-left: 0;
  float: left;
}

input.productsize {
  width: 30px;
  text-align: right;
  margin: 8px 0 0 0;
}

.tabscontent {
  width: 100%;
  display: none;
}

.selectprod {
  padding: 3px 0;
  display: block;
}

.languageselect {
  vertical-align: middle;
  display: inline-block;
}

/* hide div */
#orderform .emailcust {
  display: none;
}

a.delproduct, a.minbasket, a.plusbasket {
  display: inline-block;
  text-decoration: none;
  font-size: 16px;
  padding: 3px;
}

a.delproduct img {
  vertical-align: bottom;
}

/* PRODUCT TABLE */
.producttable {
  float: left;
  border-top: 1px solid #E9E9E9;
  margin-top: 3px;
  width: 700px
}

a.plusinput, a.mininput {
  text-decoration: none;
  font-size: 17px;
  padding: 8px 2px;
}

.not_in_backorder_info {
  font-style: italic;
  margin-top: 5px;
  display: inline-block;
}

.status_div {
  float: left;
  width: 125px;
  margin-right: 3px;
  padding: 8px;
  text-decoration: none;
  font-weight: normal;
  font-size: 12px;
  border-radius: 3px;
}

.orderstatusdiv {
  float: left;
  width: 154px;
  margin-right: 3px;
  padding: 8px;
  text-decoration: none;
  font-weight: normal;
  font-size: 12px;
  border-radius: 3px;
}

.orderstatusdiv:hover {
  text-decoration: none;
}

.info-box a {
  text-decoration: none;
}

.phonebutton {
  display: inline-block;
  vertical-align: middle;
}


/* CKEDITOR MODAL */
.fm-modal {
  z-index: 10011; /** Because CKEditor image dialog was at 10010 */
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border: 0;
  position: fixed;
  -moz-box-shadow: 0px 1px 5px 0px #656565;
  -webkit-box-shadow: 0px 1px 5px 0px #656565;
  -o-box-shadow: 0px 1px 5px 0px #656565;
  box-shadow: 0px 1px 5px 0px #656565;
}