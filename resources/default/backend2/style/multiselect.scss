.gsd-select {
  position: relative;
  display: inline-block;
  line-height: normal;
}

.gsd-select-placeholder {
  min-width: 160px;
  display: block;
  border: 1px solid var(--gray-200);
  padding: 0.8rem 1rem;
}

.gsd-select-placeholder, .gsd-select-options {
  flex-direction: column;
  font-family: inherit;
  border-radius: 6px;
  background-color: white;
  box-sizing: border-box;
  user-select: none;
}

.gsd-select-options {

  max-height: 500px;
  overflow: auto;
  cursor: default;
  position: absolute;
  z-index: 1;
  display: none;
  border: 1px solid #bbbbbba6;
  padding: 4px 4px;

  label {
    padding: 0.4rem;

    &:hover {
      background-color: var(--gsd-btn-bg-color-hover);
    }

    input {
      margin-right: 10px;
    }

  }

}

.gsd-select-option-selected {
  background-color: #f7f8fa;

  &:hover {
    background-color: var(--gsd-btn-bg-color-hover);
  }

}

.gsd-select-searchbox {
  width: auto !important;
  margin: 5px;
}

.gsd-select-flex {
  display: flex;
}

.gsd-select-empty {
  font-weight: bold;
}