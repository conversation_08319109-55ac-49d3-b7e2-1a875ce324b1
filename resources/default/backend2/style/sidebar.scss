.sidebar {
  width: 276px;
  z-index: 100;
  transition: all 0.3s;

  .logo {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    padding: 8px;
    overflow: hidden;
    border-bottom: 1px solid var(--sidebar-logo-border-color);
    background-size: 125px auto;
    background-position: center;
    background-repeat: no-repeat;
    transition: background-size 0.5s;
  }

  .toggle-menu {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    color: var(--sidebar-color);
    padding: 20px 10px 0 0;
    font-size: 1.5em;
    user-select: none;
    cursor: pointer;

    .toggle-sidebar:before {
      content: "\e9e0";
    }

    &:hover {
      color: var(--sidebar-active-color);
    }

    &.open {
      justify-content: center;
      padding-right: 0;
      transition: all 0.4s ease;

      .toggle-sidebar:before {
        content: "\e9e2";
      }
    }
  }

  nav {
    padding: 10px 0 150px 0;

    /** overflow: auto;  uitgezet, gaf scrollbar op firefox bij openklappen menu**/

    ul {
      padding-left: 0;
      margin-top: 0;
    }

    li {
      position: relative;
      list-style: none;
      transition: all 0.4s ease;
      margin: 6px 12px;

      &.sub-items-header {
        display: none;
      }

      .nav-item {
        display: flex;
        align-items: center;
        justify-content: space-between;

        &.active * {
          color: var(--sidebar-active-color);
        }

        a {
          display: flex;
          flex-grow: 1;
          align-items: center;
          text-decoration: none;
          font-size: 1.6rem;
          font-weight: 500;
          color: var(--sidebar-color);

          .link-name {
            transition: all 0.4s ease;
          }
        }

        i {
          height: 44px;
          min-width: 41px;
          text-align: center;
          line-height: 44px;
          color: var(--sidebar-color);
          cursor: pointer;
          transition: transform 0.3s ease;
        }

        i.nav-item-icon {
          font-size: 1.8rem;
        }

        i.arrow {
          font-size: 0.7em;
        }
      }

      &:hover {
        background: var(--sidebar-submenu-background-color);
        border-radius: 6px;

        > .nav-item {
          a {
            * {
              color: var(--sidebar-active-color);
            }

            .link-name {
              color: var(--sidebar-active-color);
            }
          }

          i {
            color: var(--sidebar-active-color);
          }
        }
      }

      /** SUBMENU **/
      ul {
        padding: 0 0 12px 29px;
        display: none;

        a {
          font-weight: 500;
          color: var(--gray-400);
          font-size: 1.5rem;
          padding: 4px 0;
          transition: all 0.3s ease;
        }
        /** no items on sub levels **/
        i.ki-solid {
          display: none;
        }
        /** SUB SUB MENU **/
        ul {
          padding: 0 0 12px 7px;
        }
      }

      &.sub-open {
        background: var(--sidebar-submenu-background-color);
        border-radius: 6px;
      }

      /* de 2de en 3de selectors zorgen ervoor dat het submenu openklapt en blijft als je hovert op het links icoontje */
      &.sub-open, &:has(i.nav-item-icon:hover), &:has(ul:hover) {
        > li i.arrow, > div i.arrow {
          transform: rotate(-180deg);
        }

        > ul {
          /* dit moet een inline-block zijn, anders werkt hover op linker icoontje niet goed */
          display: inline-block;
        }

        a:hover {
          opacity: 1;
        }
      }

    }

    li.active:before,
    li:hover:before {
      opacity: 1;
    }

    > li.active:before,
    > li:before {
      position: absolute;
      left: 0;
      top: 0;
      content: '';
      width: 4px;
      height: 100%;
      background: #5B5B5B;
      opacity: 0;
      transition: all 0.25s ease-in-out;
      border-top-right-radius: 5px;
      border-top-right-radius: 5px;
    }
  }

  nav::-webkit-scrollbar {
    display: none;
  }

  /** Minimized NAVBAR **/
  &.minimized {
    position: fixed;
    height: 100vh;
    width: var(--minimized-sidebar-width);
    overflow: hidden;

    .logo {
      background-size: 55px auto;

      .logo_name {
        transition-delay: 0s;
        opacity: 0;
        pointer-events: none;
      }
    }

    nav {
      overflow: visible;

      .link-name {
        margin-left: 20px;
      }

      li.sub-open > ul {
        display: none;
      }
    }

    &.minimized-hover {
      /* Hover on minimized sidebar opens the full sidebar */
      &:hover {
        width: 276px;

        .toggle-menu.open {
          justify-content: flex-end;
          padding-right: 10px;
        }

        .link-name {
          margin-left: 0px;
        }

        li.sub-open > ul {
          display: block;

          li a {
            /* this will make the animation look nicer, because the text wont change height */
            text-wrap: nowrap;
          }
        }
      }
    }
  }
}


@media (max-width: 420px) {
  .sidebar.minimized nav li ul {
    display: none;
  }
}


/* ICONS SIDEBAR */
.sidebar .nav-item-icon:before {
  content: "\e906";
}

.sidebar .nav-item-icon.M_HOME:before {
  content: "\ea41" !important;
}

.sidebar .nav-item-icon.M_CATALOG:before {
  /*content: "\f00b" !important;*/
  content: "\e91b" !important;
}

.sidebar .nav-item-icon.M_WEBSITE:before {
  content: "\e9d4" !important;
}

.sidebar .nav-item-icon.M_ORGANISATIONS:before,
.sidebar .nav-item-icon.M_USERSIMPLE:before {
  content: "\e936" !important;
}


.sidebar .nav-item-icon.M_INVOICES:before {
  content: "\e9b2" !important;
}

.sidebar .nav-item-icon.M_ORDER_OV:before {
  content: "\e95c" !important;
}

.sidebar .nav-item-icon.M_WEBSITE_MESSAGES:before {
  content: "\ea71" !important;
}

.sidebar .nav-item-icon.M_HOURS:before {
  content: "\eb0f" !important;
}

.sidebar .nav-item-icon.M_WORKER_PROJECT:before {

}

.sidebar .nav-item-icon.M_OTHER:before {
  content: "\e906" !important;
}

.sidebar .nav-item-icon.M_SETTINGS:before {
  content: "\eac6" !important;
}

.sidebar .nav-item-icon.M_SMOELEN:before {
  content: "\ea09" !important;
}

.sidebar .nav-item-icon.M_STATS:before {
  content: "\e992" !important;
}

