table.dataTable.default-data-table {
  width: 100% !important;
  border-spacing: 0;
  border: 1px solid #e6e6e666;
  border-radius: 3px 3px 0 0;
  margin: 8px 0;

  a {
    text-decoration: none;
  }

  tr td {
    padding: 3px;
    border-bottom: 1px solid #e6e6e666;
    vertical-align: middle;

    &:first-child {
      padding-left: 15px;
    }

    &:last-child {
      padding-right: 5px;
    }

    &.actions {
      text-align: right;
    }

    /* gsd-btn in de tabel td niet zo groot
    a.gsd-btn {
      padding: 6px 12px;
    }
    */

  }

  thead tr th {
    padding: 12px 7px;
    font-weight: 600;
    vertical-align: top;
    background-color: #fafafc;
    color: var(--gray-900);
    border-top: none;
    border-top: 1px solid var(--default-table-border-color);
    border-bottom: 1px solid var(--default-table-border-color);
    font-size: 1.3rem;

    &:first-child {
      padding-left: 15px;
    }

    &:last-child {
      padding-right: 5px;
    }

    &.actions {
      text-align: right;

      &.buttons-3 {
        min-width: 75px;
      }
    }
  }

  tbody tr {
    /* standaard/minimale hoogte van 40px, zodat tabellen zonder Actions kolom dezelfde hoogte hebben als met de Actions kolom */
    height: 40px;
    vertical-align: top;

    &:hover {
      background-color: var(--default-table-hover);
      /* standaard/minimale hoogte van 40px, zodat tabellen zonder Actions kolom dezelfde hoogte hebben als met de Actions kolom */
      height: 40px;
    }
  }

  &.no-footer {
    border: none;
  }

  /* Grouped by rows */
  tr.dtrg-group th {
    background-color: var(--gray-100);
    vertical-align: middle;
  }
  tr.dtrg-group.dtrg-level-0 th {
    font-weight: 500;
  }

}

.dataTables_wrapper .dt-buttons {
  margin-left: 15px;
}

.dataTables_wrapper .dt-buttons button {
  font-family: 'Open Sans', sans-serif;
  background-color: var(--gsd-btn-bg-color);
  color: var(--gsd-btn-text-color);
  border: 1px solid transparent;
  background-image: none;
  padding: 4px 10px;
  text-align: center;
  text-decoration: none;
  border-radius: 3px;
  font-size: inherit;
  line-height: inherit;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.dataTables_wrapper .dt-buttons button:hover:not(:disabled) {
  background: var(--gsd-btn-bg-color-hover);
  text-decoration: none;
  border-color: transparent;
}

.dataTables_paginate {
  margin-right: 16px;
  margin-bottom: 8px;
}

.paginate_button.current {
  background: none !important;
  border: 1px solid var(--primary-active-color) !important;
  color: var(--primary-active-color) !important;

}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  background-color: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  font-weight: normal;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--primary-active-color) !important;
  border-color: transparent !important;
  color: var(--gsd-btn-text-color) !important;
}


/* Select checkbox for row selection */
table.dataTable.compact > tbody > tr > td.select-checkbox:before, table.dataTable.compact > tbody > tr > th.select-checkbox:before {
  margin-top: 0 !important;
}

table.dataTable.compact > tbody > tr.selected > td.select-checkbox:after, table.dataTable.compact > tbody > tr.selected > th.select-checkbox:after {
  margin-top: -24px !important;
}

table.dataTable tbody tr.selected > * {
  box-shadow: inherit !important;
  background-color: #B0BED9;
}

div.dataTables_processing {
  background: white;
  box-shadow: 1px 4px 10px 0 rgb(0 0 0 / 53%), 0 3px 2px 0 rgb(0 0 0 / 15%);
  border-radius: 3px;
  padding: 10px 10px 0 10px !important;
  width: 250px;
  max-width: 100%;
  z-index: 10000;
}

/* content-block toevoegen anders kunnen we de styles van datatables niet overschrijven */
.content-block {
  .dataTables_wrapper {
    .dataTables_length {
      margin-left: 16px;

      select {
        @extend %default-input-style;
        padding: 4px 6px !important; /* smaller default padding */
      }
    }
  }
}

.dataTables_info {
  margin-left: 16px;
}