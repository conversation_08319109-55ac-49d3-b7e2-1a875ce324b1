div.box, .list-filter-form {
  display: flex;
  column-gap: 8px;
  row-gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--gray-200);
  padding: 10px 16px;

  input[type="search"], input[type="text"], input[type="number"], select {
    background: white;
    border: 1px solid var(--gray-200);
    padding: 8px 12px;
    line-height: normal; /* zodat ze dezelfde hoogte hebben */
  }

  input[type="submit"] {
    @extend %button-secondary;
    padding: 10px 24px;
  }

  input[name="reset"], input[type="submit"].reset-btn {
    @extend %button-link;

    &:hover {
      @extend %button-link-hover;
    }
  }

  label {
    display: inline-flex;
    align-items: center;
  }


  input.datesel, input.datepicker, input.datepicker_week {
    width: 11rem;
  }
}

.select2.select2-container {
  height: 39px !important;
  max-width: 350px;
}

.select2-container .select2-selection--single,
.select2-container--default .select2-selection--single .select2-selection__rendered,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  height: 39px !important;
  border-color: var(--gray-200) !important;
  border-radius: 6px !important;
}

.select2-container .select2-selection--multiple {
  min-height: 39px !important;
  border-color: var(--gray-200) !important;
  border-radius: 6px !important;
}

.select2-container .select2-search--inline .select2-search__field {
  margin-top: 8px !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 39px !important;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  top: calc(50% - 13px) !important;
}


@media (max-width: 600px) {
  div.box, .list-filter-form {
    select {
      /* forceer select breedte op mobiel */
      width: 100%;
    }
  }
}