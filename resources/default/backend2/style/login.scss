body.body_login {
  background: url("../images/background_login.jpg") center no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;

  .main-grid {
    display: grid;
    width: 100%;
    height: 100%;
    grid-template-areas:
        "head"
        "main";
    grid-template-columns: auto;
    grid-template-rows: auto 1fr;

    /* Login part */
    main {
      display: flex;
      align-items: center;
      flex-direction: column;
      background: none;
      grid-area: main;
      padding: 0px;

      > div {
        margin: auto 0;
        border-radius: 12px;
        background: var(--gray-900);
        width: 580px;
        font-size: 1.6rem;
        padding: 70px 80px 80px 80px;
        z-index: 1;

        #loginscrn {
          color: var(--gray-400);

          a {
            color: var(--gray-400);
          }

          > div {
            background: var(--gray-900);
            border-radius: 0 0 8px 8px;
          }

          > h2 {
            display: block;
            margin: 0;
            margin-bottom: 40px;
            font-size: 3.2rem;
            color: white;
            text-align: center;
          }

          label {
            display: block;
            margin-top: 24px;
            color: var(--gray-400);
          }

          input[type=submit], input[type=button] {
            display: block;
            width: 100%;
            margin-top: 8px;
            padding: 16px 12px;
            font-size: 1.6rem;
          }

          input[type=text], input[type=password] {
            display: block;
            width: 100%;
            margin-top: 8px;
            border-radius: 6px;
            background: var(--gray-800);
            padding: 16px 12px;
            border: none;
            color: white;
          }

          .below-password {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-top: 8px;
            margin-bottom: 40px;
            font-size: 1.4rem;
          }
        }

        #login_header {
          width: 100%;
          max-width: 100%;
          height: 64px;
          background-image: url(../images/gsd_logo_rand.svg);
          background-color: var(--gray-900);
          background-position: center center;
          background-size: 180px;
          background-repeat: no-repeat;
          margin-bottom: 40px;
        }
      }
    }
  }
}

@media (max-width: 600px) {

  body.body_login {
    .main-grid {
      main {
        max-width: 100vw;
        > div {
          margin: auto 15px;
          width: calc(100% - 30px);
          padding: 40px 25px;
        }
      }
    }
  }
}