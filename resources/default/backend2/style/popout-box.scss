.popout-box {
  position:relative;
}

.popout-box summary {
  display: inline-block;
  cursor: pointer;
}

.popout-box .popout-content {
  display: block;
  transform-origin: top left;
  position: absolute;
  left: 0px;
  margin-top: 1rem;
  padding: 5px 8px;
  background: white;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  border: 1px solid var(--gray-200);
  transition: 10000ms ease all;
  z-index:    10000;
}

.popout-box .popout-content.right {
  left: initial;
  right: 0px;
}