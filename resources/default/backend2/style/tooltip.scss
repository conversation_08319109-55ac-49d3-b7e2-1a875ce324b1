.qtipa.fa_info.fa.fa-info-circle, .qtipa.fa_alert.fa.fa-exclamation-circle, .fa.fa_help {
  font-size: 17px;
  text-decoration: none;
}

.qtipa.fa_alert.fa.fa-exclamation-circle {
  color: #e43331;
}

.qtip-def {
  border: 1px solid #D2D2D2;
  color: #000;
  font-size: 12px;
  background-color: white;
  border-radius: 5px 5px;
}

.qtip-def .qtip-titlebar {
  background-color: var(--gsd-primary-color);
  color: white;
  border-radius: 5px 5px 0 0;
}

.qtip {
  line-height: inherit;
}

.qtipa:hover {

}

#tooltip {
  border-radius: 4px;
  background: hsl(240, 15%, 18%);
  color: white;
  display: none;
  z-index: 100000;
  width: auto;
  max-width: 600px;
  a {
    color: white;
  }
}

#tooltip-title {
  color: white;
  padding: 10px 15px;
  font-size: 1.4rem;
  font-weight: bold;
  border-bottom: 1px solid hsl(240, 15%, 38%);
}

#tooltip-content {
  font-size: 1.4rem;
  padding: 10px 15px;
}

#tooltip-arrow,
#tooltip-arrow::before {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--gray-800);
  z-index: -1;
}

#tooltip-arrow {
  visibility: hidden;
}

#tooltip-arrow::before {
  visibility: visible;
  content: '';
  transform: rotate(45deg);
}

#tooltip[data-popper-placement^='top'] > #tooltip-arrow {
  bottom: -6px;
}

#tooltip[data-popper-placement^='bottom'] > #tooltip-arrow {
  top: -6px;
}

#tooltip[data-popper-placement^='left'] > #tooltip-arrow {
  right: -6px;
}

#tooltip[data-popper-placement^='right'] > #tooltip-arrow {
  left: -6px;
}
