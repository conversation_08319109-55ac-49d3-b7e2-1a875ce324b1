.main-grid {
  display: grid;
  width: 100%;
  min-height: 100%;
  grid-template-areas:
    "head head"
    "nav userbar"
    "nav horizontal-nav"
    "nav main"
    "nav foot";
  grid-template-rows: auto auto auto 1fr auto;
  grid-template-columns: auto 1fr;

  &.minimal-layout {
    /* only header and main content area */
    grid-template-areas:
    "head"
    "main";
    grid-template-rows: auto 1fr;
    grid-template-columns: 1fr;
  }

  header {
    grid-area: head;
  }

  aside {
    grid-area: nav;
    background-color: var(--sidebar-background-color);
  }

  .horizontal-menu-bar {
    grid-area: horizontal-nav;
  }

  main {
    grid-area: main;
    position: relative;
    transition: all 0.5s ease;
    padding: 0 10px;
  }

  footer {
    grid-area: foot;
    display: flex;
    justify-content: space-around;
    padding: 20px;
  }
  footer * {
    color: hsl(217deg 15% 70%);
  }

  .userbar {
    grid-area: userbar;
  }

  &.minimized-sidebar {
    main, header, footer, .userbar {
      margin-left: var(--minimized-sidebar-width);
    }

  }
}

.has_leftmenu {
  display: flex;
}

.has_leftmenu .div_leftmenu {
  width: 200px;
  margin-right: 15px;
  padding: 10px 0;
  background-color: #f0f0f080;
}

div.content.has_leftmenu {
  padding-top: 5px;
}