const node_modules_root = "../../../tools/node_modules/";
const path = require("path");
const MiniCssExtractPlugin = require(node_modules_root + "mini-css-extract-plugin");
const CssMinimizerPlugin = require(node_modules_root + "css-minimizer-webpack-plugin");
const TerserJSPlugin = require(node_modules_root + "terser-webpack-plugin"); // for minimizing JS

const DEVMODE = process.env.NODE_ENV !== 'production';
const PROJECT = !process.env.PROJECT ? 'default' : process.env.PROJECT;
const DIR_GSDFW = '../';

let MAIN_ENTRY = DIR_GSDFW + 'resources/' + PROJECT + '/backend2/style/imports.css';
let DIR_OUTPUT = "../../../projects/default/templates/backend2/dist/";
let URL_DIST = "/gsdfw/projects/default/templates/backend2/dist/";
if(PROJECT!="default") {
  DIR_OUTPUT = "../../../../projects/" + PROJECT + "/templates/backend2/dist/";
  URL_DIST = "/projects/" + PROJECT + "/templates/backend2/dist/";
}
if(['rde', 'jam', 'vdlcontainer', 'heblad', 'bodel', 'jari'].includes(PROJECT)) {
  //rde + jam is verplaatst naar project level. Zoals het hoort.
  MAIN_ENTRY = '../../projects/'+PROJECT+'/resources/backend2/style/imports.css';
}

module.exports = {
  optimization: {
    minimizer: [
      new TerserJSPlugin({}),
      new CssMinimizerPlugin({})
    ]
  },
  entry: [
    MAIN_ENTRY,
  ],
  output: {
    filename: DEVMODE ? "main.js" : "main.min.js",
    path: path.resolve(__dirname, DIR_OUTPUT),
  },
  mode: process.env.NODE_ENV,
  module: {
    rules: [
      {
        test: /\.css$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              // you can specify a publicPath here
              // by default it use publicPath in webpackOptions.output
              publicPath: '../'
            }
          },
          {
            loader: 'css-loader',
            options: {
              importLoaders: 2,  // 2 => postcss-loader, sass-loader
            }
          },
          {
            loader: 'sass-loader',
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  require(node_modules_root + 'postcss-import'),
                  require(node_modules_root + 'autoprefixer'),
                ],
              },
            },
          },
        ]
      },
      {
        test: /\.(gif|png|jpe?g|svg)$/i,
        type: 'asset/resource',
        generator: {
          publicPath: URL_DIST,
        },
        use: [
          // Minify PNG, JPEG, GIF, SVG and WEBP images
          {
            loader: "image-webpack-loader",
            options: {
              bypassOnDebug: true
            }
          }
        ]
      },
      {
        test: /\.(woff(2)?|ttf|eot|otf)(\?v=\d+\.\d+\.\d+)?$/,
        type: 'asset/resource',
        generator: {
          publicPath: URL_DIST + "fonts/",
          outputPath: "fonts/",
          filename: "[name][ext]"
        },
      },
    ]
  },
  plugins: [
    new MiniCssExtractPlugin({
      // Options similar to the same options in webpackOptions.output
      // both options are optional
      filename: DEVMODE ? "main.css" : "main.min.css",
      chunkFilename: DEVMODE ? "[id].css" : "[id].min.css",
    }),
  ],
};