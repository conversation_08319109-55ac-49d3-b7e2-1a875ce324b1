//letop: webpack kan geen dynamische paden laden.

var $ = require("../../../tools/node_modules/jquery");
window.$ = window.jQuery = $;

//sweetalert
window.swal = require("../../../tools/node_modules/sweetalert2");


//onderstaande staan nog in index.php

//general.js kan niet, gebruikt global functions. General.js moet eigenlijk herschreven worden tot zijn eigen module.
//https://stackoverflow.com/questions/37793132/export-global-function-using-webpack
//require("../../../gsdfw/includes/jsscripts/general.js");

//require("https://cdn.jsdelivr.net/es6-promise/latest/es6-promise.auto.min.js"); //load from cdn
//require("https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700"); //load from cdn, google determines best font from user-agent
//require("https://fonts.googleapis.com/icon?family=Material+Icons"); //load from cdn, google determines best font from user-agent
