const node_modules_root = "../../../tools/node_modules/";
const { colors } = require(node_modules_root+'tailwindcss/defaultTheme');

module.exports = {
  prefix: '',
  important: true,
  separator: ':',
  purge: {
    enabled: (process.env.NODE_ENV === 'production'), // dont purge on development
    content: [ //scan alle modules en templates van de backend
      '../../gsdfw/modules/**/*.php', //alle modules van gsdfw
      '../../gsdfw/projects/default/templates/backend/pages/*.php', //alle templates van default backend
      //'../../projects/*/templates/backend/pages/*.php', //alle templates van de project specifieke backend
      //'../../projects/**/*.php',
      //'../../projects/**/*.html',
    ],
  },
  theme: {
    screens: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1200px',
    },
    colors: {
      'black': '#000000',
      'white': '#ffffff',
      'primary': '#007BC7',
      'primary-dark': '#0263a0',
      'secondary': '#B43929',
      'grey': {
        900: '#111111',
        800: '#2c2c2c',
        700: '#707070',
        600: '#969696',
        500: '#b4b4b4',
        400: '#d3d3d3',
        300: '#cacaca',
        250: '#e3e3e3',
        200: '#eeeeee',
        100: '#f9f9f9',
      },
      'blue': {
        200: '#bee3f8',
        400: '#65a5de',
        500: '#4299e1',
        800: '#2c5282',
      },
      'green': {
        400: '#42d750'
      },
      'red': colors.red,
    },
    fontFamily: {
      'primary': ['Open Sans', 'sans-serif'],
      'secondary': ['Open Sans', 'sans-serif'],
    },
    container: {
      center: true,
    },
    // extend default theme values
    extend: {
      // add these colors only for borders
      borderColor: {
      }
    }
  },
  variants: {
    appearance: ['responsive'],
    borderColor: [],
    colors: [],
    display: ['responsive','group-hover'],
    // ...
    zIndex: ['responsive'],
  },
  plugins: [
    // ...
  ],
}