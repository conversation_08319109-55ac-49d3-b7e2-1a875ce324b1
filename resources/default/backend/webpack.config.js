const node_modules_root = "../../../tools/node_modules/";
const path = require("path");
const MiniCssExtractPlugin = require(node_modules_root + "mini-css-extract-plugin");
const {CleanWebpackPlugin} = require(node_modules_root + "clean-webpack-plugin");
const webpack = require(node_modules_root + "webpack");
const CssMinimizerPlugin = require(node_modules_root + "css-minimizer-webpack-plugin");
const TerserJSPlugin = require(node_modules_root + "terser-webpack-plugin"); // for minimizing JS

const DEVMODE = process.env.NODE_ENV !== 'production';
const PROJECT = !process.env.PROJECT ? 'default' : process.env.PROJECT;
const DIR_GSDFW = '../';

let DIR_RESOURCE_PROJECT = '../resources/default/backend/';
let DIR_OUTPUT = "../../../projects/default/templates/backend/dist";
let URL_DIST = "/gsdfw/projects/default/templates/backend/dist/";

if(PROJECT!="default") {
  //old school, resources staan niet in het project
  DIR_RESOURCE_PROJECT = '../../resources/' + PROJECT + '/backend/';
  DIR_OUTPUT = "../../../../projects/" + PROJECT + "/templates/backend/dist/";
  URL_DIST = "/projects/" + PROJECT + "/templates/backend/dist/";
}
if(PROJECT=="vdlcontainer") {
  //new school, resources staan in het project
  DIR_RESOURCE_PROJECT = '../../projects/'+PROJECT+'/resources/backend/';
}
let MAIN_ENTRY = DIR_RESOURCE_PROJECT + 'style/main.css';

module.exports = {
  optimization: {
    minimizer: [
      new TerserJSPlugin({}),
      new CssMinimizerPlugin({})
    ]
  },
  entry: [
    DIR_GSDFW + 'resources/default/backend/backend.js',
    DIR_GSDFW + 'includes/font-awesome/css/font-awesome.min.css',
    DIR_GSDFW + 'tools/node_modules/simple-line-icons/dist/styles/simple-line-icons.css',
    DIR_GSDFW + 'includes/jsscripts/jqueryui/js/jquery-ui-1.10.4.custom.min.js',
    DIR_GSDFW + 'tools/node_modules/jquery-migrate/dist/jquery-migrate.min.js',
    DIR_GSDFW + 'includes/jsscripts/jqueryui/js/jquery.ui.datepicker-nl.js',
    DIR_GSDFW + 'includes/jsscripts/jqueryui/css/custom-theme/jquery-ui-1.10.4.custom.min.css',
    DIR_GSDFW + 'tools/node_modules/tablednd/dist/jquery.tablednd.min.js',
    DIR_GSDFW + 'tools/node_modules/devbridge-autocomplete/dist/jquery.autocomplete.min.js',
    DIR_GSDFW + 'tools/node_modules/flatpickr/dist/flatpickr.min.js',
    DIR_GSDFW + 'tools/node_modules/flatpickr/dist/l10n/nl.js',
    DIR_GSDFW + 'tools/node_modules/flatpickr/dist/flatpickr.min.css',
    DIR_GSDFW + 'tools/node_modules/flatpickr/dist/themes/dark.css',
    DIR_GSDFW + 'tools/node_modules/simplelightbox/dist/simple-lightbox.min.js',
    DIR_GSDFW + 'tools/node_modules/simplelightbox/dist/simple-lightbox.min.css',
    MAIN_ENTRY,
  ],
  output: {
    filename: DEVMODE ? "deploy.js" : "deploy.min.js",
    path: path.resolve(__dirname, DIR_OUTPUT),
  },
  mode: process.env.NODE_ENV,
  module: {
    rules: [
      {
        test: /\.css$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              // you can specify a publicPath here
              // by default it use publicPath in webpackOptions.output
              publicPath: '../'
            }
          },
          {
            loader: 'css-loader',
            options: {
              importLoaders: 1, // 1 => postcss-loader
            }
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  require(node_modules_root + 'postcss-import'),
                  require(node_modules_root + 'tailwindcss')(DIR_RESOURCE_PROJECT + 'tailwind.config.js'),
                  require(node_modules_root + 'autoprefixer'),
                ],
              },
            },
          },
        ]
      },
      {
        test: /\.(gif|png|jpe?g|svg)$/i,
        type: 'asset/resource',
        generator: {
          publicPath: URL_DIST,
        },
        use: [
          // Minify PNG, JPEG, GIF, SVG and WEBP images
          {
            loader: "image-webpack-loader",
            options: {
              bypassOnDebug: true
            }
          }
        ]
      },
      {
        test: /\.(woff(2)?|ttf|eot|otf)(\?v=\d+\.\d+\.\d+)?$/,
        type: 'asset/resource',
        generator: {
          publicPath:  URL_DIST + "fonts/",
          outputPath: "fonts/",
          filename: "[name][ext]"
        },
      },
    ]
  },
  plugins: [
    new CleanWebpackPlugin({
      cleanOnceBeforeBuildPatterns: ["**/*", "!**/main.min.css", "!**/main.css", "!**/deploy.min.js", "!**/deploy.js"],
    }),
    new MiniCssExtractPlugin({
      // Options similar to the same options in webpackOptions.output
      // both options are optional
      filename: DEVMODE ? "[name].css" : "[name].min.css",
      chunkFilename: DEVMODE ? "[id].css" : "[id].min.css",
    }),
  ],
};