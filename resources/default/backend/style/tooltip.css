.qtipa.fa_info.fa.fa-info-circle, .qtipa.fa_alert.fa.fa-exclamation-circle, .fa.fa_help {
  font-size: 17px;
  text-decoration: none;
}

.qtipa.fa_alert.fa.fa-exclamation-circle {
  color: #e43331;
}

.qtip-def {
  border: 1px solid #D2D2D2;
  color: #000;
  font-size: 12px;
  background-color: white;
  border-radius: 5px 5px;
}

.qtip-def .qtip-titlebar {
  background-color: var(--gsd-primary-color);
  color: white;
  border-radius: 5px 5px 0 0;
}

.qtip {
  line-height: inherit;
}


#tooltip {
  border-radius: 4px 4px 0 0;
  background-color: white;
  display: none;
  box-shadow: 2px 5px 10px rgb(0 0 0 / 30%);
  z-index: 100000;
}

#tooltip-title {
  background-color: #0071ff;
  color: white;
  padding: 10px 15px;
  border-radius: 4px 4px 0 0;
  font-size: 13px;
  font-weight: bold;
}

#tooltip-content {
  background-color: white;
  color: black;
  font-size: 13px;
  padding: 10px 15px;
  border-radius: 4px 4px 0 0;
}

#tooltip-arrow,
#tooltip-arrow::before {
  position: absolute;
  width: 12px;
  height: 12px;
  background: inherit;
  border: 1px solid #b4b4b4;
  z-index: -1;
}

#tooltip-arrow {
  visibility: hidden;
}

#tooltip-arrow::before {
  visibility: visible;
  content: '';
  transform: rotate(45deg);
}

#tooltip[data-popper-placement^='top'] > #tooltip-arrow {
  bottom: -6px;
}

#tooltip[data-popper-placement^='bottom'] > #tooltip-arrow {
  top: -6px;
}

#tooltip[data-popper-placement^='left'] > #tooltip-arrow {
  right: -6px;
}

#tooltip[data-popper-placement^='right'] > #tooltip-arrow {
  left: -6px;
}
