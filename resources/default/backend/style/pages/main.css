/**
@todo: de vraag is of dit hier moet blijven staan, of dat we dit gaan zetten bij de module.
De module word gedeployed, dus dat is niet handig eigenlijk, dus hier is nog niet zo slecht.
*/
.allimages {
  display: inline-block;
  border: 1px dashed lightgrey;
  position: relative;
}

.allimages img {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
}

.allimages .imagegallery {
  padding: 0;
  width: 150px;
  height: 120px;
  line-height: 100px;
}

.allimages .actionmenu {
  background-color: white;
  display: block;
  padding: 0 8px;
}

.allimages .actionmenu a {
  text-decoration: none;
  display: inline-block;
}

.allimages .actionmenu .material-icons {
  font-size: 22px;
  padding: 0 3px;
}

.allimages .ch_delete {
}

.allimages .btn_crop {
  bottom: 3px;
  text-decoration: none;
  display: block;
}
