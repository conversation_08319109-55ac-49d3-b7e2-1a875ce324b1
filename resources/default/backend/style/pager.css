table.nextprev {
  border-collapse: initial;
  width: 100%;
  border-spacing: 0;
  background-color: #f2f2f229;
  padding: 5px;
}

table.nextprev tr {
  display: flex;
  align-items: center;
}

td.nextprev {
  text-transform: uppercase;
  vertical-align: middle;
}

.nextprev a {
  text-decoration: none;
  font-weight: normal;
  display: inline-block;
  padding: 2px;
}

.nextprev a .gsd-svg-icon svg {
  width: auto;
  height: auto;
}

.nextprev a:hover {
  color: #3768B3;
}

.nextprev i.fa.fa-chevron-right {
  font-size: 8px;
  font-weight: normal;
  color: #cacaca;
}

.nextprevactive {
  color: #02207A;
  font-weight: bold;
}
a.pager_letterselected {
  color: blue;
  font-weight: bold;
}
