.gsd-select {
  position: relative;
  display: inline-block;
  line-height: normal;
}
.gsd-select-placeholder{
  min-width: 160px;
  display: block;
}
.gsd-select-options {
  max-height: 500px;
  overflow: auto;
  cursor: default;
  position: absolute;
  z-index: 1;
  display: none;
}
.gsd-select-placeholder, .gsd-select-options {
  flex-direction: column;
  border: 1px solid #bbbbbba6;
  padding: 4px 4px;
  font-family: inherit;
  border-radius: 3px;
  background-color: white;
  box-sizing: border-box;
  user-select: none;
}
.gsd-select-options label {
  padding: 0.4rem;
}
.gsd-select-option-selected {
  background-color: #f7f8fa;
}
.gsd-select-options label:hover, .gsd-select-option-selected:hover {
  background-color: var(--gsd-btn-bg-color-hover);
}
.gsd-select-searchbox {
  width: auto !important;
  margin: 5px;
}
.gsd-select-options label input {
  margin-right: 10px;
}
.gsd-select-flex {
  display: flex;
}