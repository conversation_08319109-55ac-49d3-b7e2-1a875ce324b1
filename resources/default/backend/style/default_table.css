.default_table {
  width: 100%;
  border-spacing: 0;
}

.default_table_scroller {
  overflow-x: auto;
}

.default_table .material-icons, .default-data-table .material-icons {
  font-size: 17px;
}

.default_table a {
  text-decoration: none;
}

.default_table tr.dataTableRow td {
  padding: 5px;
  border-bottom: 1px solid #e6e6e666;
  vertical-align: middle;
}
.default_table tr.rowhighlight {
  background-color: #ffc107;
}
.default_table tr.rowhighlightalert td {
  background-color: #f44336;
  color: white;
}
.default_table tr.rowhighlightalert td a {
  color: white;
}

table.default_table tr.dataTableHeadingRow td {
  border-top: 1px solid #e6e6e666;
  border-bottom: 1px solid #e6e6e666;
  text-transform: uppercase;
}

table.default_table tr.dataTableHeadingRow.nobottomborder td {
  border-bottom: 0;
}

table.default_table tr.dataTableHeadingRow.topborder td, .dataTableRow.topborder td {
  border-top: 0;
}
table.default_table tr.dataTableHeadingRow.bottomborder td, .dataTableRow.bottomborder td {
  border-bottom: 0;
}
tr.dataTableHeadingRow .qtipa.fa_info.fa.fa-info-circle,
tr.dataTableHeadingRow .qtipa.fa_alert.fa.fa-exclamation-circle,
tr.dataTableHeadingRow .fa.fa_help {
  font-size: 14px;
}
table.default_table.notopborder {
  border-top: 0;
}

table.default_table.nobottomborder {
  border-bottom: 0;
}

.default_table_div {
  border-left: 1px solid #e6e6e666;
  border-right: 1px solid #e6e6e666;
  background-color: #F8F8F8;
}

.dataTableHeadingRow a {
  text-decoration: none;
}

.dataTableHeadingRow td {
  font-size: 11px;
  padding: 10px 5px;
  font-weight: bold;
  vertical-align: top;
  color: #ffffff;
  background-color: #bab9b9;
}

.dataTableHeadingRow .order:first-child {
  margin-left: 4px;
}
.dataTableHeadingRow .order .gsd-svg-icon svg {
  width: auto;
  height: auto;
  color: #A6A6ABFF;
}

.dataTableHeadingRow .order.orderactive .gsd-svg-icon svg {
  color: #272735FF;
}

.dataTableHeadingRow.dataTableHeadingRowLow td {
  padding: 5px;
}

.default_table .dataTableHeadingRow td {
  background-color: #F5F8FD;
  color: #3A505B;
}

.dataTableRow {
  height: 20px;
  vertical-align: top;
}

.dataTableHeadingTd {
  font-weight: bold;
}

.dataTableRowOver, .trhover:hover {
  background-color: #F5F8FD;
  height: 20px;
}

.dataTableHeadingRow td a.popuplink,
.dataTableHeadingRow td a.popuplink:hover,
.dataTableHeadingRow td a.popuplink:visited,
.dataTableHeadingRow td a.popuplink:active {
  text-decoration: none;
  color: white;
  font-weight: bold;
}
.default_table label {
  display: inline-block;
}
.default_table a.fa.fa-sort-down, .default_table a.fa.fa-sort-up {
  text-decoration: none;
}
/* button to open child rows */
table.default-data-table.dataTable td.dt-control:before {
  font-weight: 500;
  line-height: 1.5;
  color: #181C32;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  font-size: 1.1rem;
  border-radius: 0.475rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: #F5F8FD;
  height: calc(1.5em + 0.2rem + 2px);
  width: calc(1.5em + 0.2rem + 2px);
  /* resets from original styling */
  margin-top: 0;
  box-shadow: none;
  font-family: inherit;
}
/* button to close child rows */
table.default-data-table.dataTable tr.dt-hasChild td.dt-control:before {
  background-color: hsl(218deg 67% 90%);
}