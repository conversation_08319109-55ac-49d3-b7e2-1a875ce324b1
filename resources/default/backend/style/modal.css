.gsd-modal {
  display: none;
  z-index: 10000;
  overflow-y: auto;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.gsd-modal.gsd-modal-show {
  display: block;
}

.gsd-modal > div {
  padding: 0;
  display: block;
  text-align: center;
  justify-content: center;
  align-items: flex-end;
  min-height: 100vh;
}

.gsd-modal-bg-glass {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: fixed;
  /*transition-property: opacity;*/
  /*transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);*/
  /*transition-duration: 1500ms;*/
}

.gsd-modal-bg-glass > div {
  opacity: 0.5;
  background-color: black;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.gsd-modal-container {
  width: 800px;
  max-width: 100%;
  display: inline-block;
  vertical-align: middle;
  margin-top: 2rem;
  margin-bottom: 2rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  box-shadow: 0 0 #0000, 0 0 #0000, var(--tw-shadow);
  text-align: left;
  border-radius: 0.5rem;
  overflow: hidden;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.gsd-modal-loader {
  max-width: 100%;
}

.gsd-modal-header {
  display: flex;
  justify-content: space-between;
  background-color: var(--gsd-primary-color);
  color: #ffffff;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 1.5rem;
}

.gsd-modal-content {
  min-height: 400px;
  padding: 1.5em;
  background-color: white;
}

.gsd-modal-close svg {
  color: white;
  height: 1.5em;
  width: 1.5em;
}

.gsd-modal-close:hover svg {
  color: var(--gsd-secondary-color);
  height: 1.5em;
  width: 1.5em;
}