/*
 gsd-btn is the standard class for buttons.
 this class can be set on:
 - a elements
 - input submit/button elements
 Also some helper css for buttons with icons from the IconHelper
*/

.gsd-btn {
  background-color: var(--gsd-btn-bg-color);
  color: var(--gsd-btn-text-color);
  box-sizing: border-box;
  height: calc(1.5em + .5rem + 1.5px);
  cursor: pointer;
  border: 1px solid transparent;
  display: inline-block;
  padding: 4px 10px;
  font-weight: normal;
  text-align: center;
  text-decoration: none;
  border-radius: 3px;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.gsd-btn:visited {
  color: var(--gsd-btn-text-color);
}

.gsd-btn:hover:not(:disabled) {
  color: var(--gsd-btn-text-color-hover);
  background: var(--gsd-btn-bg-color-hover);
  text-decoration: none;
}

.gsd-btn[disabled], .gsd-btn[disabled]:hover, .gsd-btn.disabled, .gsd-btn.disabled:hover {
  background-color: var(--gsd-btn-bg-color) !important;
  color: #DDDDDD !important;
  box-shadow: none;
}

.gsd-btn.gsd-btn-primary {
  background-color: var(--gsd-btn-primary-bg-color);
  color: var(--gsd-btn-primary-text-color);
}

.gsd-btn.gsd-btn-primary:hover:not(:disabled) {
  color: var(--gsd-btn-primary-text-color-hover);
}

.gsd-btn.gsd-btn-primary:hover:not(:disabled) {
  background: var(--gsd-btn-primary-bg-color-hover);
}

.gsd-btn.gsd-btn-secondary {
  background-color: var(--gsd-btn-secondary-bg-color);
  color: var(--gsd-btn-secondary-text-color);
}

.gsd-btn.gsd-btn-secondary:hover:not(:disabled) {
  color: var(--gsd-btn-secondary-text-color-hover);
}

.gsd-btn.gsd-btn-secondary:hover:not(:disabled) {
  background: var(--gsd-btn-secondary-bg-color-hover);
}

/* gsd-btn in combination with gsd-icon */
.gsd-btn.gsd-btn-icon {
  display: inline-flex;
  align-items: center;
}

.gsd-btn.gsd-btn-icon span {
  margin-left: -0.25rem;
  padding-right: 0.25rem;
}
.gsd-btn.gsd-btn-primary.gsd-btn-icon .gsd-svg-icon svg {
  color: var(--gsd-btn-primary-text-color);
}
.gsd-btn.gsd-btn-secondary.gsd-btn-icon .gsd-svg-icon svg {
  color: var(--gsd-btn-secondary-text-color);
}
