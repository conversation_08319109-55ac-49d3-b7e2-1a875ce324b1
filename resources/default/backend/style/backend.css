@import "tailwind_fixes.css";

@import "variables.css";
@import "base.css";
@import "navigation.css";
@import "login.css";
@import "default_table.css";
@import "data_table.css";
@import "notifications.css";
@import "pager.css";
@import "tooltip.css";
@import "icons.css";
@import "buttons.css";
@import "row-sorter.css";
@import "modal.css";
@import "multiselect.css";

/* modules specific css */
@import "pages/main.css";


/**
migratie naar backend 2
opmaak aanpassingen zodat backend template niet te veel verandert terwijl we naar de backend2 template gaan,
door de html wijzigingen die we doen
**/

/** titels verbergen, die bestaan niet in de backend template */
.title-bar h1, .title-bar h2 {
  display: none;
}
.title-bar .gsd-btn {
  margin-bottom: 15px;
}


.default_table td.actions {
  text-align: right;
  padding-right: 10px;
}

.gsd-btn.gsd-btn-secondary,
.gsd-btn.gsd-btn-primary {
  background-color: var(--gsd-btn-bg-color);
  border: 1px solid transparent;
  border-radius: 3px;
  color: var(--gsd-btn-text-color);
  cursor: pointer;
  padding: 4px 10px;
  text-align: center;
  text-decoration: none;
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.gsd-btn.gsd-btn-secondary:hover,
.gsd-btn.gsd-btn-primary:hover {
  background-color: var(--gsd-btn-secondary-bg-color-hover);
}

.gsd-btn.gsd-btn-primary:hover:not(:disabled) {
  background-color: var(--gsd-btn-secondary-bg-color-hover);
}

.content-block.home {
  margin-top: 32px;
}