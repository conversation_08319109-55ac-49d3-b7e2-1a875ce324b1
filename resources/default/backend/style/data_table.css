table.dataTable.default-data-table {
  width: 100% !important;
  border-spacing: 0;
  border: 1px solid #e6e6e666;
  border-radius: 3px 3px 0 0;
}

table.dataTable.default-data-table a {
  text-decoration: none;
}

table.dataTable.default-data-table tr td {
  padding: 3px;
  border-bottom: 1px solid #e6e6e666;
  vertical-align: middle;
}

table.dataTable.default-data-table thead tr th {
  border-top: 1px solid #e6e6e666;
  border-bottom: 1px solid #e6e6e666;
}

table.dataTable.default-data-table thead tr th {
  font-size: 11px;
  padding: 10px 5px;
  font-weight: bold;
  vertical-align: top;
  background-color: #F5F8FD;
  color: #3A505B;
  text-transform: uppercase;
}

table.dataTable.default-data-table tbody tr {
  /* standaard/minimale hoogte van 40px, zodat tabellen zonder Actions kolom dezelfde hoogte hebben als met de Actions kolom */
  height: 40px;
  vertical-align: top;
}

table.dataTable.default-data-table tbody tr:hover {
  background-color: #F5F8FD;
  /* standaard/minimale hoogte van 40px, zodat tabellen zonder Actions kolom dezelfde hoogte hebben als met de Actions kolom */
  height: 40px;
}

table.dataTable.default-data-table.no-footer {
  border: none;
}

.dataTables_wrapper .dt-buttons {
  margin-left: 15px;
}

.dataTables_wrapper .dt-buttons button {
  font-family: 'Open Sans', sans-serif;
  background-color: var(--gsd-btn-bg-color);
  color: var(--gsd-btn-text-color);
  border: 1px solid transparent;
  background-image: none;
  padding: 4px 10px;
  text-align: center;
  text-decoration: none;
  border-radius: 3px;
  font-size: inherit;
  line-height: inherit;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.dataTables_wrapper .dt-buttons button:hover:not(:disabled) {
  background: var(--gsd-btn-bg-color-hover);
  text-decoration: none;
  border-color: transparent;
}

.dataTables_paginate {
  margin-bottom: 5px;
}

.paginate_button.current {
  background: none !important;
  background-color: #f6f6f6 !important;
  border: 0 !important;
  color: #02207A !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  background-color: var(--gsd-btn-bg-color);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  font-weight: normal;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--gsd-btn-bg-color-hover) !important;
  border-color: transparent !important;
  color: var(--gsd-btn-text-color) !important;
}


/* Select checkbox for row selection */
table.dataTable.compact>tbody>tr>td.select-checkbox:before, table.dataTable.compact>tbody>tr>th.select-checkbox:before {
  margin-top: 0 !important;
}
table.dataTable.compact>tbody>tr.selected>td.select-checkbox:after, table.dataTable.compact>tbody>tr.selected>th.select-checkbox:after {
  margin-top: -24px !important;
}

table.dataTable tbody tr.selected > * {
  box-shadow: inherit !important;
  background-color: #B0BED9;
}

div.dataTables_processing {
  background: white;
  box-shadow: 1px 4px 10px 0 rgb(0 0 0 / 53%), 0 3px 2px 0 rgb(0 0 0 / 15%);
  border-radius: 3px;
  padding: 10px 10px 0 10px !important;
  width: 250px;
  max-width: 100%;
  z-index: 10000;
}