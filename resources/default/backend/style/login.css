.body_login {
  background: #f9f9f9 url("../images/background_login.jpg") center no-repeat;
  background-size: cover;
}

#loginscrn {
  margin: 0 auto 20px auto;
  border-radius: 0 0 10px 10px;
  width: 400px;
  max-width: 100%;
  border: 1px solid #D3D3D3;
  border-top: 0;
  font-size: 12px;
}

#loginscrn > div {
  padding: 30px 50px;
  background: white;
  border-radius: 0 0 8px 8px;
}

#loginscrn > h2 {
  background-color: #F8F8F8;
  display: block;
  margin: 0;
  padding: 20px;
}

#loginscrn input[type=submit], #loginscrn input[type=button] {
  height: calc(1.5em + .5rem + 6px);
  margin-top: 10px;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid #2E7ABA;
  background-color: #2E7ABA;
  color: white;
  box-shadow: 0 0 2px 0 #4a4a4a;
  text-transform: uppercase;
}
#loginscrn input[type=submit]:hover, #loginscrn input[type=button]:hover {
  background-color: #202c58;
  border-color: #202c58;
  box-shadow: 0 3px 6px rgba(0,0,0,.2), 0 3px 6px rgba(0,0,0,.26);
  transition: 0.3s ease-out;
}
#loginscrn input[type=text], #loginscrn input[type=password] {
  box-sizing:border-box;
  padding: 6px 12px;
  height: calc(1.5em + .5rem + 6px);
  font-size: 14px;
  margin-top: 10px;
  background-color: #f8f8f8;
}

#login_header {
  width: 400px;
  max-width: 100%;
  height: 125px;
  margin: 0 auto;
  background: white url(../images/gsd_logo_rand.svg) no-repeat 85px;
  background-size: 210px;

  border-left: 1px solid #D3D3D3;
  border-right: 1px solid #D3D3D3;
}

@media (max-width: 768px) {

  #loginscrn > div {
    padding: 40px 15px;
  }

  #loginscrn input[type="text"],#loginscrn input[type="password"] {
    max-width: 100%;
    width: calc(100% - 8px);
  }

}