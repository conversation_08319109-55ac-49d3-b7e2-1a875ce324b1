.gsd-svg-icon svg {
  color: #A1A5B7;
  height: 1.15rem;
  width: 1.15rem;
  display: inline-block;
}

.icon-color-red .gsd-svg-icon svg {
  color: #f44336;
}

.icon-color-orange .gsd-svg-icon svg {
  color: #ff7811;
}

.icon-color-green .gsd-svg-icon svg {
  color: #A8BA2A;
}

.icon-color-off .gsd-svg-icon svg {
  color: #bbbbbb;
}

.gsd-svg-icon-a.icon-size-inline-form,
.icon-size-inline-form .gsd-svg-icon-a {
  height: 1.6em;
  width: 1.6em;
  vertical-align: top;
}

.icon-size-1 .gsd-svg-icon svg {
  height: 1rem;
  width: 1rem;
}

.icon-size-2 .gsd-svg-icon svg {
  height: 0.9rem;
  width: 0.9rem;
}

.gsd-svg-icon-a .gsd-svg-icon svg {
  display: block;
}

.gsd-svg-icon.gsd-phone svg {
  height: 1.05rem;
  width: 1.05rem;
}

.gsd-svg-icon.gsd-checkbox svg,
.gsd-svg-icon.gsd-checkbox-off svg,
.gsd-svg-icon.gsd-cross svg {
  height: 1.4rem;
  width: 1.4rem;
}
.gsd-svg-icon.gsd-checkbox-off svg {
  color: #a1a5b74f;
}

.gsd-svg-icon.gsd-remind svg {
  height: 1.35rem;
  width: 1.35rem;
}

.gsd-svg-icon.gsd-add svg {
  height: 1.3rem;
  width: 1.3rem;
}

.gsd-svg-icon.gsd-plus svg,
.gsd-svg-icon.gsd-min svg {
  height: 1.3rem;
  width: 1.3rem;
}

.gsd-svg-icon.gsd-help svg,
.gsd-svg-icon.gsd-alert svg {
  height: 1.2rem;
  width: 1.2rem;
}

.gsd-svg-icon.gsd-mail svg {
  height: 1.4rem;
  width: 1.4rem;
}

.gsd-svg-icon.gsd-login svg,
.gsd-svg-icon.gsd-download svg,
.gsd-svg-icon.gsd-upload svg,
.gsd-svg-icon.gsd-lock svg {
  height: 1.5rem;
  width: 1.5rem;
}

.gsd-svg-icon-a {
  font-weight: 500;
  line-height: 1.5;
  color: #181C32;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  font-size: 1.1rem;
  border-radius: 0.475rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: #F5F8FD;
  height: calc(1.5em + 0.2rem + 2px);
  width: calc(1.5em + 0.2rem + 2px);
}

.gsd-svg-icon-a:hover {
  background-color: #EFF4F9;
}

.gsd-svg-icon-a:hover .gsd-svg-icon svg {
  color: var(--gsd-primary-color);
}

.gsd-svg-icon.gsd-alert svg {
  color: #f44336;
}

.icon-color-off.gsd-svg-icon-a:hover .gsd-svg-icon svg {
  color: #bbbbbb;
}

.gsd-svg-icon-width-2 {
  width: 77px
}

.gsd-svg-icon-width-3 {
  width: 112px
}

.gsd-svg-icon-width-4 {
  width: 146px
}
