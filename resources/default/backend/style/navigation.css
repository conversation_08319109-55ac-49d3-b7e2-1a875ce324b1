#headmenu {
  background: none;
  height: 39px;
  border-bottom: 3px solid var(--gsd-primary-color);
  border-top: 1px solid #f0f0f0;
}

/* Reset */
#navmenu-h-container ul, #navmenu-h-container li {
  margin: 0;
  padding: 0;
  font-size: 100%;
}

#navmenu-h-container {
  float: left;
}

ul#navmenu-h {
  margin: 0;
  padding: 0;
  list-style: none;
  position: relative;
  z-index: 1000;
  width: 100%;
}

ul#navmenu-h ul {
  width: 230px; /* Sub Menu Width */
  margin: 0;
  list-style: none;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
}

ul#navmenu-h ul ul, ul#navmenu-h ul ul ul {
  top: 0;
  left: 100%;
}

ul#navmenu-h li {
  float: left;
  display: inline;
  position: relative;
}

ul#navmenu-h ul li {
  width: 100%;
  display: block;
}

/* Root Menu */
ul#navmenu-h a {
  border-top: 0;
  padding: 7px 15px 6px 15px;
  float: left;
  display: block;
  color: #4E4E4E;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  height: 1%;
  background: none;
}

ul#navmenu-h > li.sub > a::after {
  content: "\f0d7";
  font-family: 'FontAwesome';
  margin-left: 5px;
  font-size: 13px;
  color: #d4d4d4;
}

ul#navmenu-h .sub.iehover > a {
  background-color: #f8f8f8;
}

/* Root Menu Hover Persistence */
ul#navmenu-h a:hover {
  background: #F0F0F0;
}

/* 2nd Menu */
ul#navmenu-h li:hover li a, ul#navmenu-h li.iehover li a {
  float: none;
  padding: 8px 20px;
  border-right: 0;
  background: #f8f8f8;
}

/* 2nd Menu Hover Persistence */
ul#navmenu-h li:hover li a:hover, ul#navmenu-h li:hover li:hover a, ul#navmenu-h li.iehover li a:hover, ul#navmenu-h li.iehover li.iehover a {
  background: #F0F0F0;
}

/* 3rd Menu */
ul#navmenu-h li:hover li:hover li a, ul#navmenu-h li.iehover li.iehover li a {
  background: #f8f8f8;
}

/* 3rd Menu Hover Persistence */
ul#navmenu-h li:hover li:hover li a:hover, ul#navmenu-h li:hover li:hover li:hover a, ul#navmenu-h li.iehover li.iehover li a:hover, ul#navmenu-h li.iehover li.iehover li.iehover a {
  background: #F0F0F0;
}

/* 4th Menu */
ul#navmenu-h li:hover li:hover li:hover li a, ul#navmenu-h li.iehover li.iehover li.iehover li a {
  background: #f8f8f8;
}

/* 4th Menu Hover */
ul#navmenu-h li:hover li:hover li:hover li a:hover, ul#navmenu-h li.iehover li.iehover li.iehover li a:hover {
  background: #999;
}

/* Hover Function - Do Not Move */
ul#navmenu-h li:hover ul ul, ul#navmenu-h li:hover ul ul ul, ul#navmenu-h li.iehover ul ul, ul#navmenu-h li.iehover ul ul ul {
  display: none;
}

ul#navmenu-h li:hover ul, ul#navmenu-h ul li:hover ul, ul#navmenu-h ul ul li:hover ul, ul#navmenu-h li.iehover ul, ul#navmenu-h ul li.iehover ul, ul#navmenu-h ul ul li.iehover ul {
  display: block;
}

#navmenu-h a.a_active {
  background: #F0F0F0;
  color: var(--gsd-primary-color);
}

#tabnav {
  margin: 5px 0;
  padding-left: 0;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 5px 5px 0 0;
  display: flex;
}

#tabnav li {
  margin: 0;
  padding: 0;
  display: inline-block;
  list-style-type: none;
  background: #f0f0f080;
}

#tabnav a {
  font-size: 13px;
  font-weight: 600;
  padding: 6px 15px;
  margin-right: 0;
  text-decoration: none;
  color: #4E4E4E;
  display: inline-block;
}

#tabnav li:first-child, #tabnav li:first-child a {
  border-top-left-radius: 5px;
}

#tabnav li:last-child, #tabnav li:last-child a {
  border-top-right-radius: 5px;
}

#tabnav a.here0,
#tabnav a.here1,
#tabnav a.here2,
#tabnav li.active ,
#tabnav a.nu,
#tabnav a.active,
#tabnav a:hover,
.tabslink.active {
  background: #F0F0F0;
}
#tabnav a.active {
  color: var(--gsd-primary-color);
}

#tabnav #tabnav a:visited.here {
  background: var(--gsd-primary-color);
}

#tabnav a:link.disabled, #tabnav a:visited.disabled {
  color: #F8F8F8;
}

#tabnav_wiz span.here0,
#tabnav_wiz span.here1,
#tabnav_wiz span.here2,
#tabnav_wiz span.active {
  color: #000;
  font-weight: bold;
}



#leftmenu {
  list-style: none;
  margin: 0 18px auto 0;
  padding: 0;
  width: 180px;
}

ul#leftmenu a {
  padding: 6px 10px 6px 20px;
  display: block;
  font-size: 13px;
  font-weight: 600;
  text-decoration: none;
  color: #696969;
}

ul#leftmenu a:hover {
  color: black;
}