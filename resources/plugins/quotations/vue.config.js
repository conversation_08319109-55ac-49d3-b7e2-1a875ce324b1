const path = require("path");

const devPort = 8081;
const PROJECT = 'default';
const PLUGIN = 'quotations';
// const DIR_GSDFW = '../../gsdfw/';
const OUTPUTDIR = process.env.NODE_ENV === "production"?"../../../plugins/"+PLUGIN+"/dist-spa/":"dist/";

module.exports = {
  devServer: {
    hot: true,
    writeToDisk: true,
    liveReload: false,
    disableHostCheck: true,
    sockPort: devPort,
    port: devPort,
    headers: {"Access-Control-Allow-Origin": "*"}
  },
  publicPath:
    process.env.NODE_ENV === "production"
      ? OUTPUTDIR
      : `http://localhost:${devPort}/`,
  configureWebpack: {
    output: {
      filename: process.env.NODE_ENV === "production"?"quotations.min.js":"quotations.js",
      hotUpdateChunkFilename: "hot/hot-update.js",
      hotUpdateMainFilename: "hot/hot-update.json"
    },
    optimization: {
      splitChunks: false
    },
    // resolve: {
    //   alias: {
    //     vue: 'vue/dist/vue.js'
    //   }
    // }
  },
  filenameHashing: true,
  css: {
    extract: {
      filename: process.env.NODE_ENV === "production"?"quotations.min.css":"quotations.css",
    }
  },
  outputDir: path.resolve(__dirname, OUTPUTDIR),
  // assetsDir: "../../static/SPA"

};