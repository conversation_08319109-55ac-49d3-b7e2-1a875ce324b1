<template>
  <div>
    <div v-show="errors.length > 0">
      <br/>
      <div class="alert alert-danger">
        <ul>
          <li v-for="error in errors" :key="error">{{ error }}</li>
        </ul>
      </div>
    </div>
    <div v-show="system_messages.length > 0">
      <br/>
      <div class="alert alert-success">
        <ul>
          <li v-for="system_message in system_messages" :key="system_message">{{ system_message }}</li>
        </ul>
      </div>
    </div>
    <div class="" v-if="!template">
      <table class="prod default_table default_table_valign">
        <tr class="dataTableHeadingRow">
          <td>Klantgegevens</td>
          <td></td>
        </tr>
        <tr class="dataTableRow trhover">
          <td>Naam</td>
          <td>{{ organisation.name }}</td>
        </tr>
        <tr class="dataTableRow trhover">
          <td>Telefoon</td>
          <td>{{ organisation.phone }}</td>
        </tr>
        <tr class="dataTableRow trhover">
          <td>Land</td>
          <td>{{ organisation.country }}</td>
        </tr>
        <tr class="dataTableRow trhover">
          <td>Adres</td>
          <td>{{ organisation.address }} {{ organisation.number }} - {{ organisation.zip }} - {{ organisation.city }}
          </td>
        </tr>
      </table>
    </div>

    <div class="" v-if="edit && !template">

      <br>
      <hr>
      <br>
      <div class="" style="display: flex; gap: 1em;">
        <div class="gsd-btn" @click="this.showStatus=false">Bewerk offerte</div>
        <div class="gsd-btn" @click="this.showStatus=true; this.system_messages = []">Beheer offerte status</div>
      </div>
    </div>

    <br>
    <hr>
    <br>
    <div v-if="!this.showStatus">
      <h2 v-if="template">Offerte template aanmaken / bewerken</h2>
      <table class="prod default_table default_table_valign">
        <tr class="dataTableHeadingRow">
          <td>Item</td>
          <td>Waarde</td>
        </tr>
        <tr class="dataTableRow" v-show="!edit && !template">
          <td><label for="quotationType">Template</label></td>
          <td>
            <select name="quotationType" @change="setQuotationTemplateId($event)" style="width: 300px;">
              <option value="">Selecteer template...</option>
              <option value="new" v-if="template">Nieuwe template aanmaken...</option>
              <option :value="quotationTemplate.id" v-for="(quotationTemplate, index) in quotationTemplates"
                      :key="index">{{ quotationTemplate.subject }}
              </option>
            </select>
            <a @click="editTemplateById(quotationTemplateId)" v-if="quotationTemplateId" class="gsd-btn"
               style="margin-left: 1em">Bewerk deze template</a>
          </td>
        </tr>
        <tr class="dataTableRow">
          <td><label for="offerteTitel">Titel </label></td>
          <td><input type="text" v-model="quotation.subject" name="offerteTitel"></td>
        </tr>
        <tr class="dataTableRow">
          <td><label for="offerteTekst">Tekst</label></td>
          <td><textarea v-model="quotation.description" name="offerteTekst"></textarea></td>
        </tr>
        <tr class="dataTableRow" v-show="!template && quotation.id != null">
          <td><label for="version">Offerte versie</label></td>
          <td>
            <select name="version" v-model="quotation.version" style="width: 300px;">
              <option v-for="(n , index) in parseInt(quotation.version_latest)" :key="index">{{ n }}</option>
              <option :value="getNextQuotationVersion(quotation.version_latest)">
                {{ getNextQuotationVersion(quotation.version_latest) }} (ophogen)
              </option>
            </select>
          </td>
        </tr>
        <tr class="dataTableRow" v-show="Object.keys(pdftemplates).length >= 1">
          <td>
            <label for="pdftemplates">PDF briefpapier</label>
          </td>
          <td>
            <select name="pdftemplates" v-model="quotation.pdf_template" style="width: 300px;">
              <option v-for="(pdftemplate, index) in pdftemplates" :value="index" :key="index">
                {{ pdftemplate.nice_name }}
              </option>
            </select>
          </td>
        </tr>
        <tr class="dataTableRow" v-show="Object.keys(contract_periods).length >= 1">
          <td>
            <label for="contract_periods">Contract periode</label>
          </td>
          <td>
            <select name="contract_periods" v-model="quotation.contract_period" style="width: 300px;">
              <option v-for="(contract_period, index) in contract_periods" :value="contract_period" :key="index">
                {{ index }}
              </option>
            </select>
          </td>
        </tr>
        <tr class="dataTableRow" v-show="this.sales_chance.length >= 1">
          <td>
            <label for="contract_periods">Verkoop kans</label>
          </td>
          <td>
            <select name="sales_chance" v-model="quotation.sales_chance" style="width: 300px;">
              <option value="0">Onbekend</option>
              <option v-for="(sales_chance, index) in sales_chance" :value="sales_chance" :key="index">
                {{ sales_chance }}
              </option>
            </select>
          </td>
        </tr>
      </table>
      <br>
      <hr>
      <h3>Producten en diensten</h3>
      <table class="prod default_table default_table_valign">
        <tr class="dataTableHeadingRow">
          <td>Product</td>
          <td>Opmerking</td>
          <td>Aantal</td>
          <td>BTW %</td>
          <td>Prijs (ex. btw)</td>
          <td>Korting %</td>
          <td>Korting (ex. btw)</td>
          <td>Totaal (ex. btw)</td>
          <td>Acties</td>
        </tr>
        <tr class="dataTableRow trhover quotationsRow" v-for="(quotationLine, index) in quotationLines" :key="index">
          <td>
            <vSelect style="width:300px"
                     label="code"
                     :options="products"
                     v-model="quotationLine.product"
                     @option:selected="checkFree(index)"

            ></vSelect>
          </td>
          <td>
            <input type="text" v-model="quotationLine.description">
            <div
                v-if="quotationLine.product.options_formatted != null && quotationLine.product.options_formatted.length > 0">
              <select name="productoption" v-model="quotationLine.product_option" style="width: 100%">
                <option value="">Selecteer optie...</option>
                <option v-for="option in quotationLine.product.options_formatted[0].option_items" :value="option.name"
                        :key="option.name">{{ option.name }}
                </option>
              </select>
            </div>
          </td>
          <td><input style="width: 10ch" type="number" v-model="quotationLine.size" min="1"></td>
          <td>
            <select name="vatgroup" v-model="quotationLine.vatgroup">
              <option v-for="(vatgroup,index) in vatgroups" :value="index" :key="index">{{ vatgroup }} %</option>
            </select>
          </td>
          <td><input style="width: 10ch" type="number" step='0.01' v-model="quotationLine.price_ex_vat"
                     :disabled="quotationLine.type != 'free' && overrule_catalog_prices == false">
          </td>
          <td><input style="width: 10ch" type="number" v-model="quotationLine.discounted_on_price_percentage"
                     @change="CalculatePriceByDiscountPercentage(index)"></td>
          <td><input style="width: 10ch" type="number" step='0.01' v-model="quotationLine.discounted_on_price_ex_vat"
                     @change="CalculatePriceByDiscountAmount(index)"></td>
          <td><input style="width: 13ch" type="number" step='0.01' disabled
                     :value="((quotationLine.price_ex_vat * quotationLine.size) - quotationLine.discounted_on_price_ex_vat).toFixed(2)">
          </td>
          <td @click="deleteLine(index)" v-html="IconHelper.ICON_REMOVE"></td>
        </tr>
      </table>
      <br>
      <button class="gsd-btn" @click="addEmptyQuotationLine()">Nieuwe offerteregel toevoegen</button>
      <br>
      <br>
      <hr>
      <h3>Bijkomende kosten </h3>
      <table class="prod default_table default_table_valign">
        <tr class="dataTableHeadingRow">
          <td>Omschrijving</td>
          <td>Prijs (ex. btw)</td>
          <td>BTW %</td>
          <td>Acties</td>
        </tr>
        <tr class="dataTableRow trhover quotationsRow" v-for="(quotationExtra, index) in quotationExtras" :key="index">
          <td><input type="text" v-model="quotationExtra.description"
                     placeholder="bijv. voorrijdkosten, materiaalkosten, etc.."></td>
          <td><input type="number" step='0.01' v-model="quotationExtra.price_ex"></td>
          <td>
            <select name="vatgroup" v-model="quotationExtra.vat_percentage">
              <option v-for="(vatgroup,index) in vatgroups" :value="vatgroup" :key="index">{{ vatgroup }} %</option>
            </select>
          </td>

          <td @click="deleteExtra(index)" v-html="IconHelper.ICON_REMOVE"></td>
        </tr>
      </table>
      <br>
      <button class="gsd-btn" @click="addEmptyExtraLine()">Extra bijkomende kosten toevoegen</button>
      <br/>

      <br>
      <hr>
      <br>

      <div class="" v-if="template">
        <button @click="saveTemplate" class="gsd-btn" style="margin-right: 1em">Template Opslaan</button>
        <button @click="deleteTemplate" class="gsd-btn" style="margin-right: 1em">Template Verwijderen</button>
        <button @click="saveTemplateGoBack" class="gsd-btn gsd-btn-primary" style="margin-right: 1em">Template Opslaan
          en terug naar Offerte
        </button>
      </div>
      <div class="" v-if="!template">
        <button class="gsd-btn gsd-btn-primary" @click="saveQuotation('send')">Opslaan en naar verzenden</button>
        <button class="gsd-btn" @click="saveQuotation('save')" style="margin-right: 1em">Opslaan</button>
        <button class="gsd-btn" @click="saveQuotation('list')" style="margin-right: 1em">Opslaan en naar lijst</button>
        <button class="gsd-btn" @click="this.template=true; this.quotation.id='new'" style="margin-right: 1em">Template
          aanmaken op basis van deze offerte
        </button>
      </div>
    </div>
    <div v-if="this.showStatus">
      <table class="default_table" style="width: auto;">
        <tr class="dataTableHeadingRow">
          <td>Datum</td>
          <td>Versie</td>
          <td>Door</td>
          <td>Opmerking</td>
          <td>Status</td>
          <td>Klant Email</td>
          <td>PDF</td>
        </tr>
        <tr class="dataTableRow trhover" v-for="quotationStatus in quotationStatuses" :key="quotationStatus">
          <td>{{ quotationStatus.insertTS }}</td>
          <td>{{ quotationStatus.version }}</td>
          <td>{{ quotationStatus.user }}</td>
          <td>{{ quotationStatus.message }}</td>
          <td>{{ quotationStatus.quotation_status }}</td>
          <td>
            <div v-if="quotationStatus.email.is_email">
              {{ quotationStatus.email.subject }}
              <a href="javascript: void(0);" class="qtipa fa_info fa fa-info-circle"
                 :title="quotationStatus.email.message"></a>
            </div>
          </td>
          <td><a :href="quotationStatus.PDF" target="_blank">
            <button class="gsd-btn">PDF</button>
          </a></td>
        </tr>
        <tr style="height: 2em">
          <td></td>
        </tr>
        <tr>
          <td>Nieuwe status</td>
          <td></td>
          <td><!--$_SESSION['userObject']->getNaam();--></td>
          <td><textarea v-model="this.message_remark"/></td>
          <td>
            <select v-model="this.newstatus">
               <option v-for="(statusoption, index) in this.statusoptions" :value="index" v-bind:key="index">{{statusoption}}</option>
            </select>
          </td>
          <td></td>
          <td>
            <div class="gsd-btn" @click="saveStatus()"> Status opslaan</div>
          </td>
        </tr>
      </table>
    </div>
  </div>

  <!--  <vSelect>test</vSelect>-->
</template>


<script>
import IconHelper from "../helpers/IconHelper";
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';

export default {
  name: "Quotation",
  components: {
    // vSelect,

  },
  data() {
    return {
      IconHelper,
      organisation: '',
      organisation_user_id: '',
      template: false, //editing the template??
      edit: false, //editing an existing (allready saved) quotation
      quotationTemplates: '',
      quotationTemplateId: '',
      vatgroups: [],
      products: '',
      quotation: {
        id: '',
        quotation_type: '',
        titel: '',
        description: '',
        pdf_template: 'veiligesportvloer',
        template_id: '',
        version_latest: '1',
        sales_chance: 0,
      },
      quotationLines: [],
      quotationExtras: [],
      quotationStatuses: [],
      toDeleteIds: {
        quotation_lines: [],
        quotation_extras: [],
      },
      pdftemplates: [],
      contract_periods: [],
      sales_chance: [],
      newstatus: '',
      message_remark: '',
      errors: [],
      system_messages: [],
      showStatus: false,
      statusoptions: [],
      overrule_catalog_prices: false,
      editor: ClassicEditor,
      editorData: '<p>Content of the editor.</p>',
      editorConfig: {
        // The configuration of the editor.
      }
    };
  },
  watch: {
    'quotation.template_id'(template_id) {
      if (typeof parseInt(template_id) === 'number' && parseInt(template_id) > 0) {
        //only run this when we need to edit an existing template
        this.initiateByTemplate(template_id);
      }
    },
  },
  mounted() {
    this.initiate();
    this.addEmptyQuotationLine();
  },
  methods: {
    initiate: async function () {
      const queryString = window.location.search;
      const urlParams = new URLSearchParams(queryString);
      const body = {};
      body.org_id = urlParams.get('org_id'); //get parameter needed for input

      if (urlParams.get('quotation_id')) {
        body.quotation_id = urlParams.get('quotation_id');
      }

      // toevoegen is een get parameter om een bestaande offerte aan te passen -> ook verwerken in de intitiate

      await fetch('?action=InitiateVueAjax', { //link to backend
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body)
      })
          .then(response => response.json())
          .then(data => {
            if (data.error == true) { // Company id is not found
              window.location.href = data.redirect_url;
            }
            this.organisation = data.organisation;
            this.products = data.products;
            this.quotationTemplates = data.quotationTemplates;
            this.vatgroups = data.vatgroups;
            this.edit = data.edit;
            this.pdftemplates = data.pdftemplates;
            this.contract_periods = data.contract_periods;
            this.sales_chance = data.sales_chance;
            this.quotation = data.quotation;
            this.overrule_catalog_prices = data.overrule_catalog_prices;

            if (data.quotation.id) { // only when editing a quotation
              this.quotationLines = data.quotation_lines;
              this.quotationExtras = data.quotation_added_costs;
              this.quotationStatuses = data.quotation_statuses;
              this.statusoptions = data.statusoptions;

              this.newstatus = data.quotation.status;

              var $i = 0;
              for (let quotationline in this.quotationLines) {
                console.log(quotationline);
                this.quotationLines[quotationline].dummy = 'free';
                this.CalculatePriceByDiscountAmount($i);
                $i++;
              }
            }
          });

    },
    addEmptyQuotationLine: function () {
      let quotationLine = {
        "id": "new",
        "product_id": "unknown",
        "product": {
          code: "Nieuw",
        },
        "product_option": "",
        "description": "",
        "size": 1,
        "price_ex_vat": 0,
        "discounted_on_price_ex_vat": 0,
        "discounted_on_price_percentage": 0,
        "type": '',
        "changed": true,
      }
      this.quotationLines.push(quotationLine);

    },
    addEmptyExtraLine: function () {
      let quotationExtraLine = {
        "id": "new",
        "description": "",
        "price_ex": 0,
        "vat_percentage": 21,
        "type": "free",
        "changed": true,
      }
      this.quotationExtras.push(quotationExtraLine);
    },
    checkFree(index) {
      if (this.quotationLines[index].product.code == "free") {
        this.quotationLines[index].type = 'free';
        this.quotationLines[index].product_id = 'free';
        this.quotationLines[index].price_ex_vat = 0;
        this.quotationLines[index].vatgroup = 2;
      }
      else {
        this.quotationLines[index].type = '';
        this.quotationLines[index].vatgroup = this.quotationLines[index].product.vatgroup;
      }
    },
    getNextQuotationVersion(version) {
      version++;
      return version;
    },
    async initiateByTemplate(templateId) {
      await fetch('?action=initiateByTemplateAjax', { //link to backend
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateId)
      })
          .then(response => response.json())
          .then(data => {
            this.quotation = data.quotation;
            this.quotationLines = data.quotation_lines;
            this.quotationExtras = data.quotation_added_costs;
          });

    },
    saveTemplate() {
      let vueApp = this;
      this.errors = [];
      this.system_messages = [];

      if (this.quotation.subject == null || this.quotation.subject.length <= 3) {
        this.errors.push("Geef een geldige titel op van minimaal 3 tekens");
      }

      this.quotationExtras.forEach(function (extra) {
        if (extra.description.length <= 3) {
          vueApp.errors.push("Geef een geldige omschrijving voor de bijkomende kosten");
        }
      })

      if (this.errors.length > 0) {
        //errors? than do not save...
        return;
      }

      const body = {
        quotation: this.quotation,
        quotation_lines: this.quotationLines,
        quotation_extras: this.quotationExtras,
        template_id: this.quotationTemplateId,
      }
      fetch('?action=saveTemplateAjax', { //link to backend
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body)
      }).then(response => response.json())
          .then(data => {
            this.system_messages.push("Offerte template succesvol opgeslagen");
          });
    },
    setQuotationTemplateId(event) {
      this.quotation.template_id = event.target.value;
      this.quotationTemplateId = event.target.value;
    },
    deleteLine(id) {
      const quotationLine = this.quotationLines[id];
      if (quotationLine.id != 'new') {
        this.toDeleteIds.quotation_lines.push(quotationLine.id);
      }
      this.quotationLines.splice(id, 1);
    },
    deleteExtra(id) {
      const quotationExtra = this.quotationExtras[id];
      if (quotationExtra.id != 'new') {
        this.toDeleteIds.quotation_extras.push(quotationExtra.id);
      }
      this.quotationExtras.splice(id, 1);
    },
    saveQuotation(redirect) {
      this.validateQuotation();

      if (this.errors.length > 0) { // no submit / save if errors
        return;
      }

      const body = {
        quotation: this.quotation,
        quotation_lines: this.quotationLines,
        quotation_extras: this.quotationExtras,
        organisation_user_id: this.organisation_user_id,
        organisation_id: this.organisation.id,
        to_delete_ids: this.toDeleteIds,
        redirect: redirect,
      };

      fetch('?action=saveQuotationAjax', { //link to backend
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body)
      }).then(response => response.json())
          .then(data => {
            window.location.replace(data); // redirect to 'M_QUOTATIONS_SEND'
          });

    },
    validateQuotation() {
      const errors = [];
      if (this.quotation.titel == '') {
        errors.push("Offerte titel ontbreekt");
      }

      for (let quotationLine in this.quotationLines) {
        if (this.quotationLines[quotationLine].product.id == 'new') {
          errors.push("Offerteregel " + (parseInt(quotationLine) + 1) + " bevat geen product");
        }
        if (this.quotationLines[quotationLine].size == 0) {
          errors.push("Offerteregel " + (parseInt(quotationLine) + 1) + " aantal is 0");
        }
        if (this.quotationLines[quotationLine].vatgroup == null) {
          errors.push("Offerteregel " + (parseInt(quotationLine) + 1) + " BTW groep is onbekend");
        }
        if (this.quotationLines[quotationLine].price_ex_vat == null) {
          errors.push("Offerteregel " + (parseInt(quotationLine) + 1) + " prijs is leeg");
        }
      }

      this.errors = errors;

    },
    saveStatus() {
      const body = {
        quotation: this.quotation,
        new_status: this.newstatus,
        message_remark: this.message_remark,

      };

      fetch('?action=saveQuotationMessageAjax', { //link to backend
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body)
      }).then(response => response.json())
          .then(data => {
            if (data == 'succes') {
              location.reload(); // Reload page
            }
          });

    },
    CalculatePriceByDiscountPercentage(index) {
      this.quotationLines[index].discounted_on_price_ex_vat = (((this.quotationLines[index].discounted_on_price_percentage / 100) * this.quotationLines[index].price_ex_vat) * this.quotationLines[index].size).toFixed(2);
    },
    CalculatePriceByDiscountAmount(index) {
      this.quotationLines[index].discounted_on_price_percentage = ((this.quotationLines[index].discounted_on_price_ex_vat / (this.quotationLines[index].size * this.quotationLines[index].price_ex_vat)) * 100).toFixed(2);
    },
    editTemplateById(templateId) {
      // this.initiateByTemplate(templateId);
      console.log(templateId);
      this.template = true;
    },
    saveTemplateGoBack() {
      this.saveTemplate();
      // this.initiateByTemplate(templateId);
      this.template = false;
    },
    deleteTemplate() {
      this.system_messages = [];

      const body = {
        quotationTemplateId: this.quotationTemplateId,
      };

      fetch('?action=deleteQuotationTemplateAjax', { //link to backend
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body)
      }).then(response => response.json())
          .then(data => {
            if (data) {
              this.template = false;
              this.quotationTemplateId = "";
              this.quotationTemplates = data.quotationTemplates;
              this.system_messages.push("Template succesvol verwijderd");
            }
          });
    },

  },


}

</script>

<style scoped>
.quotationsRow > * > * {
  height: 44px;
}
</style>


