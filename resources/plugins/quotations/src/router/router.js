import {createRouter, createWebHashHistory} from "vue-router";

// import VueSweetalert2 from 'vue-sweetalert2';
// import 'sweetalert2/dist/sweetalert2.min.css';
// import DevelopmentHelper from "./helpers/DevelopmentHelper";
import Quotation from "@/views/Quotation.vue";
// import vSelect from "vue-select";

const routes = [
  {
    path: '/',
    name: 'Quotation',
    component: Quotation,
    // meta: {
    //   requiresAuth: false,
    // }
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("@/views/NotFound")
  }
]


const router = createRouter({
  history: createWebHashHistory(),
  routes, // short for `routes: routes`
  linkActiveClass: "activeNav"
})

// check if loggedin
// router.beforeEach((to) => {
//   if (to.meta.requiresAuth && !window.localStorage.getItem("loggedin")) {
//     return { name: 'Login', query: {redirect: to.fullPath}}
//   }
// })

export default router;