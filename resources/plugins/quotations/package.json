{"name": "quotations", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^35.3.2", "@ckeditor/ckeditor5-vue": "^4.0.1", "core-js": "^3.6.5", "vue": "^3.0.0", "vue-router": "^4.1.5", "vue-select": "^4.0.0-beta.6", "vue-select-3": "^1.0.1", "vue-sweetalert2": "^5.0.2", "vue3-popper": "^1.4.1"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "sass": "^1.26.5", "sass-loader": "^8.0.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}