volumes:
  npm_cache:

networks:
  proxy:
    external: true

services:
  indufast-portal:
    image: gsd/node:18.18
    build:
      context: ./docker/node
      dockerfile: Dockerfile
      args:
        version: 18.18
    container_name: indufast-portal
    expose:
      - 3000
    ports:
      - "3000:3000"
    volumes:
      - .:/var/www/html
      - npm_cache:/cache
    labels:
      - traefik.enable=true
      - traefik.http.routers.indufast-portal.rule=Host(`portal-indufast.node.localhost`)
      - traefik.http.routers.indufast-portal.entrypoints=websecure
      - traefik.http.routers.indufast-portal.tls=true
      - traefik.http.services.indufast-portal.loadbalancer.server.port=3000
      - diun.enable=false
    restart: unless-stopped
    working_dir: /var/www/html/projects/indufast/resources/vueapps/portal
    command: /bin/sh -c "npm ci && npm run dev -- --host --port 3000"
    networks:
      - proxy