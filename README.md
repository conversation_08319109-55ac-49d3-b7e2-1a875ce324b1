# Indufast Portal
De Indufast Portal is een interne webapplicatie voor het automatiseren van de planning en tijdregistratie van Indufast. 

## Installatie
Start de Docker-image in de root van het project en de portal is beschikbaar op https://portal-indufast.nl.indufast.localhost/.

Je kunt daar inloggen met een lokale gebruiker of als er een Medewerker-account bestaat met je @gsd.nl e-mailadres kun 
je via Google inloggen.

### Google API
Om gebruik te kunnen maken van de Google Drive Picker moet je de portal openen op https://localhost:3000, Google OAuth 
werkt alleen op https://portal-indufast.nl.indufast.localhost/.

In LastPass vind je de json-files met credentials die je in `/projects/indufast/config` moet plaatsen. De dev-omgeving
koppelt met <PERSON>'s sait.nl-omgeving, de live-omgeving koppelt met indufast.nl.

# Planning
De planning is gekoppeld met Google Calendar. Naast de centrale Project-agenda heeft iedere medewerker een eigen 
agenda. Het inplannen van een medewerker gebeurt door het verslepen naar een project waarna het project als agenda-item
in zijn/haar Google agende geplaatst wordt.

# Tijdregistratie
Op basig van ritinformatie uit Accredis of handmatig toegevoegde regels kan de heen- en terugreis bepaald worden waarmee 
een werkdag berekend kan worden.

# Datamodel
De planning maakt gebruik van een datamodel dat de volgende models bevat:
- **IndufastProject**: Een project waar medewerkers aan gekoppeld kunnen worden.
- **IndufastCalendarEvent**: Een werkdag van een project.
- **IndufastCalendarEventEmployee**: Koppeltabel tussen events en medewerkers.
- **IndufastEmployee**: Een medewerker die aan projecten kan werken.
- **IndufastWorkday**: Een werkdag van een medewerker.
- **IndufastworkdayLine**: Ritten of handmatig toegevoegde regels met reistijden waarmee een werkdag berekend kan worden.

## Deploy
Om de portal te deployen, moet je de volgende stappen volgen:
1. Deploy altijd vanaf de master branch zonder lokale wijzigingen: `git checkout master && git pull && git status -s`.
2. Zorg dat GSDFW is bijgewerkt en je geen lokale wijzigingen hebt: `cd gsdfw && git checkout master && git pull && git status -s && cd ..`
3. Voeg een nieuwe release toe in `IndufastChangelogApiTrait`.
4. Verander het versienummer in `package.json`.
5. Commit en push de codewijzigingen.
6. Maak in git een tag aan `git tag v1.0.4`.
7. Push de tag naar de remote repository: `git push --tags`.
    * Zie https://github.com/GSDgit/indufast/tags voor de laatste versie.
8. Build een nieuwe versie van de portal met `docker compose exec -ti indufast-portal npm run build`.
9. Deploy het project zoals je gewend bent.
10. Mail de klant dat de nieuwe versie is uitgerold